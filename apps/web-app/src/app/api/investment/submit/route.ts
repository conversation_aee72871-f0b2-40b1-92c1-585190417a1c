import { NextRequest } from 'next/server';
import { logApiRouteRequest, logApiRouteResponse, generateServerTraceId } from '@yai-investor-insight/shared-fe-core/server';

export async function POST(request: NextRequest) {
  const traceId = generateServerTraceId();
  const startTime = Date.now();
  
  try {
    const body = await request.json();
    
    // 记录请求日志
    await logApiRouteRequest('POST', '/api/investment/submit', traceId, {
      bodyKeys: Object.keys(body),
      userAgent: request.headers.get('user-agent'),
      ip: request.headers.get('x-forwarded-for') || 'unknown'
    });

    // 转发请求到后端投资分析服务
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';
    const response = await fetch(`${backendUrl}/api/v1/investment/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Trace-ID': traceId,
      },
      body: JSON.stringify(body),
    });

    const duration = Date.now() - startTime;
    
    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const result = await response.json();
    
    // 记录成功响应
    await logApiRouteResponse('POST', '/api/investment/submit', traceId, response.status, duration);

    return Response.json(result, {
      headers: {
        'X-Trace-ID': traceId,
      },
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    
    // 记录错误日志
    await logApiRouteResponse('POST', '/api/investment/submit', traceId, 500, duration, error);
    
    return Response.json({ 
      error: 'Internal Server Error',
      traceId 
    }, { 
      status: 500,
      headers: {
        'X-Trace-ID': traceId
      }
    });
  }
} 