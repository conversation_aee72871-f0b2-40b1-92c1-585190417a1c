import { NextRequest } from 'next/server';
import { logApiRouteRequest, logApiRouteResponse, generateServerTraceId } from '@yai-investor-insight/shared-fe-core/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { task_id: string } }
) {
  const traceId = generateServerTraceId();
  const startTime = Date.now();
  const { task_id } = params;
  
  try {
    // 记录请求日志
    await logApiRouteRequest('GET', `/api/investment/stream/${task_id}`, traceId, {
      taskId: task_id,
      userAgent: request.headers.get('user-agent'),
      ip: request.headers.get('x-forwarded-for') || 'unknown'
    });

    // 转发SSE请求到后端投资分析服务
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000';
    const response = await fetch(`${backendUrl}/api/v1/investment/stream/${task_id}`, {
      method: 'GET',
      headers: {
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'X-Trace-ID': traceId,
      },
    });

    const duration = Date.now() - startTime;
    
    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }
    
    // 记录成功响应
    await logApiRouteResponse('GET', `/api/investment/stream/${task_id}`, traceId, response.status, duration);

    // 返回流式响应
    return new Response(response.body, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
        'X-Trace-ID': traceId,
      },
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    
    // 记录错误日志
    await logApiRouteResponse('GET', `/api/investment/stream/${task_id}`, traceId, 500, duration, error);
    
    return Response.json({ 
      error: 'Internal Server Error',
      traceId 
    }, { 
      status: 500,
      headers: {
        'X-Trace-ID': traceId
      }
    });
  }
} 