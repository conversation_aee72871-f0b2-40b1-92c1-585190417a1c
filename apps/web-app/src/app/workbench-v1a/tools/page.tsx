'use client'

import { 
  ChartBarIcon, 
  DocumentTextIcon,
  CogIcon
} from '@heroicons/react/24/outline'

export default function ToolsPage() {
  return (
    <div className="p-8">
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">分析工具</h2>
          <p className="text-gray-600">使用专业的投资分析工具进行深度研究</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            { name: '财务分析', description: '深度财务数据分析工具', icon: ChartBarIcon },
            { name: '市场研究', description: '行业和市场趋势分析', icon: DocumentTextIcon },
            { name: '风险评估', description: '投资风险量化分析', icon: CogIcon }
          ].map((tool, index) => (
            <div key={index} className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow">
              <tool.icon className="h-8 w-8 text-blue-600 mb-3" />
              <h4 className="font-medium text-gray-900 mb-2">{tool.name}</h4>
              <p className="text-sm text-gray-600 mb-4">{tool.description}</p>
              <button className="px-4 py-2 text-sm border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                启动工具
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}