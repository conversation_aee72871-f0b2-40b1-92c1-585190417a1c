'use client'

import { useRouter } from 'next/navigation'
import { IdeaCardStream } from '@yai-investor-insight/idea-feature-v1a-fe'

export default function OverviewPage() {
  const router = useRouter()

  const handleCardClick = (item: { id: string | number }) => {
    // 路由跳转到 idea detail 页面
    router.push(`/workbench-v1a/ideas/${item.id}`)
  }

  return (
    <div className="p-8">
      <IdeaCardStream 
        onCardClick={handleCardClick}
      />
    </div>
  )
}