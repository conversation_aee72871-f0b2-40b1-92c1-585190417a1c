'use client'

import { useState } from 'react'
import { BellIcon, CheckIcon, ExclamationTriangleIcon, InformationCircleIcon } from '@heroicons/react/24/outline'

export default function NotificationsPage() {
  const [notifications] = useState([
    {
      id: 1,
      type: 'info',
      title: '市场提醒',
      message: '您关注的股票 AAPL 今日涨幅超过 5%',
      timestamp: '2024-01-15 14:30',
      read: false
    },
    {
      id: 2,
      type: 'warning',
      title: '风险警示',
      message: '您的投资组合波动率较高，建议关注风险控制',
      timestamp: '2024-01-15 10:15',
      read: false
    },
    {
      id: 3,
      type: 'success',
      title: '任务完成',
      message: 'AI 投资分析报告已生成完成',
      timestamp: '2024-01-14 16:45',
      read: true
    }
  ])

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-amber-500" />
      case 'success':
        return <CheckIcon className="h-5 w-5 text-green-500" />
      default:
        return <InformationCircleIcon className="h-5 w-5 text-blue-500" />
    }
  }

  const getNotificationBgColor = (type: string, read: boolean) => {
    if (read) return 'bg-white'
    switch (type) {
      case 'warning':
        return 'bg-amber-50'
      case 'success':
        return 'bg-green-50'
      default:
        return 'bg-blue-50'
    }
  }

  const unreadCount = notifications.filter(n => !n.read).length

  return (
    <div className="h-full flex flex-col">
      {/* 页面头部 */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <BellIcon className="h-6 w-6 text-gray-600" />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-600 rounded-full">
                  {unreadCount}
                </span>
              )}
            </div>
            <h1 className="text-xl font-semibold text-gray-900">通知中心</h1>
          </div>
          <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
            全部标记为已读
          </button>
        </div>
      </div>

      {/* 通知列表 */}
      <div className="flex-1 overflow-auto">
        {notifications.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-6 hover:bg-gray-50 cursor-pointer transition-colors ${getNotificationBgColor(notification.type, notification.read)}`}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className={`text-sm font-medium ${notification.read ? 'text-gray-700' : 'text-gray-900'}`}>
                        {notification.title}
                      </h3>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">{notification.timestamp}</span>
                        {!notification.read && (
                          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                        )}
                      </div>
                    </div>
                    <p className={`mt-1 text-sm ${notification.read ? 'text-gray-500' : 'text-gray-700'}`}>
                      {notification.message}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无通知</h3>
            <p className="mt-1 text-sm text-gray-500">您的通知将在这里显示</p>
          </div>
        )}
      </div>
    </div>
  )
}