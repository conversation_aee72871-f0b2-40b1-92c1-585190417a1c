'use client'

import { useState } from 'react'
import { DocumentTextIcon, PlusIcon } from '@heroicons/react/24/outline'

export default function NotesPage() {
  const [notes] = useState([
    {
      id: 1,
      title: '投资研究笔记 #1',
      content: '今日市场分析要点...',
      createdAt: '2024-01-15',
      tags: ['市场分析', '投资策略']
    },
    {
      id: 2,
      title: '股票筛选标准',
      content: '制定股票筛选的关键指标...',
      createdAt: '2024-01-14',
      tags: ['股票筛选', '投资标准']
    }
  ])

  return (
    <div className="h-full flex flex-col">
      {/* 页面头部 */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <DocumentTextIcon className="h-6 w-6 text-gray-600" />
            <h1 className="text-xl font-semibold text-gray-900">我的笔记</h1>
          </div>
          <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <PlusIcon className="h-4 w-4 mr-2" />
            新建笔记
          </button>
        </div>
      </div>

      {/* 笔记列表 */}
      <div className="flex-1 overflow-auto p-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {notes.map((note) => (
            <div
              key={note.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer"
            >
              <h3 className="font-medium text-gray-900 mb-2">{note.title}</h3>
              <p className="text-sm text-gray-600 mb-3 line-clamp-3">{note.content}</p>
              <div className="flex items-center justify-between">
                <div className="flex flex-wrap gap-1">
                  {note.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
                <span className="text-xs text-gray-500">{note.createdAt}</span>
              </div>
            </div>
          ))}
        </div>

        {/* 空状态 */}
        {notes.length === 0 && (
          <div className="text-center py-12">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无笔记</h3>
            <p className="mt-1 text-sm text-gray-500">开始创建您的第一个投资笔记</p>
            <div className="mt-6">
              <button className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <PlusIcon className="h-4 w-4 mr-2" />
                新建笔记
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}