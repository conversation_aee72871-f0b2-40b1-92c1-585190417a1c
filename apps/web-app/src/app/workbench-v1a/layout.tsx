'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { 
  ChartBarIcon, 
  HeartIcon,
  ShareIcon,
  DocumentTextIcon,
  BellIcon,
  ChevronRightIcon,
  ChevronUpIcon,
  UserCircleIcon,
  Cog6ToothIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'
import { TopBar } from '@/components/workbench-v1a'
import { IdeaCreationModal, IdeaNavigationSection, getIdeaList, type IdeaItem } from '@yai-investor-insight/idea-feature-v1a-fe'
import { StockNavigationSection } from '@yai-investor-insight/stock-feature-v1a-fe'
import type { Idea } from '@yai-investor-insight/idea-feature-v1a-fe'

interface WorkbenchLayoutProps {
  children: React.ReactNode
}

export default function WorkbenchV1ALayout({ children }: WorkbenchLayoutProps) {
  const pathname = usePathname()
  const router = useRouter()
  const [showIdeaModal, setShowIdeaModal] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [ideas, setIdeas] = useState<IdeaItem[]>([])
  const [isLoadingIdeas, setIsLoadingIdeas] = useState(true)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)

  // 判断当前是否为 idea detail 页面
  const isIdeaDetailPage = pathname?.startsWith('/workbench-v1a/ideas/')
  
  // 获取当前活跃的tab
  const getActiveTab = () => {
    if (pathname?.includes('/overview')) return 'overview'
    if (pathname?.includes('/ideas')) return 'ideas'
    if (pathname?.includes('/favorites')) return 'favorites'
    if (pathname?.includes('/shared')) return 'shared'
    if (pathname?.includes('/notes')) return 'notes'
    if (pathname?.includes('/notifications')) return 'notifications'
    if (pathname?.includes('/stocks')) return 'stocks'
    if (pathname === '/workbench-v1a') return 'overview'
    return 'overview'
  }

  const activeTab = getActiveTab()

  // 加载 Ideas 列表
  useEffect(() => {
    const loadIdeas = async () => {
      try {
        setIsLoadingIdeas(true)
        const ideaData = await getIdeaList()
        setIdeas(ideaData.myIdeas)
      } catch (error) {
        console.error('Failed to load ideas:', error)
      } finally {
        setIsLoadingIdeas(false)
      }
    }
    
    loadIdeas()
  }, [])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    // TODO: 实现搜索逻辑
    console.log('Search query:', query)
  }

  const handleNewIdea = () => {
    setShowIdeaModal(true)
  }

  const handleIdeaSubmit = async (idea: Idea) => {
    console.log('创建的 Idea:', idea)
    
    // 关闭模态框
    setShowIdeaModal(false)
    
    // 先重新加载 Ideas 列表以显示新创建的 Idea（不影响导航显示）
    try {
      const ideaData = await getIdeaList()
      setIdeas(ideaData.myIdeas)
      console.log('重新加载 Ideas 列表成功，数量:', ideaData.myIdeas.length)
    } catch (error) {
      console.error('Failed to reload ideas:', error)
    }
    
    // 然后跳转到新创建的 idea detail 页面
    router.push(`/workbench-v1a/ideas/${idea.id}`)
  }

  const handleModalClose = () => {
    setShowIdeaModal(false)
  }

  const handleUserMenuToggle = () => {
    setIsUserMenuOpen(!isUserMenuOpen)
  }

  const handleUserMenuClose = () => {
    setIsUserMenuOpen(false)
  }

  // 点击外部区域关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isUserMenuOpen && !(event.target as Element).closest('.user-menu-container')) {
        setIsUserMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isUserMenuOpen])

  // 概览导航项
  const overviewItems = [
    { key: 'overview', label: '概览', icon: ChartBarIcon, href: '/workbench-v1a/overview' },
    { key: 'favorites', label: '收藏', icon: HeartIcon, href: '/workbench-v1a/favorites' },
    { key: 'shared', label: '分享', icon: ShareIcon, href: '/workbench-v1a/shared' },
    { key: 'notes', label: '笔记', icon: DocumentTextIcon, href: '/workbench-v1a/notes' },
    { key: 'notifications', label: '通知', icon: BellIcon, href: '/workbench-v1a/notifications' }
  ]
  
  return (
    <div className="h-screen overflow-hidden">
      <div className="h-full bg-gray-50 workbench-container flex">
        {/* 左侧导航栏 - 拉通到顶部 */}
        <div className="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col">
          {/* 上方导航内容区域 - 自适应高度 */}
          <div className="flex-1 p-6 space-y-6 overflow-auto">
            <Link href="/workbench-v1a/overview">
              <h1 className="text-xl font-bold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors">
                工作台 V1-A
              </h1>
            </Link>
            
            {/* 概览分组 - 不显示标题 */}
            <div className="space-y-1 mt-8">
              {overviewItems.map(({ key, label, icon: Icon, href }) => (
                <Link key={key} href={href}>
                  <div className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors cursor-pointer ${
                    activeTab === key
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}>
                    <Icon className="h-5 w-5" />
                    <span>{label}</span>
                  </div>
                </Link>
              ))}
            </div>

            {/* 我的Ideas分组 */}
            <IdeaNavigationSection
              currentPath={pathname || ''}
              onNewIdea={handleNewIdea}
              ideas={ideas}
              isLoading={isLoadingIdeas}
            />

            {/* 我的股票分组 */}
            <StockNavigationSection
              currentPath={pathname || ''}
            />
          </div>

          {/* 底部用户设置区域 - 固定高度 */}
          <div className="border-t border-gray-200 p-4 relative user-menu-container">
            {/* 用户设置触发区域 */}
            <div 
              className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
              onClick={handleUserMenuToggle}
            >
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">张</span>
                </div>
                <span className="text-sm font-medium text-gray-900">张三</span>
              </div>
              {isUserMenuOpen ? (
                <ChevronUpIcon className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronRightIcon className="w-4 h-4 text-gray-400" />
              )}
            </div>

            {/* 向上弹出菜单 */}
            {isUserMenuOpen && (
              <div className="absolute bottom-full left-4 right-4 mb-2 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                <div className="px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center space-x-2 text-sm text-gray-700 transition-colors">
                  <UserCircleIcon className="w-4 h-4 text-gray-500" />
                  <span>个人资料</span>
                </div>
                <div className="px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center space-x-2 text-sm text-gray-700 transition-colors">
                  <Cog6ToothIcon className="w-4 h-4 text-gray-500" />
                  <span>设置</span>
                </div>
                <div className="border-t border-gray-100 mx-2 my-1"></div>
                <div className="px-3 py-2 hover:bg-gray-50 cursor-pointer flex items-center space-x-2 text-sm text-gray-700 transition-colors">
                  <ArrowRightIcon className="w-4 h-4 text-gray-500" />
                  <span>退出登录</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 右侧内容区域 */}
        <div className="flex-1 flex flex-col">
          {/* TopBar - 只在非 ideaDetail 页面下显示 */}
          {!isIdeaDetailPage && (
            <TopBar
              onSearch={handleSearch}
              searchQuery={searchQuery}
            />
          )}

          {/* 主内容区域 */}
          <div className="flex-1 overflow-auto">
            {children}
          </div>
        </div>
      </div>
      
      {/* AI 任务模态框 - 由应用层管理 */}
      <IdeaCreationModal
        open={showIdeaModal}
        onClose={handleModalClose}
        onSubmit={handleIdeaSubmit}
      />
    </div>
  )
}