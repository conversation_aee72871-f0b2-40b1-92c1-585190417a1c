'use client'

import { use } from 'react'
import { useRouter } from 'next/navigation'
import { StockDetailPage as StockDetailComponent } from '@yai-investor-insight/stock-feature-v1a-fe'

interface StockDetailPageProps {
  params: Promise<{
    symbol: string
  }>
}

export default function StockDetailPageRoute({ params }: StockDetailPageProps) {
  const router = useRouter()
  const { symbol } = use(params)

  const handleClose = () => {
    router.push('/workbench-v1a')
  }

  return (
    <StockDetailComponent 
      symbol={symbol.toUpperCase()}
      onClose={handleClose}
    />
  )
}