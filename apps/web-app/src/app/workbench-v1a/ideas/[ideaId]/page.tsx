'use client'

import { use } from 'react'
import { useRouter } from 'next/navigation'
import { IdeaDetailEmbedded } from '@yai-investor-insight/idea-feature-v1a-fe'

interface IdeaDetailPageProps {
  params: Promise<{
    ideaId: string
  }>
}

export default function IdeaDetailPageRoute({ params }: IdeaDetailPageProps) {
  const router = useRouter()
  const { ideaId } = use(params)

  const handleBack = () => {
    router.push('/workbench-v1a')
  }

  return (
    <IdeaDetailEmbedded 
      ideaId={ideaId}
      onBack={handleBack}
    />
  )
}