'use client';

import { TestCoagentPage, type UserSession } from '@yai-investor-insight/demo-feature-fe';
import { useSession, type SessionResult } from '@yai-investor-insight/user-account-fe';

/**
 * 转换 SessionResult 到 UserSession 类型
 */
function convertSessionToUserSession(session: SessionResult | null): UserSession | undefined {
  if (!session || !session.isLoggedIn) {
    return undefined;
  }

  return {
    userId: session.userId || '',
    phone: session.phone || '',
    userName: session.userName
  };
}

/**
 * 测试 Coagent 功能页面
 * 整合到 demo-feature 中的测试页面，使用 demo-feature-fe 中的组件
 */
export default function TestCoagentFeaturePage() {
  const { session, isLoading, isLoggedIn } = useSession();

  // 转换 session 类型
  const userSession = convertSessionToUserSession(session);

  return (
    <TestCoagentPage
      isLoading={isLoading}
      isLoggedIn={isLoggedIn}
      session={userSession}
    />
  );
}
