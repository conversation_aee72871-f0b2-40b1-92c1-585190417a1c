'use client';

import { useState, useEffect } from "react";
import { CurrencyDollarIcon, BoltIcon } from "@heroicons/react/24/outline";

export function PointsDisplay() {
  const [points, setPoints] = useState(1850);
  const [animatedPoints, setAnimatedPoints] = useState(1850);

  // Animate points changes
  useEffect(() => {
    const duration = 1000;
    const steps = 60;
    const increment = (points - animatedPoints) / steps;
    
    if (increment !== 0) {
      const timer = setInterval(() => {
        setAnimatedPoints(prev => {
          const next = prev + increment;
          if (
            (increment > 0 && next >= points) ||
            (increment < 0 && next <= points)
          ) {
            clearInterval(timer);
            return points;
          }
          return next;
        });
      }, duration / steps);
      
      return () => clearInterval(timer);
    }
    
    return undefined;
  }, [points, animatedPoints]);

  return (
    <div 
      className="flex items-center gap-3 px-4 py-2 rounded-lg border"
      style={{
        background: 'linear-gradient(145deg, hsl(0 0% 100%), hsl(220 14% 98%))',
        boxShadow: '0 4px 6px -1px hsl(220 13% 46% / 0.1), 0 2px 4px -1px hsl(220 13% 46% / 0.06)'
      }}
    >
      <div className="flex items-center gap-2">
        <CurrencyDollarIcon className="h-4 w-4" style={{ color: 'hsl(38 92% 50%)' }} />
        <span className="font-mono text-lg font-bold">
          {Math.round(animatedPoints).toLocaleString()}
        </span>
        <span className="text-sm" style={{ color: 'hsl(220 13% 46%)' }}>积分</span>
      </div>
      
      <div className="h-4 w-px" style={{ backgroundColor: 'hsl(220 13% 91%)' }} />
      
      <div className="flex items-center gap-1 text-sm" style={{ color: 'hsl(220 13% 46%)' }}>
        <BoltIcon className="h-3 w-3" />
        <span>+200/日</span>
      </div>
    </div>
  );
}