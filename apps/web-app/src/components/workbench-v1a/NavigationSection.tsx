'use client'

import React from 'react'

interface NavigationSectionProps {
  title: string
  children: React.ReactNode
  className?: string
}

export function NavigationSection({ 
  title, 
  children, 
  className = '' 
}: NavigationSectionProps) {
  return (
    <div className={`space-y-3 ${className}`}>
      {/* 分组标题 */}
      <div className="px-3">
        <h2 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
          {title}
        </h2>
      </div>
      
      {/* 分组内容 */}
      <div className="space-y-1">
        {children}
      </div>
    </div>
  )
}