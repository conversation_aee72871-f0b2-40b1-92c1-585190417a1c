'use client';

import { MagnifyingGlassIcon, CpuChipIcon } from "@heroicons/react/24/outline";
import { PointsDisplay } from "./PointsDisplay";

interface TopBarProps {
  onSearch?: (query: string) => void;
  searchQuery?: string;
  className?: string;
}

export function TopBar({ onSearch, searchQuery = "", className = "" }: TopBarProps) {
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSearch?.(e.target.value);
  };


  return (
    <header className={`h-16 border-b bg-card/50 backdrop-blur-sm flex items-center justify-between px-6 shadow-sm ${className}`}>
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <CpuChipIcon className="h-8 w-8 text-primary" />
          <span className="text-xl font-bold bg-gradient-primary bg-clip-text text-transparent">
            AI Tracker
          </span>
        </div>
      </div>
      
      <div className="flex-1 max-w-md mx-8">
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <input 
            value={searchQuery}
            onChange={handleSearchChange}
            placeholder="搜索 Idea 或股票..." 
            className="w-full pl-10 pr-4 py-2 bg-background/50 border border-input rounded-md text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          />
        </div>
      </div>
      
      <div className="flex items-center gap-4">
        <PointsDisplay />
      </div>
    </header>
  );
}