'use client'

import React from 'react'
import { Plus, Sparkles } from 'lucide-react'

interface NavigationActionButtonProps {
  label: string
  icon?: 'plus' | 'sparkles'
  onClick: () => void
  variant?: 'primary' | 'secondary'
  className?: string
}

export function NavigationActionButton({
  label,
  icon = 'plus',
  onClick,
  variant = 'primary',
  className = ''
}: NavigationActionButtonProps) {
  const IconComponent = icon === 'sparkles' ? Sparkles : Plus
  
  const baseClasses = 'w-full flex items-center space-x-2 px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 cursor-pointer'
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 shadow-sm hover:shadow-md',
    secondary: 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-200'
  }

  return (
    <button
      onClick={onClick}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
    >
      <IconComponent className="h-4 w-4" />
      <span>{label}</span>
    </button>
  )
}