// 导航相关的 TypeScript 接口定义

// Ideas 列表项接口
export interface IdeaNavItem {
  id: string
  title: string
  status?: 'executing' | 'completed' | 'draft'
  href: string
}

// 股票列表项接口  
export interface StockNavItem {
  symbol: string
  name?: string
  change?: number
  href: string
}

// 概览导航项接口
export interface OverviewNavItem {
  key: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  href: string
}

// 导航状态配置
export interface NavigationStatusConfig {
  label: string
  className: string
}

// 导航分组类型
export type NavigationGroupType = 'overview' | 'ideas' | 'stocks'

// 动态列表类型
export type DynamicListType = 'ideas' | 'stocks'

// 活跃Tab类型
export type ActiveTabType = 'ideas' | 'favorites' | 'shared' | 'stocks'