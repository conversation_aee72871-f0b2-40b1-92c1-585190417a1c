'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import {
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { useSession } from '@yai-investor-insight/user-account-fe';
import clsx from 'clsx';

const navigation = [
  { name: '首页', href: '/' },
  { name: '插件演示', href: '/demo-feature' },
  { name: '调研演示V1', href: '/research' },
  { name: '调研演示V2', href: '/research-v2' },
  { name: '调研演示V2B', href: '/research-v2b' },
  { name: '异步任务', href: '/investment-insight' },
  { name: '调研演示V2H', href: '/research-v2h' },
  { name: '工作台V1-A (Workbench)', href: '/workbench-v1a' }
];

export function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const { session, isLoading, isLoggedIn, logout } = useSession();
  
  const handleLogin = () => {
    router.push('/login');
  };
  
  const handleLogout = async () => {
    await logout();
    router.refresh();
  };
  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <nav className="w-full px-4 sm:px-6 lg:px-8" aria-label="Top">
        <div className="flex h-16 items-center justify-between">
          {/* Left side - Logo and Navigation */}
          <div className="flex items-center space-x-8">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-blue-600 flex items-center justify-center">
                <span className="text-white font-bold text-sm">AI</span>
              </div>
              <span className="text-xl font-bold text-gray-900">
                投资洞察
              </span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex md:items-center md:space-x-6">
              {navigation.map((item) => (
                <Link
                  key={item.href}  // 使用 href 作为 key 而不是 name
                  href={item.href}
                  className={clsx(
                    'px-3 py-2 rounded-md text-sm font-medium transition-colors',
                    pathname === item.href
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-700 hover:text-blue-600'
                  )}
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            {/* Notifications */}
            <Button variant="ghost" size="sm" className="relative">
              <BellIcon className="h-5 w-5" />
            </Button>

            {/* User Authentication */}
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
                <span className="text-sm text-gray-600">加载中...</span>
              </div>
            ) : isLoggedIn ? (
              <div className="flex items-center space-x-3">
                {/* User Info - 点击可跳转到用户信息页面 */}
                <Link 
                  href="/profile"
                  className="flex items-center space-x-2 hover:bg-gray-50 rounded-lg px-2 py-1 transition-colors cursor-pointer"
                >
                  <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {session?.userName?.charAt(0) || '用'}
                    </span>
                  </div>
                  <div className="hidden sm:block">
                    <p className="text-sm font-medium text-gray-900 hover:text-blue-600 transition-colors">
                      {session?.userName || '用户'}
                    </p>
                  </div>
                </Link>
                {/* Logout Button */}
                <Button
                  onClick={handleLogout}
                  variant="outline"
                  size="sm"
                  className="text-gray-600 hover:text-gray-900"
                >
                  退出
                </Button>
              </div>
            ) : (
              <Button
                onClick={handleLogin}
                className="flex items-center space-x-2 border border-blue-200 text-blue-600 hover:bg-blue-50 bg-white px-4 py-2 rounded-lg transition-colors"
                variant="ghost"
              >
                <UserIcon className="h-4 w-4" />
                <span className="text-sm font-medium">登录</span>
              </Button>
            )}

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? (
                  <XMarkIcon className="h-5 w-5" />
                ) : (
                  <Bars3Icon className="h-5 w-5" />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden">
            <div className="space-y-1 pb-3 pt-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={clsx(
                    'block px-3 py-2 rounded-md text-base font-medium transition-colors',
                    pathname === item.href
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-700 hover:text-blue-600'
                  )}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
        )}
      </nav>


    </header>
  );
}
