# YAI Investor Insight - Web App

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## 项目概述

本项目是 YAI 投资研究平台的前端 Web 应用，作为整个系统的核心框架壳，负责提供基础的页面结构、路由管理、状态管理和 UI 组件库。具体的业务功能通过 libs 库的形式模块化引入。

## 技术框架

### 核心技术栈
- **框架**: Next.js 15.4.0 (React 19.0.0)
- **语言**: TypeScript 5.8.3
- **样式**: Tailwind CSS 4.0
- **状态管理**: Zustand 5.0
- **数据请求**: TanStack React Query 5.83
- **UI 组件**: 
  - Headless UI 2.0 (无样式组件)
  - Heroicons 2.0 (图标库)
  - AG-UI (内部组件库)
  - Lucide React (图标库)
- **图表库**: 
  - Recharts 3.1 (通用图表)
  - Lightweight Charts 5.0 (金融图表)
- **工具库**:
  - clsx (条件样式)
  - date-fns (日期处理)
  - iron-session (会话管理)

### 开发工具
- **包管理器**: pnpm (高性能的 npm 替代方案)
- **构建工具**: Nx Monorepo
- **代码规范**: ESLint + Prettier
- **测试**: Jest + MSW (Mock Service Worker)
- **类型检查**: TypeScript

## 项目架构

### 目录结构
```
src/
├── app/                    # Next.js App Router 页面
│   ├── api/               # API 路由
│   ├── actions/           # Server Actions
│   ├── (各业务页面)/       # 页面组件
│   ├── layout.tsx         # 根布局
│   └── global.css         # 全局样式
├── components/            # 通用组件
│   ├── ui/               # 基础 UI 组件
│   ├── layout/           # 布局组件
│   ├── charts/           # 图表组件
│   └── (业务组件)/        # 各业务领域组件
├── hooks/                # 自定义 Hooks
├── lib/                  # 工具函数和配置
├── store/                # 状态管理
├── types/                # 类型定义
└── middleware.ts         # 中间件
```

### 业务功能模块化

本项目采用 Nx Monorepo 架构，核心原则是：

1. **Web App 作为框架壳**: 提供基础的页面结构、路由、布局和通用组件
2. **业务功能独立成库**: 具体的业务逻辑以 libs 形式组织，实现模块化和可复用性

#### 当前业务库
- `@yai-investor-insight/research-v2-fe` - 研究功能 V2
- `@yai-investor-insight/research-v2b-fe` - 研究功能 V2B
- `@yai-investor-insight/research-v2h-fe` - 研究功能 V2H
- `@yai-investor-insight/demo-feature-fe` - 演示功能
- `@yai-investor-insight/user-account-fe` - 用户账户功能
- `@yai-investor-insight/shared-fe-kit` - 前端通用组件库
- `@yai-investor-insight/shared-fe-core` - 前端核心工具库（包含 coagent 数据请求层）
- `@yai-investor-insight/shared-types` - 共享类型定义库

#### 核心共享库详细说明

##### `@yai-investor-insight/shared-fe-kit` - 前端通用组件库

`shared-fe-kit` 是项目的前端通用组件库，提供了一套完整的、可复用的 UI 组件和工具函数，确保整个平台的设计一致性和开发效率。

**主要功能**：
- **基础 UI 组件**: 按钮、输入框、模态框、表格等常用组件
- **布局组件**: 页面布局、栅格系统、响应式容器
- **业务组件**: 针对投资研究场景的专用组件（如股票卡片、图表容器等）
- **工具函数**: 通用的工具方法和 Hooks
- **样式系统**: 统一的设计令牌和主题配置
- **图标库**: 项目专用的图标集合

**设计原则**：
- **一致性**: 遵循统一的设计规范和交互模式
- **可复用性**: 组件高度抽象，支持多种使用场景
- **可定制性**: 支持主题定制和样式覆盖
- **可访问性**: 符合 WCAG 2.1 无障碍标准
- **类型安全**: 完整的 TypeScript 类型支持

**使用示例**：
```typescript
import { Button, Modal, DataTable, StockCard } from '@yai-investor-insight/shared-fe-kit';

function MyComponent() {
  return (
    <div>
      <Button variant="primary" size="large">提交</Button>
      <StockCard symbol="AAPL" data={stockData} />
      <DataTable columns={columns} data={tableData} />
    </div>
  );
}
```

##### `@yai-investor-insight/shared-fe-core` - 前端核心工具库

包含 `coagent` 数据请求层和其他核心工具函数，为整个前端应用提供基础设施支持。

##### `@yai-investor-insight/shared-types` - 共享类型定义库

提供前后端共享的 TypeScript 类型定义，确保数据结构的一致性和类型安全。

#### 引用方式
通过 TypeScript 路径映射配置，可以直接引用 libs：
```typescript
import { SomeComponent } from '@yai-investor-insight/demo-feature-fe';
import { Button, Modal } from '@yai-investor-insight/shared-fe-kit';
import { useCoagentQuery } from '@yai-investor-insight/shared-fe-core';
```

## 开发规范

### 1. 代码组织原则
- **关注点分离**: Web App 只负责框架层面的功能，业务逻辑放在对应的 libs 中
- **模块化**: 新的业务功能应创建独立的 lib，避免在 Web App 中直接实现
- **可复用性**: 通用组件和工具函数放在 shared 库中

### 2. 组件开发规范
- 使用 TypeScript 进行类型安全开发
- 组件采用函数式组件 + Hooks 模式
- 样式使用 Tailwind CSS，避免内联样式
- 组件应具备良好的可访问性 (a11y)

### 3. 状态管理规范
- 全局状态使用 Zustand
- 服务端状态使用 React Query
- 本地状态优先使用 useState/useReducer

### 4. API 调用规范

#### Coagent 统一数据请求方案

项目采用 `@yai-investor-insight/shared-fe-core` 中的 **coagent** 作为统一的数据请求层。`coagent` 旨在简化 Next.js `use client` 和 `use server` 之间的数据交互，并提供统一的认证和请求头管理机制。

##### 核心职责
- **统一 API 入口**: 无论是客户端组件还是服务器端操作，都通过 `coagent` 发起请求。
- **自动化认证**: `coagent` 负责处理用户认证流程。它会自动解析请求中的 `cookies`，提取用户信息（如 `userId`）。
- **请求头注入**: 对于需要认证的请求，`coagent` 会自动在 HTTP Header 中添加 `lucas-uniq-userId` 字段，以便后端服务识别用户身份。
- **公共接口处理**: `coagent` 能够区分需要认证的私有接口和无需认证的公共接口（如登录、发送验证码等），对公共接口不进行 `userId` 的注入。
- **服务端调用**: 在 Next.js Server Actions (`use server`) 中，`coagent` 利用 Node.js 的 `http` 模块直接调用后端数据服务，实现了服务端的安全数据获取。

##### 使用示例

```typescript
// 在 Client Component 或 Server Action 中使用
import { useCoagentQuery } from '@yai-investor-insight/shared-fe-core';

// 示例1: 在客户端组件中获取需要认证的数据
const { data, loading, error } = useCoagent({
  endpoint: '/api/user/profile',
  // coagent 会自动处理 cookie 解析和 header 注入
});

// 示例2: 在 Server Action 中调用后端服务
'use server';

import { createCoagent } from '@yai-investor-insight/shared-fe-core';
import axios from 'axios';

const axiosInstance = axios.create({
  baseURL: process.env.API_BASE_URL,
  timeout: 10000,
});

const coagent = createCoagent(axiosInstance);

export async function updateProfile(formData) {
  const result = await coagent.request({
    method: 'POST',
    url: '/api/user/profile',
    data: formData,
    // 在服务端，coagent 同样会处理认证逻辑
  });

  if (result.error) {
    throw new Error(result.error);
  }

  return result.data;
}

// 示例3: 调用公共接口，无需认证
const { data, error } = useCoagent({
  endpoint: '/api/auth/login',
  isPublic: true, // 标记为公共接口
});
```

#### 设计原则
- **统一接口**: 所有 API 调用通过统一的客户端进行
- **类型安全**: 基于 `@yai-investor-insight/shared-types` 提供完整的 TypeScript 类型支持
- **错误处理**: 内置统一的错误处理机制，支持重试和降级策略
- **实时通信**: 支持 SSE 和 WebSocket，提供实时数据更新能力
- **缓存策略**: 智能缓存管理，减少不必要的网络请求
- **离线支持**: 支持离线模式和数据同步

### 5. 路由规范
- 使用 Next.js App Router
- 页面组件放在 `app/` 目录下
- 动态路由使用文件夹命名约定

### 6. 样式规范
- 优先使用 Tailwind CSS 工具类
- 复杂样式可以使用 CSS Modules 或 styled-components
- 保持设计系统的一致性

### 7. 测试规范
- 单元测试使用 Jest
- API 模拟使用 MSW
- 组件测试使用 React Testing Library

## 新功能开发流程

1. **评估功能范围**: 判断是否需要创建新的 lib
2. **创建业务库**: 如果是独立业务功能，使用 `nx generate @nx/react:library` 创建新库
3. **实现业务逻辑**: 在对应的 lib 中实现具体功能
4. **集成到 Web App**: 在 Web App 中创建页面和路由，引用业务库的组件
5. **更新路径映射**: 在 `tsconfig.base.json` 中添加新库的路径映射
6. **测试和文档**: 编写测试用例和更新文档

## Getting Started

### 安装依赖

本项目使用 pnpm 作为包管理器，请确保已安装 pnpm：

```bash
# 安装 pnpm (如果尚未安装)
npm install -g pnpm

# 安装项目依赖
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
