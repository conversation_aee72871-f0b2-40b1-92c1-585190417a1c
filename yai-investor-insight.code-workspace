{
  "folders": [
    {
      "name": "🏠 Root",
      "path": "."
    },
    {
      "name": "🚀 API Server",
      "path": "apps/api-server"
    },
    {
      "name": "🌐 Web App",
      "path": "apps/web-app"
    },
    {
      "name": "📄 Debug Logs",
      "path": "logs"
    },
    {
      "name": "📦 Shared Frontend Types",
      "path": "libs/shared-fe-types"
    },
    {
      "name": "📦 Shared Frontend Kit",
      "path": "libs/shared-fe-kit"
    },
    {
      "name": "📦 Shared Frontend Core",
      "path": "libs/shared-fe-core"
    },
    {
      "name": "📦 Shared Backend Core",
      "path": "libs/shared-bs-core"
    },
    {
      "name": "📦 Shared Backend LLM",
      "path": "libs/shared-bs-llm"
    },
    {
      "name": "📦 Demo Feature Frontend",
      "path": "libs/demo-feature-fe"
    },
    {
      "name": "📦 Demo Feature Backend",
      "path": "libs/demo-feature-bs"
    },
    {
      "name": "📦 User Feature Frontend",
      "path": "libs/user-account-fe"
    },
    {
      "name": "📦 Research V2 Frontend",
      "path": "libs/research-v2-fe"
    },
    {
      "name": "📦 Research V2 Backend",
      "path": "libs/research-v2-bs"
    },
    {
      "name": "📦 Research V2B Frontend",
      "path": "libs/research-v2b-fe"
    },
    {
      "name": "📦 Research V2B Backend",
      "path": "libs/research-v2b-bs"
    },
    {
      "name": "📦 Research V2H Frontend",
      "path": "libs/research-v2h-fe"
    },
    {
      "name": "📦 Research V2H Backend",
      "path": "libs/research-v2h-bs"
    },
    {
      "name": "🧠 Idea Feature V1A Frontend",
      "path": "libs/idea-feature-v1a-fe"
    },
    {
      "name": "🧪 Test Coagent Frontend",
      "path": "libs/test-coagent-fe"
    },
    {
      "name": "🎯 Lovable Demo (v3)",
      "path": "examples/lovable-idea-pilot-demo"
    },
    {
      "name": "🎯 Lovable Demo (v4 手动版)",
      "path": "examples/lovable-idea-pilot-demo-manual-v4"
    },
    {
      "name": "🧪 E2E Tests",
      "path": "examples/lovable-idea-pilot-demo-e2e"
    }
  ],
  "settings": {
    // Python 配置
    "python.defaultInterpreterPath": "./apps/api-server/.venv/bin/python",
    "python.terminal.activateEnvironment": true,
    "python.analysis.autoImportCompletions": true,
    "python.analysis.typeCheckingMode": "basic",
    "python.analysis.extraPaths": [
      "./apps/api-server/src"
    ],
    
    // 搜索排除 - 提升搜索速度
    "search.exclude": {
      "**/node_modules": true,
      "**/.venv": true,
      "**/.nx": true,
      "**/dist": true,
      "**/.next": true,
      "**/__pycache__": true,
      "**/.ruff_cache": true,
      "**/pnpm-lock.yaml": true
    },
    
    // 文件监视排除 - 减少 CPU 占用
    "files.watcherExclude": {
      "**/.venv/**": true,
      "**/node_modules/**": true,
      "**/.nx/**": true,
      "**/dist/**": true,
      "**/.next/**": true,
      "**/__pycache__/**": true,
      "**/.ruff_cache/**": true
    },
    "nxConsole.generateAiAgentRules": true
  },
  "extensions": {
    "recommendations": [
      "ms-python.python",
      "ms-python.debugpy",
      "ms-vscode.vscode-typescript-next",
      "bradlc.vscode-tailwindcss",
      "nrwl.angular-console"
    ]
  }
}