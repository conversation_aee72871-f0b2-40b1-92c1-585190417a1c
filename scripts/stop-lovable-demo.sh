#!/bin/bash

# 停止 Lovable Idea Pilot Demo 项目

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

LOG_DIR="logs"
PID_FILE="$LOG_DIR/lovable-demo.pid"

echo -e "${BLUE}=== 停止 Lovable Idea Pilot Demo ===${NC}"

# 检查PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo -e "${YELLOW}未找到PID文件，尝试通过端口查找进程...${NC}"
    
    # 通过端口8080查找进程
    PID=$(lsof -ti:8080 2>/dev/null || echo "")
    
    if [ -z "$PID" ]; then
        echo -e "${YELLOW}未找到运行在端口8080的进程${NC}"
        exit 0
    fi
else
    # 从文件读取PID
    PID=$(cat "$PID_FILE")
fi

echo -e "${YELLOW}找到进程ID: $PID${NC}"

# 检查进程是否存在
if kill -0 $PID 2>/dev/null; then
    echo -e "${YELLOW}正在停止进程...${NC}"
    
    # 优雅停止
    kill -TERM $PID 2>/dev/null || true
    
    # 等待进程结束
    sleep 2
    
    # 如果进程仍在运行，强制停止
    if kill -0 $PID 2>/dev/null; then
        echo -e "${YELLOW}进程未响应，强制停止...${NC}"
        kill -KILL $PID 2>/dev/null || true
    fi
    
    echo -e "${GREEN}✅ Lovable Demo 已停止${NC}"
else
    echo -e "${YELLOW}进程已经停止${NC}"
fi

# 清理PID文件
if [ -f "$PID_FILE" ]; then
    rm "$PID_FILE"
fi

echo -e "${GREEN}🧹 清理完成${NC}"
