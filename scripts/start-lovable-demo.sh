#!/bin/bash

# 启动 Lovable Idea Pilot Demo 项目
# 这是一个基于 Vite + React + TypeScript + shadcn-ui 的演示项目

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_DIR="examples/lovable-idea-pilot-demo"
LOG_DIR="logs"

echo -e "${BLUE}=== 启动 Lovable Idea Pilot Demo ===${NC}"

# 检查项目目录是否存在
if [ ! -d "$PROJECT_DIR" ]; then
    echo -e "${RED}错误: 项目目录 $PROJECT_DIR 不存在${NC}"
    exit 1
fi

# 创建日志目录
mkdir -p "$LOG_DIR"

# 进入项目目录
cd "$PROJECT_DIR"

echo -e "${YELLOW}检查依赖安装状态...${NC}"

# 检查 node_modules 是否存在
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}依赖未安装，正在安装...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}依赖安装失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}依赖安装完成${NC}"
else
    echo -e "${GREEN}依赖已安装${NC}"
fi

echo -e "${YELLOW}启动开发服务器...${NC}"

# 启动开发服务器，并将日志输出到文件
npm run dev 2>&1 | tee "../../$LOG_DIR/lovable-demo.log" &

# 获取进程ID
PID=$!

# 等待服务器启动
sleep 3

# 检查进程是否还在运行
if kill -0 $PID 2>/dev/null; then
    echo -e "${GREEN}✅ Lovable Demo 启动成功！${NC}"
    echo -e "${BLUE}📱 本地访问: http://localhost:8080/${NC}"
    echo -e "${BLUE}🌐 网络访问: http://************:8080/${NC}"
    echo -e "${YELLOW}📋 进程ID: $PID${NC}"
    echo -e "${YELLOW}📝 日志文件: $LOG_DIR/lovable-demo.log${NC}"
    echo -e "${YELLOW}🛑 停止服务: 使用 scripts/stop-lovable-demo.sh${NC}"
    
    # 保存进程ID到文件
    echo $PID > "../../$LOG_DIR/lovable-demo.pid"
else
    echo -e "${RED}❌ 启动失败，请检查日志文件${NC}"
    exit 1
fi
