#!/bin/bash

# 查看 Lovable Idea Pilot Demo 项目日志

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

LOG_DIR="logs"
LOG_FILE="$LOG_DIR/lovable-demo.log"

echo -e "${BLUE}=== Lovable Idea Pilot Demo 日志 ===${NC}"

# 检查日志文件是否存在
if [ ! -f "$LOG_FILE" ]; then
    echo -e "${YELLOW}日志文件不存在: $LOG_FILE${NC}"
    echo -e "${YELLOW}请先启动项目: scripts/start-lovable-demo.sh${NC}"
    exit 1
fi

echo -e "${GREEN}📝 日志文件: $LOG_FILE${NC}"
echo -e "${YELLOW}按 Ctrl+C 退出日志查看${NC}"
echo -e "${BLUE}===========================================${NC}"

# 实时查看日志
tail -f "$LOG_FILE"
