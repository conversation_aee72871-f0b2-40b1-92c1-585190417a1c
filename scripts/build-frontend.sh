#!/bin/bash

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本所在目录的项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 切换到项目根目录
cd "$PROJECT_ROOT"

log_info "开始构建前端项目..."
log_info "项目根目录: $PROJECT_ROOT"

# 记录开始时间
START_TIME=$(date +%s)

# 1. 环境检查
log_info "正在检查前端构建环境..."

# 检查 Node.js
if ! command -v node &> /dev/null; then
    log_error "Node.js 未安装或不在 PATH 中"
    exit 1
fi
NODE_VERSION=$(node --version)
log_success "Node.js 版本: $NODE_VERSION"

# 检查 pnpm
if ! command -v pnpm &> /dev/null; then
    log_error "pnpm 未安装或不在 PATH 中"
    exit 1
fi
PNPM_VERSION=$(pnpm --version)
log_success "pnpm 版本: $PNPM_VERSION"

# 检查 Nx
if ! command -v nx &> /dev/null; then
    if ! pnpm nx --version &> /dev/null; then
        log_error "Nx 未安装"
        exit 1
    fi
fi
log_success "Nx 可用"

# 2. 清理前端构建产物
log_info "正在清理前端构建产物..."

# 清理前端相关的构建产物（仅清理库的 dist，保留 web-app 的 .next）
find libs -name "dist" -type d -exec rm -rf {} + 2>/dev/null || true
# 注意：不清理 apps/web-app/.next，因为我们需要验证构建结果

log_success "前端构建产物清理完成"

# 3. 安装前端依赖
log_info "正在安装前端依赖..."
# 尝试使用 frozen-lockfile，如果失败则使用普通安装
if ! pnpm install --frozen-lockfile; then
    log_warn "frozen-lockfile 安装失败，尝试切换镜像源并使用普通安装模式..."
    pnpm config set registry http://registry.npmmirror.com
    pnpm install
fi
log_success "前端依赖安装完成"

# 4. 构建共享库（必须优先构建）
log_info "正在构建共享库..."
# shared-types 已删除，跳过构建
log_success "共享库构建完成"

# 5. 构建前端库
log_info "正在构建前端库..."
# 构建所有 fe 类型的库（跳过没有 build target 的项目）
if timeout 300 pnpm nx run-many --target=build --projects=tag:type:fe,tag:type:ui --parallel; then
    log_success "前端库构建完成"
else
    log_warn "部分前端库构建失败或超时，继续进行..."
fi

# 6. 构建前端应用
log_info "构建前端应用 (web-app)..."
cd apps/web-app
if pnpm build; then
    log_success "前端应用构建完成"
else
    log_error "前端应用构建失败"
    exit 1
fi
cd "$PROJECT_ROOT"

# 7. 验证前端构建结果
log_info "正在验证前端构建结果..."

FAILED_BUILDS=()

# 检查前端构建产物
if [ ! -d "apps/web-app/.next" ] && [ ! -d "apps/web-app/dist" ]; then
    FAILED_BUILDS+=("web-app")
fi

# 检查共享库构建产物
# shared-types 已删除，跳过检查

# 检查前端库构建产物（可选，因为可能部分失败）
FE_LIBS=("research-v2-fe" "research-v2b-fe" "research-v2h-fe" "shared-fe-core" "shared-fe-kit" "demo-feature-fe" "user-account-fe")
for lib in "${FE_LIBS[@]}"; do
    if [ -d "libs/$lib" ] && [ ! -d "libs/$lib/dist" ]; then
        log_warn "前端库 $lib 构建失败，但不影响主应用"
    fi
done

# 8. 输出前端构建结果
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo "======================================"
echo "         前端构建结果汇总"
echo "======================================"

if [ ${#FAILED_BUILDS[@]} -eq 0 ]; then
    log_success "前端项目构建成功！"
    echo ""
    log_info "构建产物位置："
    log_info "  - 前端应用: apps/web-app/.next/"
    log_info "  - 共享库: libs/*/dist/"
    log_info "  - 前端库: libs/*-fe/dist/"
    echo ""
    log_success "总耗时: ${DURATION}秒"
    echo ""
    log_info "启动命令："
    log_info "  - 启动前端开发服务器: pnpm nx dev web-app"
    log_info "  - 启动前端生产服务器: cd apps/web-app && pnpm start"
else
    log_error "以下前端项目构建失败:"
    for failed in "${FAILED_BUILDS[@]}"; do
        log_error "  - $failed"
    done
    echo ""
    log_warn "总耗时: ${DURATION}秒"
    exit 1
fi

echo "======================================"