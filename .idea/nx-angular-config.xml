<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="NxAngularConfigService" workspaceLocation="file://$PROJECT_DIR$/nx.json">
    <projects>
      <project name=".vercel" file="file://$PROJECT_DIR$/.vercel/project.json" />
      <project name="api-server" file="file://$PROJECT_DIR$/apps/api-server/project.json" />
      <project name="demo-feature-fe" file="file://$PROJECT_DIR$/libs/demo-feature-fe/project.json" />
      <project name="idea-feature-v1a-fe" file="file://$PROJECT_DIR$/libs/idea-feature-v1a-fe/project.json" />
      <project name="lovable-idea-pilot-demo" file="file://$PROJECT_DIR$/examples/lovable-idea-pilot-demo-manual-v4/project.json" />
      <project name="lovable-idea-pilot-demo-e2e" file="file://$PROJECT_DIR$/examples/lovable-idea-pilot-demo-e2e/project.json" />
      <project name="research-v2-fe" file="file://$PROJECT_DIR$/libs/research-v2-fe/project.json" />
      <project name="research-v2b-fe" file="file://$PROJECT_DIR$/libs/research-v2b-fe/project.json" />
      <project name="research-v2h-fe" file="file://$PROJECT_DIR$/libs/research-v2h-fe/project.json" />
      <project name="shared-bs-core" file="file://$PROJECT_DIR$/libs/shared-bs-core/project.json" />
      <project name="shared-bs-llm" file="file://$PROJECT_DIR$/libs/shared-bs-llm/project.json" />
      <project name="shared-fe-core" file="file://$PROJECT_DIR$/libs/shared-fe-core/project.json" />
      <project name="shared-fe-kit" file="file://$PROJECT_DIR$/libs/shared-fe-kit/project.json" />
      <project name="stock-feature-v1a-fe" file="file://$PROJECT_DIR$/libs/stock-feature-v1a-fe/project.json" />
      <project name="user-account-fe" file="file://$PROJECT_DIR$/libs/user-account-fe/project.json" />
      <project name="web-app" file="file://$PROJECT_DIR$/apps/web-app/project.json" />
      <project name="yai-investor-insight" file="file://$PROJECT_DIR$/project.json" />
    </projects>
  </component>
</project>