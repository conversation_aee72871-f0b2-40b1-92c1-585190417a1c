@import "tailwindcss";

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

:root {
  /* AI-inspired color palette */
  --background: hsl(220 13% 98%);
  --foreground: hsl(220 20% 15%);

  --card: hsl(0 0% 100%);
  --card-foreground: hsl(220 20% 15%);

  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(220 20% 15%);

  /* Deep blue-purple primary palette */
  --primary: hsl(240 50% 20%);
  --primary-foreground: hsl(0 0% 98%);
  --primary-glow: hsl(240 100% 80%);

  /* Financial green for success states */
  --success: hsl(142 71% 45%);
  --success-foreground: hsl(0 0% 98%);

  /* Warning orange for alerts */
  --warning: hsl(38 92% 50%);
  --warning-foreground: hsl(0 0% 98%);

  --secondary: hsl(220 14% 96%);
  --secondary-foreground: hsl(220 20% 15%);

  --muted: hsl(220 14% 96%);
  --muted-foreground: hsl(220 13% 46%);

  --accent: hsl(240 5% 96%);
  --accent-foreground: hsl(240 50% 20%);

  --destructive: hsl(0 84% 60%);
  --destructive-foreground: hsl(0 0% 98%);

  --border: hsl(220 13% 91%);
  --input: hsl(220 13% 91%);
  --ring: hsl(240 50% 20%);

  --radius: 0.75rem;

  /* Gradients for premium feel */
  --gradient-primary: linear-gradient(135deg, hsl(240 50% 20%), hsl(260 50% 25%));
  --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(220 14% 98%));
  --gradient-success: linear-gradient(135deg, hsl(142 71% 45%), hsl(152 81% 55%));

  /* Shadows for depth */
  --shadow-card: 0 4px 6px -1px hsl(220 13% 46% / 0.1), 0 2px 4px -1px hsl(220 13% 46% / 0.06);
  --shadow-elevated: 0 10px 15px -3px hsl(240 50% 20% / 0.1), 0 4px 6px -2px hsl(240 50% 20% / 0.05);
  --shadow-glow: 0 0 40px hsl(240 100% 80% / 0.3);

  /* Animations */
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Sidebar */
  --sidebar-background: hsl(240 10% 8%);
  --sidebar-foreground: hsl(240 20% 80%);
  --sidebar-primary: hsl(240 50% 20%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(240 15% 15%);
  --sidebar-accent-foreground: hsl(240 20% 80%);
  --sidebar-border: hsl(240 15% 15%);
  --sidebar-ring: hsl(240 100% 80%);
}

.dark {
  --background: hsl(222.2 84% 4.9%);
  --foreground: hsl(210 40% 98%);

  --card: hsl(222.2 84% 4.9%);
  --card-foreground: hsl(210 40% 98%);

  --popover: hsl(222.2 84% 4.9%);
  --popover-foreground: hsl(210 40% 98%);

  --primary: hsl(210 40% 98%);
  --primary-foreground: hsl(222.2 47.4% 11.2%);

  --secondary: hsl(217.2 32.6% 17.5%);
  --secondary-foreground: hsl(210 40% 98%);

  --muted: hsl(217.2 32.6% 17.5%);
  --muted-foreground: hsl(215 20.2% 65.1%);

  --accent: hsl(217.2 32.6% 17.5%);
  --accent-foreground: hsl(210 40% 98%);

  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(210 40% 98%);

  --border: hsl(217.2 32.6% 17.5%);
  --input: hsl(217.2 32.6% 17.5%);
  --ring: hsl(212.7 26.8% 83.9%);
  --sidebar-background: hsl(240 5.9% 10%);
  --sidebar-foreground: hsl(240 4.8% 95.9%);
  --sidebar-primary: hsl(224.3 76.3% 48%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(240 3.7% 15.9%);
  --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
  --sidebar-border: hsl(240 3.7% 15.9%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary-glow: var(--primary-glow);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-sidebar-background: var(--sidebar-background);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}