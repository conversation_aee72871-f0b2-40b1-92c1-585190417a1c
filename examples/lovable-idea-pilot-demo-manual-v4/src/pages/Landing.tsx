import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Brain, Search, TrendingUp, FileText, Clock, BarChart3, Paperclip, ChevronDown } from "lucide-react";

export default function Landing() {
  const [searchQuery, setSearchQuery] = useState("");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [deepVerify, setDeepVerify] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState("全部");
  const [selectedHotFilter, setSelectedHotFilter] = useState("热门");
  const [selectedTimeFilter, setSelectedTimeFilter] = useState("全部");
  const [selectedIndustryFilter, setSelectedIndustryFilter] = useState("全部");
  const [selectedEventFilter, setSelectedEventFilter] = useState("全部");
  const [showMoreTopics, setShowMoreTopics] = useState(false);
  const navigate = useNavigate();

  // 筛选选项
  const hotFilters = ["热门", "最新", "推荐"];
  const timeFilters = ["全部", "今日", "本周", "本月"];
  const industryFilters = ["全部", "科技", "金融", "新能源", "医疗", "消费"];
  const eventFilters = ["全部", "财报", "并购", "政策", "产品", "人事"];

  // 热门分析话题
  const allTopics = [
    {
      id: 1,
      title: "NVIDIA H200芯片量产对AI行业的影响",
      description: "分析新一代AI芯片对行业竞争格局的深度影响，预测相关公司业绩表现...",
      category: "科技",
      stocks: ["NVDA", "AMD", "INTC", "TSM"],
      type: "行业分析",
      time: "1小时前",
      author: "张三"
    },
    {
      id: 2,
      title: "微软Copilot在企业市场的渗透率",
      description: "评估AI助手在企业级市场的商业化进展，分析订阅收入增长潜力...",
      category: "科技",
      stocks: ["MSFT", "GOOGL", "AMZN", "ORCL"],
      type: "产品分析",
      time: "2小时前",
      author: "李四"
    },
    {
      id: 3,
      title: "特斯拉FSD自动驾驶技术商业化前景",
      description: "深度解析全自动驾驶技术的商业化时间表，评估对公司估值的影响...",
      category: "新能源",
      stocks: ["TSLA", "WAYMO", "NVDA", "AAPL"],
      type: "技术分析",
      time: "3小时前",
      author: "王五"
    },
    {
      id: 4,
      title: "苹果M4芯片对端侧AI的推动作用",
      description: "分析苹果最新芯片架构对AI计算能力的提升，预测生态系统变化...",
      category: "科技",
      stocks: ["AAPL", "QCOM", "ARM", "NVDA"],
      type: "技术分析",
      time: "5小时前",
      author: "赵六"
    },
    {
      id: 5,
      title: "美联储利率政策对银行股的影响",
      description: "解读最新货币政策对金融行业的影响，重点关注净息差变化趋势...",
      category: "金融",
      stocks: ["JPM", "BAC", "WFC", "C"],
      type: "政策分析",
      time: "1天前",
      author: "孙七"
    },
    {
      id: 6,
      title: "新能源汽车补贴政策退坡影响",
      description: "分析各国补贴政策变化对新能源车企的影响，评估市场竞争格局...",
      category: "新能源",
      stocks: ["BYD", "NIO", "XPEV", "LI"],
      type: "政策分析",
      time: "1天前",
      author: "周八"
    },
    {
      id: 7,
      title: "辉瑞新冠疫苗专利到期影响",
      description: "评估疫苗专利到期对制药巨头收入的影响，分析后疫情时代策略...",
      category: "医疗",
      stocks: ["PFE", "MRNA", "BNTX", "JNJ"],
      type: "事件分析",
      time: "2天前",
      author: "吴九"
    },
    {
      id: 8,
      title: "电商平台Q4业绩预期分析",
      description: "预测主要电商平台四季度表现，分析消费复苏对业绩的提振作用...",
      category: "消费",
      stocks: ["AMZN", "BABA", "JD", "PDD"],
      type: "财报分析",
      time: "2天前",
      author: "郑十"
    },
    {
      id: 9,
      title: "ChatGPT-5发布对AI行业的冲击",
      description: "分析新一代大模型对整个AI生态的颠覆性影响，预测竞争格局变化...",
      category: "科技",
      stocks: ["MSFT", "GOOGL", "META", "NVDA"],
      type: "事件分析",
      time: "3天前",
      author: "钱一"
    },
    {
      id: 10,
      title: "全球通胀数据对央行政策的影响",
      description: "解读最新通胀数据，预测各国央行货币政策走向及对市场的影响...",
      category: "金融",
      stocks: ["GLD", "TLT", "DXY", "SPY"],
      type: "宏观分析",
      time: "3天前",
      author: "陈二"
    }
  ];

  // 根据筛选条件过滤话题
  let filteredTopics = allTopics;
  
  // 根据行业筛选
  if (selectedIndustryFilter !== "全部") {
    filteredTopics = filteredTopics.filter(topic => topic.category === selectedIndustryFilter);
  }
    
  // 根据时间筛选
  if (selectedTimeFilter !== "全部") {
    filteredTopics = filteredTopics.filter(topic => {
      if (selectedTimeFilter === "今日") return topic.time.includes("小时前");
      if (selectedTimeFilter === "本周") return topic.time.includes("天前") || topic.time.includes("小时前");
      if (selectedTimeFilter === "本月") return true;
      return true;
    });
  }
  
  // 根据事件类型筛选
  if (selectedEventFilter !== "全部") {
    const eventTypeMap: any = {
      "财报": ["财报分析"],
      "并购": ["事件分析"],
      "政策": ["政策分析"],
      "产品": ["产品分析", "技术分析"],
      "人事": ["事件分析"]
    };
    if (eventTypeMap[selectedEventFilter]) {
      filteredTopics = filteredTopics.filter(topic => 
        eventTypeMap[selectedEventFilter].includes(topic.type)
      );
    }
  }
  
  // 根据热门筛选排序
  if (selectedHotFilter === "最新") {
    filteredTopics = [...filteredTopics].sort((a, b) => {
      const timeOrder = (time: string) => {
        if (time.includes("小时前")) return parseInt(time);
        if (time.includes("天前")) return parseInt(time) * 24;
        return 999;
      };
      return timeOrder(a.time) - timeOrder(b.time);
    });
  }

  // 示例工作区数据
  const workspaceItems = [
    {
      id: 1,
      title: "特斯拉财报分析",
      description: "Q3 2024财报深度解读及投资建议，包含详细的财务数据分析和未来预测模型...",
      status: "进行中",
      type: "财报分析",
      time: "2小时前",
      author: "张明",
      stocks: ["TSLA", "NIO", "XPEV", "BYD"]
    },
    {
      id: 2,
      title: "AI芯片行业调研",
      description: "NVIDIA财报后的行业影响分析，涵盖上下游供应链及竞争格局变化...",
      status: "已完成",
      type: "行业分析",
      time: "1天前",
      author: "李华",
      stocks: ["NVDA", "AMD", "INTC", "QCOM"]
    },
    {
      id: 3,
      title: "新能源政策影响",
      description: "欧盟新能源补贴政策对相关公司的影响，重点关注政策实施时间和力度...",
      status: "已完成",
      type: "政策分析",
      time: "3天前",
      author: "王磊",
      stocks: ["BYD", "CATL", "LI", "NIO"]
    }
  ];

  const handleAnalyze = async () => {
    if (!searchQuery.trim()) return;
    
    setIsAnalyzing(true);
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsAnalyzing(false);
    navigate('/workspace');
  };

  const handleTopicClick = (topic: any) => {
    setSearchQuery(topic.title);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAnalyze();
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case "看涨": return "text-emerald-700 bg-emerald-50 border-emerald-200";
      case "看跌": return "text-red-700 bg-red-50 border-red-200";
      case "中性": return "text-slate-700 bg-slate-50 border-slate-200";
      default: return "text-slate-700 bg-slate-50 border-slate-200";
    }
  };

  const getStatusColor = (status: string) => {
    return status === "进行中" ? "bg-blue-50 text-blue-700 border-blue-200" : "bg-slate-50 text-slate-700 border-slate-200";
  };

  return (
    <div className="min-h-screen bg-slate-50">
      {/* 顶部导航 */}
      <header className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-slate-800">
                <BarChart3 className="h-5 w-5 text-white" />
              </div>
              <span className="font-semibold text-xl text-slate-800">Insightly</span>
            </div>
            
            <div className="flex items-center gap-8">
              <nav className="flex items-center gap-6">
                <a href="#" className="text-slate-600 hover:text-slate-800 transition-colors">
                  Community
                </a>
                <Button 
                  variant="ghost" 
                  onClick={() => navigate('/pricing')}
                  className="text-slate-600 hover:text-slate-800"
                >
                  Pricing
                </Button>
                <Button 
                  variant="ghost" 
                  onClick={() => navigate('/blog')}
                  className="text-slate-600 hover:text-slate-800"
                >
                  Blog
                </Button>
              </nav>
              <Button 
                variant="outline" 
                onClick={() => navigate('/workspace')}
                className="text-slate-600 border-slate-300 hover:bg-slate-50"
              >
                工作台
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero区域 */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        <div className="bg-white rounded-lg border border-slate-200 p-8 mb-8">
          <div className="max-w-4xl mx-auto text-center space-y-6">
            <div className="space-y-4">
              <h1 className="text-3xl font-bold text-slate-800 leading-tight">
                From event to edge in one search
              </h1>
              <p className="text-lg text-slate-600 max-w-3xl mx-auto">
                基于AI驱动的事件分析，快速验证事实、预测影响、构建投资逻辑
              </p>
            </div>

            {/* 搜索框 */}
            <div className="max-w-3xl mx-auto">
              <Card className="border-slate-200 shadow-sm">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3 p-3 bg-slate-50 rounded-lg">
                      <Search className="h-5 w-5 text-slate-400" />
                      <Input
                        placeholder="输入您想要分析的市场事件或投资信息..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        onKeyPress={handleKeyPress}
                        className="border-0 bg-transparent focus-visible:ring-0 shadow-none text-slate-800 placeholder:text-slate-500"
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setDeepVerify(!deepVerify)}
                          className={`h-8 px-4 rounded-full transition-all duration-200 ${
                            deepVerify 
                              ? 'bg-blue-100 text-blue-700 hover:bg-blue-200' 
                              : 'text-slate-600 hover:bg-slate-100'
                          }`}
                        >
                          深度核实
                        </Button>
                        <Button variant="ghost" size="sm" className="h-8 px-3 text-slate-600 hover:bg-slate-100">
                          <Paperclip className="h-4 w-4 mr-1" />
                          附件
                        </Button>
                      </div>
                      <Button 
                        onClick={handleAnalyze}
                        disabled={!searchQuery.trim() || isAnalyzing}
                        className="h-8 px-6 bg-slate-800 hover:bg-slate-700"
                      >
                        {isAnalyzing ? "分析中..." : "开始分析"}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* 我的工作区 */}
        <div className="bg-white/50 rounded-lg border border-slate-200 p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-slate-800">My Workspace</h2>
            <Button variant="ghost" size="sm" onClick={() => navigate('/workspace')} className="text-slate-600">
              查看全部
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {workspaceItems.map((item) => (
              <Card key={item.id} className="cursor-pointer hover:shadow-sm transition-shadow border-slate-200 h-fit">
                <CardContent className="p-4">
                  <div className="flex">
                    <div className="flex-1 min-w-0 pr-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge className={`text-xs px-2 py-1 border ${getStatusColor(item.status)}`}>
                          {item.status}
                        </Badge>
                        <Badge variant="outline" className="text-xs px-2 py-1 text-slate-600 border-slate-300">
                          {item.type}
                        </Badge>
                      </div>
                      <h3 className="font-medium text-slate-800 mb-1 text-sm line-clamp-1">
                        {item.title}
                      </h3>
                      <p className="text-xs text-slate-600 line-clamp-2 leading-relaxed">
                        {item.description}
                      </p>
                       <div className="flex items-center gap-1 mt-2 text-xs text-slate-500">
                         <Clock className="h-3 w-3" />
                         <span>{item.time}</span>
                       </div>
                    </div>
                    <div className="border-l border-slate-200 pl-3">
                       <div className="space-y-1">
                         {item.stocks.slice(0, 4).map((stock, index) => (
                           <div key={index} className="text-xs font-mono text-slate-700 bg-slate-50 px-2 py-1 rounded">
                             {stock}
                           </div>
                         ))}
                       </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* 热门分析话题 */}
        <div className="bg-white/50 rounded-lg border border-slate-200 p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-slate-800">
              热门分析话题
            </h3>
            <Button variant="ghost" size="sm" className="text-slate-600">
              查看更多
            </Button>
          </div>
          
          {/* 筛选器 - 下拉菜单 */}
          <div className="flex items-center gap-6 mb-6 pb-4 border-b border-slate-200">
            {/* 热门筛选 */}
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-slate-700 min-w-[32px]">热门</span>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8 px-3 text-xs text-slate-600 border-slate-300">
                    {selectedHotFilter} <ChevronDown className="ml-1 h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="bg-white">
                  {hotFilters.map((filter) => (
                    <DropdownMenuItem 
                      key={filter}
                      onClick={() => setSelectedHotFilter(filter)}
                      className={selectedHotFilter === filter ? "bg-slate-100" : ""}
                    >
                      {filter}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            
            {/* 时间筛选 */}
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-slate-700 min-w-[32px]">时间</span>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8 px-3 text-xs text-slate-600 border-slate-300">
                    {selectedTimeFilter} <ChevronDown className="ml-1 h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="bg-white">
                  {timeFilters.map((filter) => (
                    <DropdownMenuItem 
                      key={filter}
                      onClick={() => setSelectedTimeFilter(filter)}
                      className={selectedTimeFilter === filter ? "bg-slate-100" : ""}
                    >
                      {filter}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            
            {/* 行业筛选 */}
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-slate-700 min-w-[32px]">行业</span>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8 px-3 text-xs text-slate-600 border-slate-300">
                    {selectedIndustryFilter} <ChevronDown className="ml-1 h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="bg-white">
                  {industryFilters.map((filter) => (
                    <DropdownMenuItem 
                      key={filter}
                      onClick={() => setSelectedIndustryFilter(filter)}
                      className={selectedIndustryFilter === filter ? "bg-slate-100" : ""}
                    >
                      {filter}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            
            {/* 事件类型筛选 */}
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-slate-700 min-w-[64px]">事件类型</span>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8 px-3 text-xs text-slate-600 border-slate-300">
                    {selectedEventFilter} <ChevronDown className="ml-1 h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="bg-white">
                  {eventFilters.map((filter) => (
                    <DropdownMenuItem 
                      key={filter}
                      onClick={() => setSelectedEventFilter(filter)}
                      className={selectedEventFilter === filter ? "bg-slate-100" : ""}
                    >
                      {filter}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {(showMoreTopics ? filteredTopics : filteredTopics.slice(0, 9)).map((topic) => (
              <Card 
                key={topic.id}
                className="cursor-pointer hover:shadow-sm transition-shadow border-slate-200 h-fit"
                onClick={() => handleTopicClick(topic)}
              >
                <CardContent className="p-4">
                  <div className="flex">
                    <div className="flex-1 min-w-0 pr-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline" className="text-xs px-2 py-1 text-slate-600 border-slate-300">
                          {topic.type}
                        </Badge>
                        <Badge variant="outline" className="text-xs px-2 py-1 text-slate-600 border-slate-300">
                          {topic.category}
                        </Badge>
                      </div>
                      <h3 className="font-medium text-slate-800 mb-1 text-sm line-clamp-1">
                        {topic.title}
                      </h3>
                       <p className="text-xs text-slate-600 line-clamp-2 leading-relaxed">
                         {topic.description}
                       </p>
                       <div className="flex items-center gap-1 mt-2 text-xs text-slate-500">
                         <Clock className="h-3 w-3" />
                         <span>{topic.time}</span>
                         <span>•</span>
                         <span>{topic.author}</span>
                       </div>
                    </div>
                    <div className="border-l border-slate-200 pl-3">
                       <div className="space-y-1">
                         {topic.stocks.slice(0, 4).map((stock, index) => (
                            <div key={index} className="text-xs font-mono text-slate-700 bg-slate-50 px-2 py-1 rounded">
                              {stock}
                            </div>
                          ))}
                        </div>
                     </div>
                   </div>
                 </CardContent>
               </Card>
             ))}
           </div>
           
           {/* 显示更多按钮 */}
           {filteredTopics.length > 9 && (
             <div className="flex justify-center mt-6">
               <Button
                 variant="ghost"
                 size="sm"
                 onClick={() => setShowMoreTopics(!showMoreTopics)}
                 className="text-slate-600 hover:bg-slate-100 flex items-center gap-2"
               >
                 {showMoreTopics ? (
                   <>
                     收起 <ChevronDown className="h-4 w-4 rotate-180 transition-transform" />
                   </>
                 ) : (
                   <>
                     显示更多 <ChevronDown className="h-4 w-4 transition-transform" />
                   </>
                 )}
               </Button>
             </div>
           )}
        </div>
      </main>
    </div>
  );
}