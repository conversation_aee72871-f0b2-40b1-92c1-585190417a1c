import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { BarChart3, Clock, User, ArrowRight } from "lucide-react";
import { useNavigate } from "react-router-dom";

export default function Blog() {
  const navigate = useNavigate();

  const blogPosts = [
    {
      id: 1,
      title: "AI如何革命化投资分析：从数据到洞察",
      excerpt: "探索人工智能技术如何改变传统投资分析方法，提升决策效率和准确性。通过机器学习和深度分析，投资者能够...",
      category: "技术洞察",
      author: "张明",
      date: "2024年1月15日",
      readTime: "8分钟阅读",
      image: "/api/placeholder/400/240",
      featured: true
    },
    {
      id: 2,
      title: "2024年科技股投资趋势分析",
      excerpt: "深入分析2024年科技行业的投资机会与风险，包括AI、云计算、半导体等重点领域的前景预测...",
      category: "市场分析",
      author: "李华",
      date: "2024年1月12日",
      readTime: "6分钟阅读",
      image: "/api/placeholder/400/240",
      featured: false
    },
    {
      id: 3,
      title: "ESG投资：可持续发展的投资新范式",
      excerpt: "环境、社会和治理(ESG)因素正在重塑投资决策。了解如何将ESG原则融入投资策略，实现财务回报与社会责任的平衡...",
      category: "投资策略",
      author: "王磊",
      date: "2024年1月10日",
      readTime: "7分钟阅读",
      image: "/api/placeholder/400/240",
      featured: false
    },
    {
      id: 4,
      title: "量化交易入门指南：从策略到实践",
      excerpt: "量化交易正在成为现代投资的重要工具。本文将介绍量化交易的基本概念、策略设计和风险管理...",
      category: "交易技术",
      author: "陈小红",
      date: "2024年1月8日",
      readTime: "10分钟阅读",
      image: "/api/placeholder/400/240",
      featured: false
    },
    {
      id: 5,
      title: "新兴市场投资机会与挑战",
      excerpt: "新兴市场为投资者提供了巨大的增长潜力，但同时也伴随着独特的风险。探索如何在新兴市场中寻找投资机会...",
      category: "全球市场",
      author: "赵强",
      date: "2024年1月5日",
      readTime: "9分钟阅读",
      image: "/api/placeholder/400/240",
      featured: false
    },
    {
      id: 6,
      title: "加密货币市场分析：机遇与风险并存",
      excerpt: "加密货币市场的波动性为投资者带来了机遇和挑战。深入了解数字资产的投资策略和风险管理方法...",
      category: "数字资产",
      author: "孙丽",
      date: "2024年1月3日",
      readTime: "8分钟阅读",
      image: "/api/placeholder/400/240",
      featured: false
    }
  ];

  const categories = ["全部", "技术洞察", "市场分析", "投资策略", "交易技术", "全球市场", "数字资产"];
  
  const featuredPost = blogPosts.find(post => post.featured);
  const regularPosts = blogPosts.filter(post => !post.featured);

  return (
    <div className="min-h-screen bg-slate-50">
      {/* 导航栏 */}
      <header className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-slate-800">
                <BarChart3 className="h-5 w-5 text-white" />
              </div>
              <Button 
                variant="ghost" 
                onClick={() => navigate('/')}
                className="font-semibold text-xl text-slate-800 hover:bg-transparent"
              >
                专业投资分析AI
              </Button>
            </div>
            
            <div className="flex items-center gap-8">
              <nav className="flex items-center gap-6">
                <a href="#" className="text-slate-600 hover:text-slate-800 transition-colors">
                  Community
                </a>
                <Button 
                  variant="ghost" 
                  onClick={() => navigate('/pricing')}
                  className="text-slate-600 hover:text-slate-800"
                >
                  Pricing
                </Button>
                <span className="text-slate-800 font-medium">Blog</span>
              </nav>
              <Button 
                variant="outline" 
                onClick={() => navigate('/workspace')}
                className="text-slate-600 border-slate-300 hover:bg-slate-50"
              >
                工作台
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-6 py-16">
        {/* 头部 */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-slate-800 mb-4">
            投资洞察博客
          </h1>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            深入分析市场趋势，分享投资智慧，助您做出更明智的投资决策
          </p>
        </div>

        {/* 特色文章 */}
        {featuredPost && (
          <div className="mb-16">
            <h2 className="text-2xl font-semibold text-slate-800 mb-6">特色文章</h2>
            <Card className="overflow-hidden border-slate-200 hover:shadow-lg transition-shadow cursor-pointer">
              <div className="md:flex">
                <div className="md:w-1/2">
                  <div className="h-64 bg-slate-200 flex items-center justify-center">
                    <span className="text-slate-500">特色文章图片</span>
                  </div>
                </div>
                <div className="md:w-1/2 p-8">
                  <Badge className="mb-4 bg-slate-800 text-white">
                    {featuredPost.category}
                  </Badge>
                  <h3 className="text-2xl font-bold text-slate-800 mb-4 line-clamp-2">
                    {featuredPost.title}
                  </h3>
                  <p className="text-slate-600 mb-6 line-clamp-3">
                    {featuredPost.excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 text-sm text-slate-500">
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        <span>{featuredPost.author}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        <span>{featuredPost.readTime}</span>
                      </div>
                      <span>{featuredPost.date}</span>
                    </div>
                    <Button variant="ghost" size="sm" className="text-slate-600">
                      阅读更多 <ArrowRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* 分类筛选 */}
        <div className="flex gap-2 mb-8 flex-wrap">
          {categories.map((category) => (
            <Button
              key={category}
              variant="outline"
              size="sm"
              className="text-xs h-8 px-3 rounded-full text-slate-600 border-slate-300 hover:bg-slate-50"
            >
              {category}
            </Button>
          ))}
        </div>

        {/* 文章列表 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {regularPosts.map((post) => (
            <Card key={post.id} className="border-slate-200 hover:shadow-lg transition-shadow cursor-pointer group">
              <div className="h-48 bg-slate-200 flex items-center justify-center rounded-t-lg">
                <span className="text-slate-500">文章图片</span>
              </div>
              <CardHeader className="pb-3">
                <Badge variant="outline" className="w-fit mb-2 text-xs text-slate-600 border-slate-300">
                  {post.category}
                </Badge>
                <h3 className="font-semibold text-slate-800 line-clamp-2 group-hover:text-slate-600 transition-colors">
                  {post.title}
                </h3>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-slate-600 line-clamp-3 mb-4">
                  {post.excerpt}
                </p>
                <div className="flex items-center justify-between text-xs text-slate-500">
                  <div className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    <span>{post.author}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{post.readTime}</span>
                  </div>
                </div>
                <div className="text-xs text-slate-500 mt-2">
                  {post.date}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 加载更多 */}
        <div className="text-center mt-12">
          <Button variant="outline" className="text-slate-600 border-slate-300 hover:bg-slate-50">
            加载更多文章
          </Button>
        </div>
      </main>
    </div>
  );
}