import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/layout/AppSidebar";
import { TopBar } from "@/components/layout/TopBar";
import { BottomDrawer } from "@/components/layout/BottomDrawer";

interface MainLayoutProps {
  children: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex flex-col flex-1 md:ml-56">
          <TopBar />
          <main className="flex-1 p-6">
            {children}
          </main>
          <BottomDrawer />
        </div>
      </div>
    </SidebarProvider>
  );
}