import { useState, useEffect } from "react";
import { Coins, Zap } from "lucide-react";

export function PointsDisplay() {
  const [points, setPoints] = useState(1850);
  const [animatedPoints, setAnimatedPoints] = useState(1850);

  // Animate points changes
  useEffect(() => {
    const duration = 1000;
    const steps = 60;
    const increment = (points - animatedPoints) / steps;
    
    if (increment !== 0) {
      const timer = setInterval(() => {
        setAnimatedPoints(prev => {
          const next = prev + increment;
          if (
            (increment > 0 && next >= points) ||
            (increment < 0 && next <= points)
          ) {
            clearInterval(timer);
            return points;
          }
          return next;
        });
      }, duration / steps);
      
      return () => clearInterval(timer);
    }
  }, [points, animatedPoints]);

  return (
    <div className="flex items-center gap-3 bg-gradient-card px-4 py-2 rounded-lg border shadow-card">
      <div className="flex items-center gap-2">
        <Coins className="h-4 w-4 text-warning" />
        <span className="font-mono text-lg font-bold">
          {Math.round(animatedPoints).toLocaleString()}
        </span>
        <span className="text-sm text-muted-foreground">积分</span>
      </div>
      
      <div className="h-4 w-px bg-border" />
      
      <div className="flex items-center gap-1 text-sm text-muted-foreground">
        <Zap className="h-3 w-3" />
        <span>+200/日</span>
      </div>
    </div>
  );
}