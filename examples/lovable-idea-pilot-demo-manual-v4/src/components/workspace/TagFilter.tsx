import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { X, ChevronDown, Filter } from "lucide-react";

interface TagFilterProps {
  allTags: string[];
  selectedTags: string[];
  onTagChange: (tags: string[]) => void;
}

export function TagFilter({ allTags, selectedTags, onTagChange }: TagFilterProps) {
  const [selectedSingleTag, setSelectedSingleTag] = useState("全部");

  const handleTagSelect = (tag: string) => {
    setSelectedSingleTag(tag);
    if (tag === "全部") {
      onTagChange([]);
    } else {
      onTagChange([tag]);
    }
  };

  const clearAllTags = () => {
    setSelectedSingleTag("全部");
    onTagChange([]);
  };

  // 合并"全部"选项和标签选项
  const allOptions = ["全部", ...allTags];

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm font-medium text-slate-700 min-w-[48px]">标签筛选</span>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="h-8 px-3 text-xs text-slate-600 border-slate-300">
            {selectedSingleTag} <ChevronDown className="ml-1 h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="bg-white min-w-[120px] max-h-[300px] overflow-y-auto">
          {allOptions.map((tag) => (
            <DropdownMenuItem 
              key={tag}
              onClick={() => handleTagSelect(tag)}
              className={selectedSingleTag === tag ? "bg-slate-100" : ""}
            >
              {tag}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
      
      {selectedSingleTag !== "全部" && (
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={clearAllTags}
          className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
        >
          <X className="h-3 w-3 mr-1" />
          清除
        </Button>
      )}
    </div>
  );
}