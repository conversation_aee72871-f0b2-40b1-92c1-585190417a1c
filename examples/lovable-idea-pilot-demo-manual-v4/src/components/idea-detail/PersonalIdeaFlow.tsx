import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { User, Plus, Edit3, Check, X, Trash2 } from "lucide-react";

interface PersonalIdeaCard {
  id: string;
  content: string;
  createdAt: string;
  isEditing?: boolean;
}

interface PersonalIdeaFlowProps {
  ideaId: number;
}

export function PersonalIdeaFlow({ ideaId }: PersonalIdeaFlowProps) {
  const [ideas, setIdeas] = useState<PersonalIdeaCard[]>([
    {
      id: "personal-1",
      content: "从投资角度看，H200的发布可能会推动整个AI基础设施的升级周期。不仅仅是云服务商，传统企业也会加速AI转型。",
      createdAt: "2小时前"
    },
    {
      id: "personal-2", 
      content: "需要关注竞争对手的反应。AMD的MI300X和Intel的Gaudi3都在虎视眈眈，价格战可能即将开始。",
      createdAt: "1小时前"
    }
  ]);

  const [newIdeaContent, setNewIdeaContent] = useState("");
  const [isAdding, setIsAdding] = useState(false);

  const addNewIdea = () => {
    if (newIdeaContent.trim()) {
      const newIdea: PersonalIdeaCard = {
        id: `personal-${Date.now()}`,
        content: newIdeaContent.trim(),
        createdAt: "刚刚"
      };
      setIdeas(prev => [...prev, newIdea]);
      setNewIdeaContent("");
      setIsAdding(false);
    }
  };

  const startEdit = (id: string) => {
    setIdeas(prev => prev.map(idea => 
      idea.id === id ? { ...idea, isEditing: true } : idea
    ));
  };

  const saveEdit = (id: string, newContent: string) => {
    setIdeas(prev => prev.map(idea => 
      idea.id === id 
        ? { ...idea, content: newContent, isEditing: false }
        : idea
    ));
  };

  const cancelEdit = (id: string) => {
    setIdeas(prev => prev.map(idea => 
      idea.id === id ? { ...idea, isEditing: false } : idea
    ));
  };

  const deleteIdea = (id: string) => {
    setIdeas(prev => prev.filter(idea => idea.id !== id));
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <User className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">个人想法流</h3>
        </div>
        <Button 
          size="sm" 
          onClick={() => setIsAdding(true)}
          disabled={isAdding}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          新建想法
        </Button>
      </div>

      <div className="space-y-3">
        {/* 新建想法卡片 */}
        {isAdding && (
          <Card className="border-primary/50 shadow-sm">
            <CardContent className="p-4">
              <div className="space-y-3">
                <Textarea
                  placeholder="记录你的想法..."
                  value={newIdeaContent}
                  onChange={(e) => setNewIdeaContent(e.target.value)}
                  className="min-h-[80px] resize-none text-sm"
                />
                <div className="flex items-center gap-2">
                  <Button size="sm" onClick={addNewIdea}>
                    <Check className="h-4 w-4 mr-2" />
                    保存
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => {
                      setIsAdding(false);
                      setNewIdeaContent("");
                    }}
                  >
                    <X className="h-4 w-4 mr-2" />
                    取消
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 现有想法卡片 */}
        {ideas.map((idea) => (
          <IdeaCard 
            key={idea.id} 
            idea={idea}
            onEdit={startEdit}
            onSave={saveEdit}
            onCancel={cancelEdit}
            onDelete={deleteIdea}
          />
        ))}
      </div>
    </div>
  );
}

interface IdeaCardProps {
  idea: PersonalIdeaCard;
  onEdit: (id: string) => void;
  onSave: (id: string, content: string) => void;
  onCancel: (id: string) => void;
  onDelete: (id: string) => void;
}

function IdeaCard({ idea, onEdit, onSave, onCancel, onDelete }: IdeaCardProps) {
  const [editContent, setEditContent] = useState(idea.content);

  return (
    <Card className="transition-all duration-200 hover:shadow-md">
      <CardContent className="p-4">
        {idea.isEditing ? (
          <div className="space-y-3">
            <Textarea
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              className="min-h-[80px] resize-none text-sm"
            />
            <div className="flex items-center gap-2">
              <Button size="sm" onClick={() => onSave(idea.id, editContent)}>
                <Check className="h-4 w-4 mr-2" />
                保存
              </Button>
              <Button 
                size="sm" 
                variant="outline" 
                onClick={() => {
                  setEditContent(idea.content);
                  onCancel(idea.id);
                }}
              >
                <X className="h-4 w-4 mr-2" />
                取消
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            <p className="text-sm text-muted-foreground leading-relaxed">
              {idea.content}
            </p>
            
            <div className="flex items-center justify-between">
              <Badge variant="outline" className="text-xs">
                {idea.createdAt}
              </Badge>
              
              <div className="flex items-center gap-1">
                <Button 
                  size="sm" 
                  variant="ghost" 
                  className="h-7 px-2 text-xs"
                  onClick={() => onEdit(idea.id)}
                >
                  <Edit3 className="h-3 w-3 mr-1" />
                  编辑
                </Button>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  className="h-7 px-2 text-xs text-destructive hover:text-destructive"
                  onClick={() => onDelete(idea.id)}
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  删除
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}