import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { TrendingUp, TrendingDown, RefreshCw, Star, Eye, Settings, Save, BarChart3 } from "lucide-react";

interface StockRecommendationsProps {
  ideaId: number;
  isReadOnly?: boolean;
}

interface StockRecommendation {
  symbol: string;
  name: string;
  type: "bullish" | "bearish";
  price: string;
  change: string;
  reasoning: string;
  shortReasoning: string;
  confidence: "高" | "中" | "低";
  isFollowing: boolean;
  financials: {
    ttmPE: string;
    ntmPE: string;
    ps: string;
    revenue: string;
    netIncome: string;
    profitMargin: string;
    revenueGrowth: string;
  };
}

export function StockRecommendations({ ideaId, isReadOnly = false }: StockRecommendationsProps) {
  const navigate = useNavigate();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [preferences, setPreferences] = useState({
    sectors: [] as string[],
    riskLevel: [] as string[],
    timeHorizon: [] as string[],
    specialRequirements: [] as string[]
  });
  const [isPreferencesDialogOpen, setIsPreferencesDialogOpen] = useState(false);
  const [tempPreferences, setTempPreferences] = useState(preferences);
  const [selectedStocks, setSelectedStocks] = useState<string[]>([]);
  const [analysisCards, setAnalysisCards] = useState<{[key: string]: any}>({});
  
  const [recommendations, setRecommendations] = useState<StockRecommendation[]>([
    {
      symbol: "NVDA",
      name: "英伟达",
      type: "bullish",
      price: "$722.48",
      change: "+2.3%",
      reasoning: "H200芯片的推出将显著提升英伟达在AI芯片市场的领导地位。预计H200的高内存容量和处理能力将吸引更多云服务商采购，推动营收增长。微软、OpenAI等重要客户的大额订单已经确认，为未来几个季度的业绩提供强力支撑。技术护城河进一步加深。",
      shortReasoning: "H200芯片性能大幅提升，微软OpenAI等大客户确认订单。AI芯片领导地位进一步巩固，营收增长强劲。",
      confidence: "高",
      isFollowing: true,
      financials: {
        ttmPE: "65.2",
        ntmPE: "45.8",
        ps: "22.1",
        revenue: "$601B",
        netIncome: "$298B",
        profitMargin: "49.6%",
        revenueGrowth: "+126%"
      }
    },
    {
      symbol: "TSM",
      name: "台积电",
      type: "bullish",
      price: "$97.85",
      change: "+1.8%",
      reasoning: "作为H200芯片的独家代工厂，台积电将直接受益于英伟达芯片需求的爆发式增长。先进制程技术的垄断地位确保了稳定的订单来源和高毛利率。随着AI芯片市场的扩张，台积电的产能利用率和定价能力都将得到提升。",
      shortReasoning: "作为H200独家代工厂，直接受益AI芯片需求爆发。先进制程垄断地位确保高毛利率，产能利用率大幅提升。",
      confidence: "高",
      isFollowing: false,
      financials: {
        ttmPE: "25.4",
        ntmPE: "19.8",
        ps: "7.2",
        revenue: "$75.9B",
        netIncome: "$26.9B",
        profitMargin: "35.4%",
        revenueGrowth: "+9.8%"
      }
    },
    {
      symbol: "MSFT",
      name: "微软",
      type: "bullish",
      price: "$378.24",
      change: "+0.9%",
      reasoning: "微软Azure率先部署H200芯片，强化了其在云计算AI服务领域的竞争优势。H200的性能提升将降低AI训练和推理成本，提高Azure的盈利能力。与OpenAI的深度合作关系确保了在AGI竞赛中的领先地位，长期价值显著。",
      shortReasoning: "Azure率先部署H200强化AI竞争优势，降低训练成本提升盈利。与OpenAI深度合作确保AGI竞赛领先地位。",
      confidence: "高",
      isFollowing: true,
      financials: {
        ttmPE: "34.1",
        ntmPE: "28.5",
        ps: "12.8",
        revenue: "$211B",
        netIncome: "$72.4B",
        profitMargin: "34.3%",
        revenueGrowth: "+15.7%"
      }
    },
    {
      symbol: "AMZN",
      name: "亚马逊",
      type: "bullish",
      price: "$151.94",
      change: "+1.2%",
      reasoning: "AWS作为云计算市场领导者，H200芯片的部署将增强其AI/ML服务的竞争力。亚马逊在自研芯片方面的投入可能受到英伟达技术进步的启发，推动内部创新。电商业务也可能通过更强大的AI能力获得效率提升。",
      shortReasoning: "AWS领导地位受益H200部署增强AI服务竞争力。自研芯片投入获启发，电商业务AI效率提升潜力巨大。",
      confidence: "中",
      isFollowing: false,
      financials: {
        ttmPE: "45.2",
        ntmPE: "38.1",
        ps: "2.8",
        revenue: "$574B",
        netIncome: "$15.3B",
        profitMargin: "2.7%",
        revenueGrowth: "+11.2%"
      }
    },
    {
      symbol: "GOOGL",
      name: "谷歌",
      type: "bullish",
      price: "$138.21",
      change: "+0.7%",
      reasoning: "谷歌云平台GCP可能通过部署H200芯片提升AI服务能力，与AWS和Azure竞争。Bard和其他AI产品的性能改进将增强用户体验。广告业务可能通过更精准的AI算法获得收入增长。自研TPU与H200的互补性可能产生协同效应。",
      shortReasoning: "GCP部署H200提升AI服务与巨头竞争，Bard性能改进增强体验。广告业务AI算法更精准，TPU协同效应明显。",
      confidence: "中",
      isFollowing: false,
      financials: {
        ttmPE: "24.6",
        ntmPE: "20.3",
        ps: "5.4",
        revenue: "$307B",
        netIncome: "$73.8B",
        profitMargin: "24.0%",
        revenueGrowth: "+13.8%"
      }
    },
    {
      symbol: "AMD",
      name: "AMD",
      type: "bearish",
      price: "$136.78",
      change: "-1.5%",
      reasoning: "H200芯片的强劲性能进一步拉大了与AMD MI300X的差距，可能导致市场份额的进一步流失。虽然AMD在价格方面具有优势，但在高端AI训练市场，性能往往比价格更重要。客户可能会推迟AMD芯片的采购决策。",
      shortReasoning: "H200性能优势拉大与MI300X差距，高端AI市场性能比价格重要。市场份额流失风险增加，客户采购决策推迟。",
      confidence: "中",
      isFollowing: false,
      financials: {
        ttmPE: "163.4",
        ntmPE: "42.1",
        ps: "8.9",
        revenue: "$22.7B",
        netIncome: "$854M",
        profitMargin: "3.8%",
        revenueGrowth: "+9.0%"
      }
    },
    {
      symbol: "INTC",
      name: "英特尔",
      type: "bearish",
      price: "$21.84",
      change: "-2.1%",
      reasoning: "英特尔在AI芯片领域已经明显落后，H200的发布进一步凸显了其技术劣势。Gaudi系列芯片难以与英伟达竞争，市场定位尴尬。传统CPU业务也面临来自AI加速计算的冲击，转型压力巨大，短期内难以扭转颓势。",
      shortReasoning: "AI芯片领域明显落后，H200发布凸显技术劣势。Gaudi系列竞争力不足，传统CPU业务受AI冲击，转型压力巨大。",
      confidence: "高",
      isFollowing: true,
      financials: {
        ttmPE: "N/A",
        ntmPE: "87.2",
        ps: "2.1",
        revenue: "$63.1B",
        netIncome: "-$1.6B",
        profitMargin: "-2.5%",
        revenueGrowth: "-0.9%"
      }
    },
    {
      symbol: "CRM",
      name: "Salesforce",
      type: "bearish",
      price: "$249.63",
      change: "-0.8%",
      reasoning: "H200等先进AI芯片的普及可能降低企业级AI应用的开发门槛，增加Salesforce面临的竞争压力。云计算巨头的AI能力增强可能蚕食CRM市场份额。虽然Salesforce在AI集成方面有所努力，但相比专业AI公司仍存在差距。",
      shortReasoning: "H200普及降低企业AI应用门槛，竞争压力增加。云巨头AI能力增强蚕食CRM份额，与专业AI公司存在差距。",
      confidence: "低",
      isFollowing: false,
      financials: {
        ttmPE: "58.2",
        ntmPE: "35.4",
        ps: "8.4",
        revenue: "$31.4B",
        netIncome: "$4.1B",
        profitMargin: "13.1%",
        revenueGrowth: "+11.3%"
      }
    },
    {
      symbol: "ORCL",
      name: "甲骨文",
      type: "bearish",
      price: "$134.27",
      change: "-1.2%",
      reasoning: "传统数据库厂商面临AI原生应用的冲击，H200等芯片的普及加速了这一趋势。Oracle的云计算业务相对较小，难以与主要云服务商竞争。AI时代的数据处理模式变化可能削弱传统数据库的价值，需要重大战略调整。",
      shortReasoning: "传统数据库面临AI原生应用冲击，H200普及加速趋势。云业务规模小竞争困难，数据处理模式变化削弱价值。",
      confidence: "中",
      isFollowing: false,
      financials: {
        ttmPE: "33.8",
        ntmPE: "24.1",
        ps: "9.2",
        revenue: "$49.9B",
        netIncome: "$11.5B",
        profitMargin: "23.0%",
        revenueGrowth: "+6.2%"
      }
    },
    {
      symbol: "IBM",
      name: "IBM",
      type: "bearish",
      price: "$210.45",
      change: "-0.5%",
      reasoning: "IBM在AI芯片和云计算领域的投入相对不足，H200等先进技术的普及可能进一步拉大其与领先厂商的差距。Watson等AI产品的市场表现不及预期，企业服务业务面临新兴AI公司的竞争。转型步伐相对缓慢，可能错失AI红利。",
      shortReasoning: "AI芯片云计算投入不足，H200普及拉大与领先厂商差距。Watson表现不及预期，转型缓慢错失AI红利风险。",
      confidence: "中",
      isFollowing: false,
      financials: {
        ttmPE: "22.4",
        ntmPE: "18.9",
        ps: "2.8",
        revenue: "$57.4B",
        netIncome: "$5.8B",
        profitMargin: "10.1%",
        revenueGrowth: "-4.6%"
      }
    }
  ]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // 模拟API调用，可以基于偏好设置生成不同的推荐
    setTimeout(() => {
      setIsRefreshing(false);
    }, 2000);
  };

  const handleSavePreferences = () => {
    setPreferences(tempPreferences);
    setIsPreferencesDialogOpen(false);
    // 这里可以触发重新生成推荐
    handleRefresh();
  };

  const handlePreferencesChange = (field: keyof typeof tempPreferences, value: string, checked: boolean) => {
    setTempPreferences(prev => ({
      ...prev,
      [field]: checked 
        ? [...prev[field], value]
        : prev[field].filter(item => item !== value)
    }));
  };

  const handleStockSelection = (symbol: string, checked: boolean) => {
    setSelectedStocks(prev => 
      checked 
        ? [...prev, symbol]
        : prev.filter(s => s !== symbol)
    );
  };

  const generateAnalysis = async () => {
    if (selectedStocks.length === 0) return;
    
    // 模拟生成深度分析
    const newAnalysisCards: {[key: string]: any} = {};
    selectedStocks.forEach(symbol => {
      const stock = recommendations.find(r => r.symbol === symbol);
      if (stock) {
        newAnalysisCards[symbol] = {
          symbol,
          name: stock.name,
          deepAnalysis: `对${stock.name}(${symbol})的深度分析：基于当前市场环境和技术发展趋势，该股票在未来6-12个月内具有较强的投资价值。从技术面分析，该股票突破了关键阻力位，成交量放大确认了突破的有效性。从基本面角度，公司在核心业务领域的竞争优势持续强化，预计未来几个季度的业绩将保持稳定增长。`,
          riskFactors: [
            "市场整体波动性增加的风险",
            "行业竞争加剧可能影响毛利率",
            "宏观经济政策变化的不确定性"
          ],
          targetPrice: stock.type === "bullish" ? "目标价位：上调15-20%" : "目标价位：下调10-15%",
          timeframe: "投资期限：6-12个月",
          confidence: stock.confidence
        };
      }
    });
    
    setAnalysisCards(prev => ({ ...prev, ...newAnalysisCards }));
  };

  const hasPreferences = preferences.sectors.length > 0 || preferences.riskLevel.length > 0 || preferences.timeHorizon.length > 0 || preferences.specialRequirements.length > 0;

  const handleFollowToggle = (symbol: string) => {
    setRecommendations(prev => 
      prev.map(stock => 
        stock.symbol === symbol 
          ? { ...stock, isFollowing: !stock.isFollowing }
          : stock
      )
    );
  };

  const handleStockClick = (symbol: string) => {
    // 导航到股票详情页面，显示与当前idea的联动分析
    navigate(`/stock/${symbol}?ideaId=${ideaId}`);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Star className="h-5 w-5" />
            股票推荐
            {hasPreferences && (
              <Badge variant="secondary" className="text-xs ml-2">
                已自定义
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            {selectedStocks.length > 0 && (
              <Button 
                size="sm" 
                variant="default"
                onClick={generateAnalysis}
                className="h-8 px-3"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                深度分析({selectedStocks.length})
              </Button>
            )}
            {!isReadOnly && (
              <>
                <Dialog open={isPreferencesDialogOpen} onOpenChange={setIsPreferencesDialogOpen}>
                  <DialogTrigger asChild>
                    <Button 
                      size="sm" 
                      variant="outline"
                      className="h-8 px-3"
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      偏好方向
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                      <DialogTitle className="flex items-center gap-2">
                        <Settings className="h-5 w-5" />
                        自定义股票推荐偏好
                      </DialogTitle>
                    </DialogHeader>
                    <div className="space-y-6 py-4">
                      <div>
                        <label className="text-sm font-medium mb-3 block">关注行业/板块</label>
                        <div className="grid grid-cols-2 gap-3">
                          {["人工智能", "半导体", "云计算", "新能源", "生物医药", "金融科技", "消费电子", "新材料"].map((sector) => (
                            <div key={sector} className="flex items-center space-x-2">
                              <Checkbox
                                id={`sector-${sector}`}
                                checked={tempPreferences.sectors.includes(sector)}
                                onCheckedChange={(checked) => handlePreferencesChange('sectors', sector, checked as boolean)}
                              />
                              <label htmlFor={`sector-${sector}`} className="text-sm">{sector}</label>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <label className="text-sm font-medium mb-3 block">风险偏好</label>
                        <div className="space-y-2">
                          {["保守型（大盘蓝筹）", "平衡型（中等波动）", "激进型（高成长股）"].map((risk) => (
                            <div key={risk} className="flex items-center space-x-2">
                              <Checkbox
                                id={`risk-${risk}`}
                                checked={tempPreferences.riskLevel.includes(risk)}
                                onCheckedChange={(checked) => handlePreferencesChange('riskLevel', risk, checked as boolean)}
                              />
                              <label htmlFor={`risk-${risk}`} className="text-sm">{risk}</label>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <label className="text-sm font-medium mb-3 block">投资时间期限</label>
                        <div className="space-y-2">
                          {["短期（1-3个月）", "中期（3-12个月）", "长期（1年以上）"].map((time) => (
                            <div key={time} className="flex items-center space-x-2">
                              <Checkbox
                                id={`time-${time}`}
                                checked={tempPreferences.timeHorizon.includes(time)}
                                onCheckedChange={(checked) => handlePreferencesChange('timeHorizon', time, checked as boolean)}
                              />
                              <label htmlFor={`time-${time}`} className="text-sm">{time}</label>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <label className="text-sm font-medium mb-3 block">特殊要求</label>
                        <div className="grid grid-cols-2 gap-3">
                          {["ESG投资", "高分红", "避免中概股", "只考虑美股", "大市值股票", "成长股优先"].map((req) => (
                            <div key={req} className="flex items-center space-x-2">
                              <Checkbox
                                id={`req-${req}`}
                                checked={tempPreferences.specialRequirements.includes(req)}
                                onCheckedChange={(checked) => handlePreferencesChange('specialRequirements', req, checked as boolean)}
                              />
                              <label htmlFor={`req-${req}`} className="text-sm">{req}</label>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex justify-end gap-2 pt-4">
                      <Button 
                        variant="outline" 
                        onClick={() => {
                          setTempPreferences(preferences);
                          setIsPreferencesDialogOpen(false);
                        }}
                      >
                        取消
                      </Button>
                      <Button onClick={handleSavePreferences} className="flex items-center gap-2">
                        <Save className="h-4 w-4" />
                        保存并应用
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                  className="h-8 px-3"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                  换一批
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">选择</TableHead>
              <TableHead className="w-20">类型</TableHead>
              <TableHead className="w-24">股票</TableHead>
              <TableHead className="w-24">价格</TableHead>
              <TableHead className="w-20">涨跌幅</TableHead>
              <TableHead className="w-32">估值倍数</TableHead>
              <TableHead className="w-32">财务指标</TableHead>
              <TableHead className="w-80">推荐理由</TableHead>
              <TableHead className="w-20">关注</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {recommendations.map((stock) => (
              <TableRow 
                key={stock.symbol}
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleStockClick(stock.symbol)}
              >
                <TableCell onClick={(e) => e.stopPropagation()}>
                  <Checkbox
                    checked={selectedStocks.includes(stock.symbol)}
                    onCheckedChange={(checked) => handleStockSelection(stock.symbol, checked as boolean)}
                  />
                </TableCell>
                <TableCell>
                  {stock.type === "bullish" ? (
                    <TrendingUp className="h-4 w-4 text-success" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-destructive" />
                  )}
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium text-sm">{stock.symbol}</div>
                    <div className="text-xs text-muted-foreground">{stock.name}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm font-medium">{stock.price}</div>
                </TableCell>
                <TableCell>
                  <div className={`text-sm font-medium ${stock.change.startsWith('+') ? 'text-success' : 'text-destructive'}`}>
                    {stock.change}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-xs space-y-0.5">
                    <div>TTM P/E: {stock.financials.ttmPE}</div>
                    <div>NTM P/E: {stock.financials.ntmPE}</div>
                    <div>P/S: {stock.financials.ps}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-xs space-y-0.5">
                    <div>收入: {stock.financials.revenue}</div>
                    <div className={parseFloat(stock.financials.profitMargin) < 0 ? 'text-destructive' : ''}>
                      利润率: {stock.financials.profitMargin}
                    </div>
                    <div className={`${stock.financials.revenueGrowth.startsWith('+') ? 'text-success' : stock.financials.revenueGrowth.startsWith('-') ? 'text-destructive' : ''}`}>
                      增速: {stock.financials.revenueGrowth}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-xs text-muted-foreground">
                    {stock.shortReasoning}
                  </div>
                </TableCell>
                <TableCell>
                  <Switch
                    checked={stock.isFollowing}
                    onCheckedChange={() => handleFollowToggle(stock.symbol)}
                    onClick={(e) => e.stopPropagation()}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        
        {/* 深度分析卡片 */}
        {Object.keys(analysisCards).length > 0 && (
          <div className="mt-6 space-y-4">
            <h3 className="text-lg font-semibold">深度分析报告</h3>
            {Object.values(analysisCards).map((analysis: any) => (
              <Card key={analysis.symbol} className="border-l-4 border-l-primary">
                <CardHeader>
                  <CardTitle className="text-base flex items-center justify-between">
                    <span>{analysis.name} ({analysis.symbol}) - 深度分析</span>
                    <Badge variant={analysis.confidence === "高" ? "default" : analysis.confidence === "中" ? "secondary" : "outline"}>
                      信心度: {analysis.confidence}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">投资分析</h4>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {analysis.deepAnalysis}
                      </p>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium mb-2">主要风险因素</h4>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {analysis.riskFactors.map((risk: string, index: number) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="text-destructive mt-1">•</span>
                              {risk}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="text-sm">
                          <span className="font-medium">目标价位:</span>
                          <span className="ml-2 text-muted-foreground">{analysis.targetPrice}</span>
                        </div>
                        <div className="text-sm">
                          <span className="font-medium">投资期限:</span>
                          <span className="ml-2 text-muted-foreground">{analysis.timeframe}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}