import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Brain, Edit3, Check, X, AlertCircle } from "lucide-react";

interface ExtendedThoughtsProps {
  ideaId: number;
}

export function ExtendedThoughts({ ideaId }: ExtendedThoughtsProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [thoughts, setThoughts] = useState("H200芯片的发布不仅仅是性能的提升，更重要的是它标志着AI计算进入了一个新的阶段。内存容量的大幅提升意味着可以训练更大规模的模型，这将推动AGI的发展进程。");
  const [editingThoughts, setEditingThoughts] = useState(thoughts);
  const [factChecks] = useState([
    { fact: "H200内存容量确实从80GB提升至141GB", status: "verified" as const },
    { fact: "性能提升90%的数据来源于NVIDIA官方基准测试", status: "verified" as const },
    { fact: "AGI发展时间表尚未明确", status: "caution" as const }
  ]);

  const handleSave = () => {
    setThoughts(editingThoughts);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditingThoughts(thoughts);
    setIsEditing(false);
  };

  return (
    <div className="space-y-3">
      <div className="mb-3">
        <h4 className="text-base font-semibold flex items-center gap-2 mb-2">
          <Brain className="h-4 w-4" />
          Ideas延展思考
        </h4>
        {isEditing ? (
          <div className="space-y-3">
            <Textarea
              value={editingThoughts}
              onChange={(e) => setEditingThoughts(e.target.value)}
              placeholder="添加您对这个Ideas的延展思考..."
              className="min-h-[100px] text-sm"
            />
            <div className="flex gap-2">
              <Button size="sm" onClick={handleSave}>
                <Check className="h-4 w-4 mr-2" />
                保存
              </Button>
              <Button size="sm" variant="outline" onClick={handleCancel}>
                <X className="h-4 w-4 mr-2" />
                取消
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="text-sm text-muted-foreground leading-relaxed">
              {thoughts}
            </div>
            <Button size="sm" variant="outline" onClick={() => setIsEditing(true)}>
              <Edit3 className="h-4 w-4 mr-2" />
              记录新思考
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}