import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, <PERSON>etContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { RefreshCw, Edit3, Heart, MessageCircle, ChevronDown, ChevronUp, User, TrendingUp, ExternalLink, FileText } from "lucide-react";
interface FutureScenario {
  id: string;
  title: string;
  content: string;
  probability: number;
  possibility: string;
  timeframe: string;
  liked: boolean;
  references: Array<{
    id: string;
    title: string;
    source: string;
    url: string;
    time: string;
  }>;
}
interface UserComment {
  id: string;
  userName: string;
  userAvatar?: string;
  content: string;
  time: string;
  likes: number;
  isLiked: boolean;
}
interface AiThinkingFlowProps {
  ideaId: number;
  isReadOnly?: boolean;
}
export function AiThinkingFlow({
  ideaId,
  isReadOnly = false
}: AiThinkingFlowProps) {
  const [scenarios, setScenarios] = useState<FutureScenario[]>([{
    id: "scenario-1",
    title: "技术领先持续巩固",
    content: "英伟达H200采用先进的HBM3e内存技术，提供了比前代产品近2倍的内存带宽提升，这将显著改善大型模型训练和推理的效率。从技术角度看，H200的架构优化针对Transformer模型进行了深度定制，在处理注意力机制时的计算效率远超竞品。考虑到当前AI应用对算力的需求爆发式增长，特别是在自动驾驶、医疗影像分析和科学计算等领域，H200的性能提升来得正是时候。英伟达在CUDA生态系统上的深度积累也为H200提供了强大的软件支持，让开发者能够快速迁移现有应用。预计在未来6个月内，主要云服务提供商将大规模采购H200，这将推动英伟达在数据中心GPU市场的份额进一步提升至80%以上。",
    probability: 85,
    possibility: "高可能性",
    timeframe: "3-6个月",
    liked: false,
    references: [{
      id: "ref-1-1",
      title: "英伟达H200 GPU正式发布，性能提升90%",
      source: "科技日报",
      url: "#",
      time: "2天前"
    }, {
      id: "ref-1-2",
      title: "云服务商争相采购新一代AI芯片",
      source: "财经周刊",
      url: "#",
      time: "1周前"
    }]
  }, {
    id: "scenario-2",
    title: "市场竞争加剧",
    content: "AMD正加快MI300X系列的生产进度，预计在明年第二季度大规模出货，其在某些特定工作负载下的性价比可能超过H200。同时，英特尔的Gaudi系列芯片在推理任务上也展现出了竞争优势，特别是在成本控制方面。更值得关注的是，中国的寒武纪、海光等厂商正在快速追赶，虽然在绝对性能上仍有差距，但在特定应用场景下已经能够满足基本需求。这种竞争格局的变化可能会影响英伟达的定价权，迫使其在保持技术领先的同时也要考虑成本竞争。不过，考虑到AI市场整体仍在快速扩张，预计到2025年市场规模将达到万亿美元级别，即使市场份额有所下降，英伟达的绝对收入仍可能保持增长。关键在于公司能否在软件生态和开发者体验上继续保持领先优势。",
    probability: 70,
    possibility: "中等可能性",
    timeframe: "6-12个月",
    liked: true,
    references: [{
      id: "ref-2-1",
      title: "AMD MI300X芯片性能评测报告",
      source: "芯片世界",
      url: "#",
      time: "3天前"
    }, {
      id: "ref-2-2",
      title: "中国AI芯片厂商加速追赶",
      source: "经济观察报",
      url: "#",
      time: "5天前"
    }]
  }, {
    id: "scenario-3",
    title: "供应链风险持续",
    content: "地缘政治紧张局势可能对H200的生产和销售产生重大影响，特别是在中美科技竞争持续升级的背景下。美国政府可能进一步收紧对华高端芯片出口限制，这将直接影响英伟达在中国这一重要市场的收入。中国市场占英伟达数据中心业务收入的约20-25%，任何供应限制都将产生实质性影响。同时，台积电等关键代工厂商可能面临产能分配的挑战，需要在不同客户和产品线之间进行平衡。另外，先进封装技术的产能限制也可能成为瓶颈，特别是HBM3e内存的供应紧张可能影响H200的整体出货量。这些供应链风险不仅会影响短期业绩，还可能推动英伟达加速供应链多元化布局，包括与更多代工厂商合作以及在其他地区建立生产能力。预计这种调整过程将持续12-18个月。",
    probability: 60,
    possibility: "中等可能性",
    timeframe: "1-3个月",
    liked: false,
    references: [{
      id: "ref-3-1",
      title: "美国考虑进一步限制AI芯片出口",
      source: "华尔街日报",
      url: "#",
      time: "1天前"
    }, {
      id: "ref-3-2",
      title: "台积电先进封装产能紧张",
      source: "电子工程专辑",
      url: "#",
      time: "4天前"
    }]
  }, {
    id: "scenario-4",
    title: "AI应用爆发式增长",
    content: "随着ChatGPT、Claude等大模型应用的普及，以及Sora、Midjourney等AIGC工具的快速发展，市场对高性能AI芯片的需求正呈现爆发式增长。企业级应用正从试点阶段快速进入规模化部署，特别是在金融、医疗、制造等传统行业。H200的强大性能能够支持更大规模的模型训练和实时推理，这对于需要处理海量数据的企业应用至关重要。同时，边缘AI的兴起也为英伟达带来了新的增长机会，自动驾驶、智能机器人、AR/VR等应用场景对算力的需求正在快速增长。预计到2025年，全球AI芯片市场规模将从目前的约500亿美元增长到超过1500亿美元。英伟达凭借H200的技术优势，有望在这波增长中获得超额收益。特别是在云服务提供商加速AI基础设施建设的推动下，数据中心GPU的需求可能超出当前的市场预期。",
    probability: 75,
    possibility: "高可能性",
    timeframe: "6-18个月",
    liked: false,
    references: [{
      id: "ref-4-1",
      title: "2024年AI应用市场规模预测报告",
      source: "IDC研究",
      url: "#",
      time: "1周前"
    }, {
      id: "ref-4-2",
      title: "云服务商AI投资计划大盘点",
      source: "云计算世界",
      url: "#",
      time: "3天前"
    }]
  }, {
    id: "scenario-5",
    title: "技术发展遇到瓶颈",
    content: "AI技术发展可能在某些关键领域遇到瓶颈，影响对高端芯片的需求增长。首先，大模型的规模扩张可能面临收益递减的问题，当前参数量已经从千亿级增长到万亿级，但性能提升的边际效应正在下降。这可能促使行业重新思考技术路线，更多关注模型效率而非纯粹的参数规模。其次，数据质量和可用性问题也可能成为制约因素，高质量训练数据的稀缺性可能限制模型进一步优化的空间。另外，能耗问题也日益突出，训练大型模型的电力成本已经成为重要考虑因素，这可能推动市场更多关注能效比而非绝对性能。在这种背景下，市场可能更青睐性价比较高的中端芯片，而对H200这样的高端产品需求增长可能放缓。同时，新的计算范式如量子计算、光子计算的发展也可能对传统GPU市场产生冲击，虽然短期内影响有限，但长期趋势值得关注。",
    probability: 40,
    possibility: "低可能性",
    timeframe: "12-24个月",
    liked: false,
    references: [{
      id: "ref-5-1",
      title: "大模型训练成本分析报告",
      source: "MIT技术评论",
      url: "#",
      time: "2周前"
    }, {
      id: "ref-5-2",
      title: "量子计算技术突破对AI芯片的影响",
      source: "自然杂志",
      url: "#",
      time: "1个月前"
    }]
  }]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [regeneratingId, setRegeneratingId] = useState<string | null>(null);
  const [editingScenario, setEditingScenario] = useState<FutureScenario | null>(null);
  const [editTitle, setEditTitle] = useState("");
  const [editContent, setEditContent] = useState("");

  // 讨论区相关状态
  const [isDiscussionExpanded, setIsDiscussionExpanded] = useState(false);
  const [comments, setComments] = useState<UserComment[]>([{
    id: "comment-1",
    userName: "投资老李",
    content: "这个分析很有道理，H200的确是革命性的产品。不过我觉得还要关注一下竞争对手的反应，AMD可能会加快MI300X的发布节奏。",
    time: "2小时前",
    likes: 12,
    isLiked: false
  }, {
    id: "comment-2",
    userName: "科技分析师小王",
    content: "从技术层面来看，H200的HBM3e内存确实是亮点，但更重要的是软件生态的配合。CUDA平台的优势还是很明显的。",
    time: "1小时前",
    likes: 8,
    isLiked: true
  }, {
    id: "comment-3",
    userName: "量化交易员张三",
    content: "已经在关注相关标的了，NVDA、TSM都值得配置。不过要注意估值问题，现在价格不便宜。",
    time: "45分钟前",
    likes: 15,
    isLiked: false
  }, {
    id: "comment-4",
    userName: "芯片行业从业者",
    content: "作为行业内人士，我想补充一点：H200的量产能力和良品率还有待观察，这直接影响实际的市场供应。",
    time: "30分钟前",
    likes: 6,
    isLiked: false
  }, {
    id: "comment-5",
    userName: "AI创业者小陈",
    content: "对于我们做AI应用的公司来说，这确实是好消息。训练成本降低意味着更多创新可能性。期待云服务商能快速跟进。",
    time: "20分钟前",
    likes: 9,
    isLiked: true
  }]);
  const displayedComments = isDiscussionExpanded ? comments : comments.slice(0, 3);

  // 评论相关功能
  const toggleCommentLike = (commentId: string) => {
    setComments(prev => prev.map(comment => comment.id === commentId ? {
      ...comment,
      isLiked: !comment.isLiked,
      likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1
    } : comment));
  };

  // 情况推演相关功能
  const toggleScenarioLike = (scenarioId: string) => {
    setScenarios(prev => prev.map(scenario => scenario.id === scenarioId ? {
      ...scenario,
      liked: !scenario.liked
    } : scenario));
  };
  const editScenario = (scenario: FutureScenario) => {
    setEditingScenario(scenario);
    setEditTitle(scenario.title);
    setEditContent(scenario.content);
  };
  const saveEditedScenario = () => {
    if (!editingScenario) return;
    setScenarios(prev => prev.map(scenario => scenario.id === editingScenario.id ? {
      ...scenario,
      title: editTitle,
      content: editContent
    } : scenario));
    setEditingScenario(null);
  };
  const cancelEdit = () => {
    setEditingScenario(null);
    setEditTitle("");
    setEditContent("");
  };
  const replaceScenario = (scenarioId: string) => {
    setRegeneratingId(scenarioId);
    setTimeout(() => {
      setScenarios(prev => prev.map(scenario => scenario.id === scenarioId ? {
        ...scenario,
        title: "新生成的未来情况",
        content: "基于最新数据和分析生成的未来可能性推演...",
        probability: Math.floor(Math.random() * 40) + 40
      } : scenario));
      setRegeneratingId(null);
    }, 1500);
  };
  const regenerateAllScenarios = () => {
    setIsGenerating(true);
    setTimeout(() => {
      // 模拟重新生成所有情况
      setIsGenerating(false);
    }, 2000);
  };
  return <>
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="text-lg flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-primary" />
            未来情况推演
          </CardTitle>
          {!isReadOnly && <div className="flex gap-2 mt-2">
              <Button variant="outline" size="sm" onClick={regenerateAllScenarios} disabled={isGenerating}>
                <RefreshCw className={`h-4 w-4 mr-1 ${isGenerating ? 'animate-spin' : ''}`} />
                重新推演
              </Button>
            </div>}
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4 max-h-[600px] overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-muted-foreground/20 hover:scrollbar-thumb-muted-foreground/40">
            {scenarios.map((scenario, index) => <Card key={scenario.id} className="transition-all duration-200 hover:shadow-md">
                <CardContent className="p-4">
                  <div className="flex gap-4">
                    {/* 左侧主要内容 */}
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            情况 {index + 1}
                          </Badge>
                          <Badge variant="secondary" className={`text-xs ${scenario.possibility === '高可能性' ? 'bg-green-100 text-green-700' : scenario.possibility === '中等可能性' ? 'bg-yellow-100 text-yellow-700' : 'bg-red-100 text-red-700'}`}>
                            {scenario.possibility}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button size="sm" variant="ghost" className="h-6 w-6 p-0" onClick={() => toggleScenarioLike(scenario.id)}>
                            <Heart className={`h-4 w-4 ${scenario.liked ? 'fill-red-500 text-red-500' : 'text-muted-foreground'}`} />
                          </Button>
                          {!isReadOnly && <>
                              <Button size="sm" variant="ghost" className="h-6 w-6 p-0" onClick={() => editScenario(scenario)}>
                                <Edit3 className="h-3 w-3" />
                              </Button>
                              <Button size="sm" variant="ghost" className="h-6 w-6 p-0" onClick={() => replaceScenario(scenario.id)} disabled={regeneratingId === scenario.id}>
                                <RefreshCw className={`h-3 w-3 ${regeneratingId === scenario.id ? 'animate-spin' : ''}`} />
                              </Button>
                            </>}
                        </div>
                      </div>
                      
                      <h4 className="font-medium text-sm mb-2 leading-tight">
                        {scenario.title}
                      </h4>
                      
                      <p className="text-xs text-muted-foreground leading-relaxed mb-3">
                        {scenario.content}
                      </p>
                      
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>概率: {scenario.probability}%</span>
                        <span>时间: {scenario.timeframe}</span>
                      </div>
                    </div>

                    {/* 右侧参考新闻 */}
                    <div className="w-64 border-l pl-4">
                      <div className="flex items-center gap-1 mb-3">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <span className="text-xs font-medium text-muted-foreground">参考资料</span>
                      </div>
                      <div className="space-y-1">
                        {scenario.references.map(ref => <div key={ref.id} className="flex items-center justify-between gap-2 py-1 px-2 rounded hover:bg-muted/30 transition-colors">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2">
                                <span className="text-xs font-medium truncate">{ref.title}</span>
                                <span className="text-xs text-muted-foreground whitespace-nowrap">·</span>
                                <span className="text-xs text-muted-foreground whitespace-nowrap">{ref.source}</span>
                                <span className="text-xs text-muted-foreground whitespace-nowrap">·</span>
                                <span className="text-xs text-muted-foreground whitespace-nowrap">{ref.time}</span>
                              </div>
                            </div>
                            <Button variant="ghost" size="sm" className="h-4 w-4 p-0 flex-shrink-0" onClick={() => window.open(ref.url, '_blank')}>
                              <ExternalLink className="h-2.5 w-2.5" />
                            </Button>
                          </div>)}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>)}
          </div>
        </CardContent>
      </Card>

      {/* 讨论区 */}
      

      {/* 编辑情况推演的抽屉 */}
      <Sheet open={!!editingScenario} onOpenChange={() => setEditingScenario(null)}>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>编辑未来情况</SheetTitle>
          </SheetHeader>
          <div className="space-y-4 mt-6">
            <div>
              <label className="text-sm font-medium">标题</label>
              <Input value={editTitle} onChange={e => setEditTitle(e.target.value)} className="mt-1" />
            </div>
            <div>
              <label className="text-sm font-medium">内容</label>
              <Textarea value={editContent} onChange={e => setEditContent(e.target.value)} className="mt-1 min-h-[120px]" />
            </div>
            <div className="flex gap-2 pt-4">
              <Button onClick={saveEditedScenario}>保存</Button>
              <Button variant="outline" onClick={cancelEdit}>取消</Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </>;
}