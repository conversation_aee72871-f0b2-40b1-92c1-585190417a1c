import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Building, Globe, Users, Calendar } from "lucide-react";

interface CompanyInfoProps {
  symbol: string;
  name: string;
  employees: string;
  founded: string;
  headquarters: string;
  description: string;
}

export function CompanyInfo({ symbol, name, employees, founded, headquarters, description }: CompanyInfoProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building className="h-5 w-5" />
          公司信息
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">员工数</span>
            </div>
            <p className="font-semibold">{employees}</p>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">成立年份</span>
            </div>
            <p className="font-semibold">{founded}</p>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Globe className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">总部</span>
            </div>
            <p className="font-semibold">{headquarters}</p>
          </div>
        </div>
        
        <div className="pt-4 border-t">
          <h4 className="font-medium mb-2">公司简介</h4>
          <p className="text-sm text-muted-foreground leading-relaxed">
            {description}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}