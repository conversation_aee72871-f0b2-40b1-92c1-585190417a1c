import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TrendingUp, TrendingDown, BarChart3 } from "lucide-react";
import { useState } from "react";

interface StockChartProps {
  symbol: string;
  name: string;
  price: string;
  change: string;
  type: "bullish" | "bearish";
}

export function StockChart({ symbol, name, price, change, type }: StockChartProps) {
  const [timeFrame, setTimeFrame] = useState<'today' | '5days' | 'kline' | 'valuation'>('today');

  // 模拟不同时间框架的数据
  const todayData = [
    { time: "09:30", price: 715, volume: 2.1 },
    { time: "10:00", price: 718, volume: 1.8 },
    { time: "10:30", price: 720, volume: 2.3 },
    { time: "11:00", price: 722, volume: 1.9 },
    { time: "11:30", price: 719, volume: 2.5 },
    { time: "12:00", price: 721, volume: 1.7 },
    { time: "12:30", price: 723, volume: 2.0 },
    { time: "13:00", price: 725, volume: 2.2 },
    { time: "13:30", price: 722, volume: 1.8 },
    { time: "14:00", price: 724, volume: 2.1 },
    { time: "14:30", price: 726, volume: 1.9 },
    { time: "15:00", price: 722, volume: 2.3 },
    { time: "15:30", price: 720, volume: 2.0 }
  ];

  const fiveDaysData = [
    { time: "周一", price: 715, volume: 45.2 },
    { time: "周二", price: 720, volume: 52.1 },
    { time: "周三", price: 718, volume: 48.7 },
    { time: "周四", price: 725, volume: 55.3 },
    { time: "周五", price: 722, volume: 51.8 }
  ];

  const klineData = [
    { time: "09:30", open: 715, high: 718, low: 714, close: 717 },
    { time: "10:00", open: 717, high: 721, low: 716, close: 720 },
    { time: "10:30", open: 720, high: 723, low: 719, close: 722 },
    { time: "11:00", open: 722, high: 725, low: 721, close: 724 },
    { time: "11:30", open: 724, high: 726, low: 720, close: 722 }
  ];

  // 估值走势数据
  const valuationData = [
    { time: "Q1 2023", pe: 45.2, ps: 15.8 },
    { time: "Q2 2023", pe: 52.1, ps: 18.2 },
    { time: "Q3 2023", pe: 58.7, ps: 20.5 },
    { time: "Q4 2023", pe: 62.3, ps: 22.1 },
    { time: "Q1 2024", pe: 65.4, ps: 24.3 }
  ];

  const getCurrentData = () => {
    switch (timeFrame) {
      case '5days': return fiveDaysData;
      case 'kline': return klineData;
      case 'valuation': return valuationData;
      default: return todayData;
    }
  };

  const currentData = getCurrentData();
  
  // 根据不同模式获取价格数据
  const getPrices = () => {
    if (timeFrame === 'valuation') {
      return []; // 估值模式不需要价格数据
    }
    return timeFrame === 'kline' 
      ? currentData.map(d => d.close) 
      : currentData.map(d => d.price);
  };

  const prices = getPrices();
  const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
  const maxPrice = prices.length > 0 ? Math.max(...prices) : 100;
  const priceRange = maxPrice - minPrice;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-primary/10">
              {type === "bullish" ? (
                <TrendingUp className="h-5 w-5 text-success" />
              ) : (
                <TrendingDown className="h-5 w-5 text-destructive" />
              )}
            </div>
            <div>
              <CardTitle className="text-xl font-bold">{symbol}</CardTitle>
              <p className="text-sm text-muted-foreground">{name}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">{price}</div>
            <div className={`text-base font-medium flex items-center gap-1 ${change.startsWith('+') ? 'text-success' : 'text-destructive'}`}>
              {change.startsWith('+') ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
              {change}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">股价走势</span>
              <Badge variant={type === "bullish" ? "default" : "destructive"} className="text-xs">
                {type === "bullish" ? "看涨" : "看跌"}
              </Badge>
            </div>
            
            {/* 时间框架切换 */}
            <div className="flex gap-1">
              <Button
                variant={timeFrame === 'today' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTimeFrame('today')}
                className="text-xs h-7"
              >
                今日
              </Button>
              <Button
                variant={timeFrame === '5days' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTimeFrame('5days')}
                className="text-xs h-7"
              >
                近5日
              </Button>
              <Button
                variant={timeFrame === 'kline' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTimeFrame('kline')}
                className="text-xs h-7"
              >
                日K线
              </Button>
              <Button
                variant={timeFrame === 'valuation' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTimeFrame('valuation')}
                className="text-xs h-7"
              >
                估值走势
              </Button>
            </div>
          </div>
          
          {/* 图表区域 */}
          <div className="relative h-48 bg-muted/10 rounded-lg p-4">
            {timeFrame === 'valuation' ? (
              // 估值走势图 (P/E, P/S)
              <div className="flex items-end justify-between h-full">
                {valuationData.map((point, index) => {
                  const peHeight = (point.pe / 70) * 100; // 假设最大PE为70
                  const psHeight = (point.ps / 30) * 100; // 假设最大PS为30
                  
                  return (
                    <div key={index} className="flex flex-col items-center gap-1">
                      <div className="flex gap-1 items-end">
                        <div 
                          className="w-2 bg-blue-500 rounded-t"
                          style={{ height: `${Math.max(peHeight, 5)}%` }}
                          title={`P/E: ${point.pe}`}
                        />
                        <div 
                          className="w-2 bg-green-500 rounded-t"
                          style={{ height: `${Math.max(psHeight, 5)}%` }}
                          title={`P/S: ${point.ps}`}
                        />
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {point.time}
                      </span>
                    </div>
                  );
                })}
                {/* 图例 */}
                <div className="absolute top-2 right-2 flex gap-3 text-xs">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-blue-500 rounded"></div>
                    <span>P/E</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-500 rounded"></div>
                    <span>P/S</span>
                  </div>
                </div>
              </div>
            ) : timeFrame === 'kline' ? (
              // K线图
              <div className="flex items-end justify-between h-full">
                {klineData.map((candle, index) => {
                  const bodyHeight = Math.abs(candle.close - candle.open) / priceRange * 100;
                  const bodyTop = ((Math.max(candle.close, candle.open) - minPrice) / priceRange) * 100;
                  const isGreen = candle.close > candle.open;
                  
                  return (
                    <div key={index} className="relative flex flex-col items-center">
                      {/* 影线 */}
                      <div 
                        className="w-0.5 bg-muted-foreground"
                        style={{ 
                          height: `${((candle.high - candle.low) / priceRange) * 100}%`,
                          marginBottom: `${((candle.low - minPrice) / priceRange) * 100}%`
                        }}
                      />
                      {/* K线实体 */}
                      <div 
                        className={`w-3 absolute ${isGreen ? 'bg-success' : 'bg-destructive'}`}
                        style={{ 
                          height: `${Math.max(bodyHeight, 2)}%`,
                          bottom: `${bodyTop - bodyHeight}%`
                        }}
                      />
                      {index % 1 === 0 && (
                        <span className="text-xs text-muted-foreground mt-1">
                          {candle.time}
                        </span>
                      )}
                    </div>
                  );
                })}
              </div>
            ) : (
              // 普通折线图
              <div className="flex items-end justify-between h-full">
                {currentData.map((point, index) => {
                  const price = 'price' in point ? point.price : point.close;
                  const height = ((price - minPrice) / priceRange) * 100;
                  return (
                    <div key={index} className="flex flex-col items-center gap-1">
                      <div 
                        className={`w-2 rounded-t transition-all duration-300 ${
                          type === "bullish" ? "bg-success" : "bg-destructive"
                        }`}
                        style={{ height: `${Math.max(height, 10)}%` }}
                      />
                      {(timeFrame === 'today' ? index % 3 === 0 : true) && (
                        <span className="text-xs text-muted-foreground mt-1">
                          {point.time}
                        </span>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* 价格区间和成交量 */}
          {timeFrame === 'valuation' ? (
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-1">
                <div>
                  <span className="text-muted-foreground">当前P/E: </span>
                  <span className="font-medium">65.4</span>
                </div>
                <div>
                  <span className="text-muted-foreground">行业平均P/E: </span>
                  <span className="font-medium">28.5</span>
                </div>
              </div>
              <div className="space-y-1">
                <div>
                  <span className="text-muted-foreground">当前P/S: </span>
                  <span className="font-medium">24.3</span>
                </div>
                <div>
                  <span className="text-muted-foreground">行业平均P/S: </span>
                  <span className="font-medium">12.1</span>
                </div>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-1">
                <div>
                  <span className="text-muted-foreground">最低: </span>
                  <span className="font-medium">${minPrice.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">最高: </span>
                  <span className="font-medium">${maxPrice.toFixed(2)}</span>
                </div>
              </div>
              <div className="space-y-1">
                <div>
                  <span className="text-muted-foreground">成交量: </span>
                  <span className="font-medium">
                    {timeFrame === '5days' ? '251.1M' : timeFrame === 'today' ? '25.1M' : '45.2M'}
                  </span>
                </div>
                <div>
                  <span className="text-muted-foreground">换手率: </span>
                  <span className="font-medium">2.34%</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}