import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DollarSign, TrendingUp, Percent, BarChart } from "lucide-react";

interface FinancialSummaryProps {
  marketCap: string;
  revenue: string;
  profit: string;
  peRatio: string;
  recentQuarters?: Array<{
    quarter: string;
    revenue: string;
    growth: string;
  }>;
}

export function FinancialSummary({ marketCap, revenue, profit, peRatio, recentQuarters = [] }: FinancialSummaryProps) {
  const financialMetrics = [
    {
      label: "市值",
      value: marketCap,
      icon: DollarSign,
      color: "text-primary"
    },
    {
      label: "年营收",
      value: revenue,
      icon: TrendingUp,
      color: "text-success"
    },
    {
      label: "净利润",
      value: profit,
      icon: BarChart,
      color: "text-blue-600"
    },
    {
      label: "P/E比率",
      value: peRatio,
      icon: Percent,
      color: "text-orange-600"
    }
  ];

  return (
    <div className="space-y-6">
      {/* 核心财务指标 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            核心财务数据
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {financialMetrics.map((metric) => {
              const IconComponent = metric.icon;
              return (
                <div key={metric.label} className="text-center p-4 bg-muted/20 rounded-lg">
                  <div className="flex justify-center mb-2">
                    <IconComponent className={`h-6 w-6 ${metric.color}`} />
                  </div>
                  <div className="text-sm text-muted-foreground mb-1">{metric.label}</div>
                  <div className="text-2xl font-bold">{metric.value}</div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* 近期财报 */}
      {recentQuarters.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>近期财报表现</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {recentQuarters.map((quarter, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg bg-muted/5">
                  <div>
                    <div className="font-medium">{quarter.quarter}</div>
                    <div className="text-sm text-muted-foreground">营收</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-lg">{quarter.revenue}</div>
                    <div className={`text-sm font-medium ${
                      quarter.growth.startsWith('+') ? 'text-success' : 'text-destructive'
                    }`}>
                      {quarter.growth}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}