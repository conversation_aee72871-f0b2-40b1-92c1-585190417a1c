import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Brain, TrendingUp, AlertTriangle, Target } from "lucide-react";
import { useState } from "react";

interface IdeasAnalysisProps {
  symbol: string;
  ideaId?: string | null;
}

export function IdeasAnalysis({ symbol, ideaId }: IdeasAnalysisProps) {
  const [selectedIdeaId, setSelectedIdeaId] = useState(ideaId || '1');

  // 模拟该股票相关的Ideas/事件
  const relatedIdeas = [
    {
      id: '1',
      title: 'H200芯片AI训练突破',
      category: '技术创新',
      status: 'active',
      description: 'H200芯片性能提升90%，AI训练效率大幅改善'
    },
    {
      id: '2', 
      title: '数据中心业务爆发',
      category: '业绩增长',
      status: 'trending',
      description: '云服务商GPU采购需求激增，营收同比增长427%'
    },
    {
      id: '3',
      title: '自动驾驶商业化进展',
      category: '新兴业务',
      status: 'potential',
      description: 'DRIVE平台获更多车企采用，商业化前景明朗'
    },
    {
      id: '4',
      title: 'CUDA生态护城河',
      category: '竞争优势',
      status: 'stable',
      description: '开发者生态持续扩张，技术壁垒不断加深'
    },
    {
      id: '5',
      title: 'AI芯片竞争格局',
      category: '市场环境',
      status: 'watch',
      description: 'AMD、Intel加大投入，竞争态势值得关注'
    }
  ];
  
  // 根据选中的idea获取对应的定性分析数据
  const getQualitativeAnalysis = (ideaId: string) => {
    const analysisMap: Record<string, any> = {
      '1': { // H200芯片
        eventAnalysis: {
          title: "事件分析",
          content: [
            "H200芯片发布标志着英伟达在AI算力领域的技术领先地位进一步巩固，相比H100性能提升达90%",
            "数据中心业务持续爆发式增长，Q1营收同比增长427%，远超市场预期",
            "与主要云服务商签署长期供货协议，订单可见性大幅提升至18个月",
            "软件业务CUDA生态持续扩张，开发者数量突破500万，护城河不断加深"
          ]
        },
        businessChanges: {
          title: "业务变化", 
          content: [
            "数据中心业务占比从去年同期的40%提升至78%，成为绝对主要收入来源",
            "游戏业务虽然绝对金额增长，但占比下降至17%，业务结构向B端倾斜",
            "专业可视化业务受益于AI工作站需求增长，同比增长108%",
            "软件和服务收入占比提升，从硬件为主向软硬一体化解决方案转型"
          ]
        },
        investmentOpportunity: {
          title: "投资机会分析",
          opportunities: [
            "AI革命仍处于早期阶段，数据中心GPU需求将持续3-5年高速增长",
            "技术护城河深厚，CUDA生态难以被替代，享有定价权优势",
            "新产品发布周期缩短，B200、X100等产品将带来新增长动力"
          ],
          risks: [
            "估值水平较高，PE达65倍，存在回调风险",
            "地缘政治影响供应链稳定，对中国市场限制影响营收",
            "竞争对手AMD、Intel加大AI芯片投入，市场竞争加剧"
          ]
        }
      },
      '2': { // 数据中心业务
        eventAnalysis: {
          title: "事件分析",
          content: [
            "云服务商在AI基础设施投资呈现加速态势，微软、亚马逊、谷歌等大幅增加GPU采购预算",
            "企业级AI应用快速落地，从概念验证转向规模化部署，算力需求激增",
            "ChatGPT等大模型成功验证商业价值，带动整个行业对AI训练和推理需求",
            "数据中心现代化改造需求旺盛，传统CPU向GPU+CPU异构计算转型"
          ]
        },
        businessChanges: {
          title: "业务变化",
          content: [
            "数据中心业务营收环比增长22%，连续四个季度保持高速增长态势",
            "客户结构优化，超大规模云服务商占比提升至70%以上",
            "平均销售价格持续上涨，H100芯片供不应求推动毛利率改善",
            "服务和软件收入快速增长，为客户提供端到端AI解决方案"
          ]
        },
        investmentOpportunity: {
          title: "投资机会分析",
          opportunities: [
            "全球数据中心GPU市场规模预计5年内增长10倍，英伟达市占率超80%",
            "云服务商资本开支持续增长，GPU采购预算占比不断提升",
            "边缘AI和推理市场启动，为公司打开新的增长空间"
          ],
          risks: [
            "客户集中度较高，过度依赖少数大型科技公司",
            "供应链紧张，产能扩张需要时间，可能影响市场份额",
            "数据中心投资具有周期性，需警惕需求波动风险"
          ]
        }
      }
    };
    
    return analysisMap[ideaId] || analysisMap['1'];
  };

  const qualitativeAnalysis = getQualitativeAnalysis(selectedIdeaId);

  // 根据选中的idea获取对应的定量分析数据  
  const getQuantitativeAnalysis = (ideaId: string) => {
    const analysisMap: Record<string, any> = {
      '1': { // H200芯片
        impactAssessment: {
          financialImpact: {
            revenue2024: { estimate: "+88%", confidence: 85 },
            revenue2025: { estimate: "+24%", confidence: 75 },
            margin: { estimate: "+200bp", confidence: 80 },
            roe: { estimate: "120%+", confidence: 90 }
          },
          valuationImpact: {
            targetPE: { estimate: "45-55x", confidence: 70 },
            targetPS: { estimate: "18-22x", confidence: 75 },
            priceTarget: { estimate: "$850", confidence: 85 },
            timeHorizon: "12个月"
          }
        },
        reportData: {
          lastUpdated: "2024年1月15日 14:30",
          version: "v2.1",
          analyst: "AI量化团队",
          content: `基于H200芯片发布的最新分析，英伟达技术领先优势进一步扩大。H200相比H100性能提升90%，预计将推动数据中心业务在2024年实现88%的强劲增长。

**收入增长驱动因素：**
H200芯片的推出恰逢企业AI应用加速落地期，云服务商对高性能GPU的需求达到历史新高。预计2024年数据中心业务营收将达到500亿美元规模，毛利率有望提升至75%以上。

**盈利能力分析：**
技术升级带来的定价权增强，叠加规模效应，预计净利润率将达到新高。ROE水平有望突破120%，在科技股中处于领先地位。

**估值合理性评估：**
尽管当前估值偏高，但考虑到技术突破和市场需求，合理PE区间调整至45-55倍。基于DCF模型测算，目标价维持850美元。`
        }
      },
      '2': { // 数据中心业务
        impactAssessment: {
          financialImpact: {
            revenue2024: { estimate: "+95%", confidence: 90 },
            revenue2025: { estimate: "+28%", confidence: 80 },
            margin: { estimate: "+250bp", confidence: 85 },
            roe: { estimate: "125%+", confidence: 88 }
          },
          valuationImpact: {
            targetPE: { estimate: "40-50x", confidence: 75 },
            targetPS: { estimate: "16-20x", confidence: 80 },
            priceTarget: { estimate: "$900", confidence: 88 },
            timeHorizon: "12个月"
          }
        },
        reportData: {
          lastUpdated: "2024年1月18日 16:45",
          version: "v2.3", 
          analyst: "数据中心专项小组",
          content: `数据中心业务呈现超预期增长态势，云服务商AI基础设施投资进入快车道。

**市场需求分析：**
全球云服务商资本开支中GPU占比已提升至30%以上，预计2024年相关采购预算将突破800亿美元。英伟达凭借技术优势有望获得80%以上市场份额。

**竞争格局评估：**
虽然AMD、Intel等竞争对手加大投入，但CUDA生态系统的先发优势短期内难以撼动。预计英伟达在数据中心GPU市场的主导地位至少维持2-3年。

**财务表现预测：**
基于当前订单情况和客户指引，2024年数据中心业务营收预计增长95%，达到550亿美元。毛利率受益于产品组合优化，有望提升250个基点。`
        }
      }
    };
    
    return analysisMap[ideaId] || analysisMap['1'];
  };

  const quantitativeAnalysis = getQuantitativeAnalysis(selectedIdeaId);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-700 border-green-200';
      case 'trending': return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'potential': return 'bg-purple-100 text-purple-700 border-purple-200';
      case 'stable': return 'bg-gray-100 text-gray-700 border-gray-200';
      case 'watch': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  return (
    <div className="space-y-4">
      {/* 相关Ideas/事件卡片选择器 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <Brain className="h-4 w-4" />
            我的ideas
            <span className="text-xs text-muted-foreground font-normal">(仅显示自己的ideas)</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-3 overflow-x-auto pb-2">
            {relatedIdeas.map((idea) => (
              <div
                key={idea.id}
                onClick={() => setSelectedIdeaId(idea.id)}
                className={`flex-shrink-0 cursor-pointer p-3 rounded-lg border-2 transition-all duration-200 min-w-[200px] ${
                  selectedIdeaId === idea.id 
                    ? 'border-primary bg-primary/5' 
                    : 'border-border hover:border-primary/50 hover:bg-muted/50'
                }`}
              >
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-sm">{idea.title}</h4>
                    <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(idea.status)}`}>
                      {idea.category}
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground leading-relaxed">
                    {idea.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      {/* 定性分析 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <Brain className="h-4 w-4" />
            事件定性分析
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 三栏布局：事件分析、业务变化、投资机会分析 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 divide-x divide-border">
            {/* 事件分析 */}
            <div className="space-y-2 pr-4">
              <h4 className="font-medium text-sm text-foreground">
                {qualitativeAnalysis.eventAnalysis.title}
              </h4>
              <div className="max-h-64 overflow-y-auto pr-2">
                <ul className="space-y-2">
                  {qualitativeAnalysis.eventAnalysis.content.map((item, index) => (
                    <li key={index} className="text-xs text-muted-foreground flex items-start gap-2">
                      <span className="w-1 h-1 bg-muted-foreground rounded-full mt-1.5 flex-shrink-0"></span>
                      <span className="leading-relaxed">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* 业务变化 */}
            <div className="space-y-2 px-4">
              <h4 className="font-medium text-sm text-foreground">
                {qualitativeAnalysis.businessChanges.title}
              </h4>
              <div className="max-h-64 overflow-y-auto pr-2">
                <ul className="space-y-2">
                  {qualitativeAnalysis.businessChanges.content.map((item, index) => (
                    <li key={index} className="text-xs text-muted-foreground flex items-start gap-2">
                      <span className="w-1 h-1 bg-muted-foreground rounded-full mt-1.5 flex-shrink-0"></span>
                      <span className="leading-relaxed">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* 投资机会分析 */}
            <div className="space-y-3 pl-4">
              <h4 className="font-medium text-sm text-foreground">
                {qualitativeAnalysis.investmentOpportunity.title}
              </h4>
              <div className="max-h-64 overflow-y-auto pr-2 space-y-3">
                {/* 投资机会 */}
                <div className="space-y-1">
                  <h5 className="text-xs font-medium text-foreground">投资机会</h5>
                  <ul className="space-y-1">
                    {qualitativeAnalysis.investmentOpportunity.opportunities.map((item, index) => (
                      <li key={index} className="text-xs text-muted-foreground flex items-start gap-2">
                        <span className="w-1 h-1 bg-muted-foreground rounded-full mt-1.5 flex-shrink-0"></span>
                        <span className="leading-relaxed">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* 投资风险 */}
                <div className="space-y-1">
                  <h5 className="text-xs font-medium text-foreground">投资风险</h5>
                  <ul className="space-y-1">
                    {qualitativeAnalysis.investmentOpportunity.risks.map((item, index) => (
                      <li key={index} className="text-xs text-muted-foreground flex items-start gap-2">
                        <span className="w-1 h-1 bg-muted-foreground rounded-full mt-1.5 flex-shrink-0"></span>
                        <span className="leading-relaxed">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 定量分析 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <TrendingUp className="h-4 w-4" />
            事件定量分析
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 影响估计和置信度 - 财务和估值指标合并为一行 */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 财务指标影响 */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">财务指标影响估计</h4>
                <div className="grid grid-cols-2 gap-3">
                  <div className="p-3 bg-muted/10 rounded">
                    <div className="text-xs text-muted-foreground">2024年营收增长</div>
                    <div className="font-semibold text-success">{quantitativeAnalysis.impactAssessment.financialImpact.revenue2024.estimate}</div>
                    <div className="text-xs text-muted-foreground">置信度: {quantitativeAnalysis.impactAssessment.financialImpact.revenue2024.confidence}%</div>
                  </div>
                  <div className="p-3 bg-muted/10 rounded">
                    <div className="text-xs text-muted-foreground">2025年营收增长</div>
                    <div className="font-semibold text-success">{quantitativeAnalysis.impactAssessment.financialImpact.revenue2025.estimate}</div>
                    <div className="text-xs text-muted-foreground">置信度: {quantitativeAnalysis.impactAssessment.financialImpact.revenue2025.confidence}%</div>
                  </div>
                  <div className="p-3 bg-muted/10 rounded">
                    <div className="text-xs text-muted-foreground">利润率提升</div>
                    <div className="font-semibold text-primary">{quantitativeAnalysis.impactAssessment.financialImpact.margin.estimate}</div>
                    <div className="text-xs text-muted-foreground">置信度: {quantitativeAnalysis.impactAssessment.financialImpact.margin.confidence}%</div>
                  </div>
                  <div className="p-3 bg-muted/10 rounded">
                    <div className="text-xs text-muted-foreground">ROE水平</div>
                    <div className="font-semibold text-primary">{quantitativeAnalysis.impactAssessment.financialImpact.roe.estimate}</div>
                    <div className="text-xs text-muted-foreground">置信度: {quantitativeAnalysis.impactAssessment.financialImpact.roe.confidence}%</div>
                  </div>
                </div>
              </div>

              {/* 估值指标影响 */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">估值指标影响估计</h4>
                <div className="grid grid-cols-2 gap-3">
                  <div className="p-3 bg-muted/10 rounded">
                    <div className="text-xs text-muted-foreground">合理PE区间</div>
                    <div className="font-semibold text-blue-600">{quantitativeAnalysis.impactAssessment.valuationImpact.targetPE.estimate}</div>
                    <div className="text-xs text-muted-foreground">置信度: {quantitativeAnalysis.impactAssessment.valuationImpact.targetPE.confidence}%</div>
                  </div>
                  <div className="p-3 bg-muted/10 rounded">
                    <div className="text-xs text-muted-foreground">合理PS区间</div>
                    <div className="font-semibold text-blue-600">{quantitativeAnalysis.impactAssessment.valuationImpact.targetPS.estimate}</div>
                    <div className="text-xs text-muted-foreground">置信度: {quantitativeAnalysis.impactAssessment.valuationImpact.targetPS.confidence}%</div>
                  </div>
                  <div className="p-3 bg-muted/10 rounded">
                    <div className="text-xs text-muted-foreground">目标价</div>
                    <div className="font-semibold text-success">{quantitativeAnalysis.impactAssessment.valuationImpact.priceTarget.estimate}</div>
                    <div className="text-xs text-muted-foreground">置信度: {quantitativeAnalysis.impactAssessment.valuationImpact.priceTarget.confidence}%</div>
                  </div>
                  <div className="p-3 bg-muted/10 rounded">
                    <div className="text-xs text-muted-foreground">时间框架</div>
                    <div className="font-semibold">{quantitativeAnalysis.impactAssessment.valuationImpact.timeHorizon}</div>
                    <div className="text-xs text-muted-foreground">投资期限</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 定量分析报告 */}
          <div className="border-t pt-4">
            <div className="flex gap-4">
              {/* 左侧版本信息 */}
              <div className="w-48 flex-shrink-0 space-y-2">
                <div className="text-xs text-muted-foreground">
                  <div className="font-medium">版本信息</div>
                  <div className="mt-1 space-y-1">
                    <div>更新时间: {quantitativeAnalysis.reportData.lastUpdated}</div>
                    <div>版本: {quantitativeAnalysis.reportData.version}</div>
                    <div>分析师: {quantitativeAnalysis.reportData.analyst}</div>
                  </div>
                </div>
              </div>
              
              {/* 右侧报告正文 */}
              <div className="flex-1">
                <div className="text-sm text-muted-foreground leading-relaxed whitespace-pre-line">
                  {quantitativeAnalysis.reportData.content}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}