import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Building, Package, TrendingUp, DollarSign, ChevronDown, ChevronUp } from "lucide-react";
import { useState } from "react";

interface ProductLine {
  name: string;
  description: string;
}

interface EnhancedCompanyInfoProps {
  symbol: string;
  name: string;
  employees: string;
  founded: string;
  headquarters: string;
  description: string;
  businessRevenue3Years?: Array<{
    year: string;
    segments: Array<{
      segment: string;
      revenue: string;
      change: string;
      percentage: string;
    }>;
  }>;
}

export function EnhancedCompanyInfo({ 
  symbol, 
  name, 
  employees, 
  founded, 
  headquarters, 
  description,
  businessRevenue3Years = []
}: EnhancedCompanyInfoProps) {
  // 默认产品线数据（3行正文介绍）
  const defaultProductDescription = symbol === 'NVDA' ? 
    "英伟达主要专注于GPU和AI计算加速器领域，其H100、A100等数据中心GPU在深度学习训练和推理市场占据主导地位。公司通过CUDA生态系统构建了强大的软件平台，为AI开发者提供完整工具链。此外，DRIVE自动驾驶平台和专业可视化产品线也为公司带来稳定增长，形成了从芯片硬件到软件生态的完整产业布局，在AI革命中占据核心地位。" :
    "公司主要业务涵盖核心产品服务、云计算解决方案和企业级应用三大板块。通过持续的技术创新和市场拓展，公司在各个细分领域都建立了竞争优势。新兴技术平台的投入为公司未来增长提供了新的动力引擎。";

  // 最近3年分业务收入数据
  const defaultBusinessRevenue3Years = symbol === 'NVDA' ? [
    { year: '2023', segments: [
      { segment: '数据中心', revenue: '$47.5B', change: '+427%', percentage: '78%' },
      { segment: '游戏', revenue: '$10.4B', change: '+15%', percentage: '17%' },
      { segment: '专业可视化', revenue: '$1.5B', change: '+108%', percentage: '2%' },
      { segment: '汽车', revenue: '$1.1B', change: '+72%', percentage: '2%' }
    ]},
    { year: '2022', segments: [
      { segment: '数据中心', revenue: '$15.0B', change: '+61%', percentage: '55%' },
      { segment: '游戏', revenue: '$9.1B', change: '-33%', percentage: '34%' },
      { segment: '专业可视化', revenue: '$1.5B', change: '-27%', percentage: '6%' },
      { segment: '汽车', revenue: '$0.6B', change: '+60%', percentage: '2%' }
    ]},
    { year: '2021', segments: [
      { segment: '数据中心', revenue: '$10.6B', change: '+124%', percentage: '40%' },
      { segment: '游戏', revenue: '$12.5B', change: '+61%', percentage: '47%' },
      { segment: '专业可视化', revenue: '$2.1B', change: '+12%', percentage: '8%' },
      { segment: '汽车', revenue: '$0.6B', change: '+6%', percentage: '2%' }
    ]}
  ] : [
    { year: '2023', segments: [
      { segment: '主营业务', revenue: '$150.2B', change: '+12%', percentage: '75%' },
      { segment: '云服务', revenue: '$45.8B', change: '+28%', percentage: '23%' },
      { segment: '其他业务', revenue: '$4.2B', change: '+5%', percentage: '2%' }
    ]},
    { year: '2022', segments: [
      { segment: '主营业务', revenue: '$134.1B', change: '+8%', percentage: '74%' },
      { segment: '云服务', revenue: '$35.8B', change: '+32%', percentage: '25%' },
      { segment: '其他业务', revenue: '$4.0B', change: '+2%', percentage: '2%' }
    ]},
    { year: '2021', segments: [
      { segment: '主营业务', revenue: '$124.2B', change: '+15%', percentage: '76%' },
      { segment: '云服务', revenue: '$27.1B', change: '+45%', percentage: '22%' },
      { segment: '其他业务', revenue: '$3.9B', change: '+8%', percentage: '2%' }
    ]}
  ];

  // 默认分业务收入数据
  const defaultBusinessRevenue = symbol === 'NVDA' ? [
    { segment: '数据中心', revenue: '$47.5B', change: '+427%', percentage: '78%' },
    { segment: '游戏', revenue: '$10.4B', change: '+15%', percentage: '17%' },
    { segment: '专业可视化', revenue: '$1.5B', change: '+108%', percentage: '2%' },
    { segment: '汽车', revenue: '$1.1B', change: '+72%', percentage: '2%' }
  ] : [
    { segment: '主营业务', revenue: '$150.2B', change: '+12%', percentage: '75%' },
    { segment: '云服务', revenue: '$45.8B', change: '+28%', percentage: '23%' },
    { segment: '其他业务', revenue: '$4.2B', change: '+5%', percentage: '2%' }
  ];

  const currentBusinessRevenue3Years = businessRevenue3Years.length > 0 ? businessRevenue3Years : defaultBusinessRevenue3Years;
  const isTextTruncated = defaultProductDescription.length > 150;
  const [showPercentages, setShowPercentages] = useState(false);
  const [showGrowthRates, setShowGrowthRates] = useState(false);

  return (
    <TooltipProvider>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <Building className="h-4 w-4" />
            公司信息
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 基本信息 */}
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div className="space-y-1">
              <span className="text-muted-foreground">员工数</span>
              <p className="font-semibold">{employees}</p>
            </div>
            <div className="space-y-1">
              <span className="text-muted-foreground">成立年份</span>
              <p className="font-semibold">{founded}</p>
            </div>
            <div className="space-y-1">
              <span className="text-muted-foreground">总部</span>
              <p className="font-semibold">{headquarters}</p>
            </div>
          </div>
          
          {/* 公司简介 */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <Building className="h-3 w-3" />
              公司简介
            </h4>
            <p className="text-sm text-muted-foreground leading-relaxed">
              {description}
            </p>
          </div>

          {/* 主要业务和产品线 - 3行正文介绍 */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <Package className="h-3 w-3" />
              主要业务和产品线
            </h4>
            <div className="text-sm text-muted-foreground leading-relaxed">
              {isTextTruncated ? (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="cursor-help">
                      {defaultProductDescription.substring(0, 150)}
                      <span className="text-primary hover:underline ml-1">&gt;more</span>
                    </span>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="max-w-md">
                    <p className="text-xs">{defaultProductDescription}</p>
                  </TooltipContent>
                </Tooltip>
              ) : (
                <span>{defaultProductDescription}</span>
              )}
            </div>
          </div>

          {/* 分业务收入变化 - 最近3年数据 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-sm flex items-center gap-2">
                <DollarSign className="h-3 w-3" />
                分业务收入变化（最近3年）
              </h4>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowGrowthRates(!showGrowthRates)}
                  className="text-xs h-6 px-2 text-muted-foreground hover:text-foreground"
                >
                  {showGrowthRates ? (
                    <>
                      <ChevronUp className="h-3 w-3 mr-1" />
                      隐藏增长率
                    </>
                  ) : (
                    <>
                      <ChevronDown className="h-3 w-3 mr-1" />
                      显示增长率
                    </>
                  )}
                </Button>
                <Button
                  variant={showPercentages ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setShowPercentages(!showPercentages)}
                  className="text-xs h-6"
                >
                  {showPercentages ? '营收金额' : '占比显示'}
                </Button>
              </div>
            </div>
            
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-xs">业务板块</TableHead>
                    {currentBusinessRevenue3Years.map((yearData) => (
                      <TableHead key={yearData.year} className="text-xs text-center">
                        {yearData.year}年
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {/* 营收数据行 */}
                  {currentBusinessRevenue3Years[0]?.segments.map((_, segmentIndex) => (
                    <TableRow key={segmentIndex}>
                      <TableCell className="font-medium text-xs">
                        {currentBusinessRevenue3Years[0].segments[segmentIndex].segment}
                      </TableCell>
                      {currentBusinessRevenue3Years.map((yearData) => (
                        <TableCell key={yearData.year} className="text-xs text-center">
                          <div className="font-medium">
                            {showPercentages 
                              ? yearData.segments[segmentIndex]?.percentage
                              : yearData.segments[segmentIndex]?.revenue
                            }
                          </div>
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                  
                  {/* YoY增长率表头和数据 - 可折叠 */}
                  {showGrowthRates && (
                    <>
                      {/* YoY增长率表头 */}
                      <TableRow className="bg-muted/20">
                        <TableCell className="font-semibold text-xs text-primary">
                          YoY增长率
                        </TableCell>
                        {currentBusinessRevenue3Years.map((yearData) => (
                          <TableCell key={yearData.year} className="text-xs text-center font-medium text-primary">
                            {yearData.year}年
                          </TableCell>
                        ))}
                      </TableRow>
                      
                      {/* YoY增长率数据行 */}
                      {currentBusinessRevenue3Years[0]?.segments.map((_, segmentIndex) => (
                        <TableRow key={`yoy-${segmentIndex}`}>
                          <TableCell className="font-medium text-xs text-muted-foreground">
                            {currentBusinessRevenue3Years[0].segments[segmentIndex].segment}
                          </TableCell>
                          {currentBusinessRevenue3Years.map((yearData) => (
                            <TableCell key={yearData.year} className="text-xs text-center">
                              <span className={`text-xs font-medium ${
                                yearData.segments[segmentIndex]?.change.startsWith('+') 
                                  ? 'text-success' 
                                  : 'text-destructive'
                              }`}>
                                {yearData.segments[segmentIndex]?.change}
                              </span>
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}