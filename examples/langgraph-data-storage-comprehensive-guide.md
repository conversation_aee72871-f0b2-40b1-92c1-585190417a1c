# LangGraph 中间数据存储与业务数据使用详细方案

基于对LangGraph官方存储方案和开源社区案例的深入调研，本文档为你提供中间数据存储和业务数据使用的最佳实践方案。

## 🎯 核心概念理解

### 1. LangGraph的数据存储类型

#### **Checkpoint 数据 (检查点数据)**
- **用途**：存储执行状态、图节点状态、消息历史
- **特点**：自动管理，支持断点续传，用于容错和恢复
- **生命周期**：与会话线程绑定

#### **业务数据 (Business Data)**
- **用途**：存储业务逻辑相关的中间结果和最终结果
- **特点**：需要主动管理，与业务流程紧密相关
- **生命周期**：根据业务需求确定

#### **长期记忆数据 (Long-term Memory)**
- **用途**：跨会话的知识积累和用户偏好存储
- **特点**：支持语义搜索，需要向量化存储
- **生命周期**：长期保存

## 🏗️ 官方存储方案详解

### 1. PostgreSQL Checkpointer (推荐生产方案)

```python
# 基础设置
from langgraph.checkpoint.postgres import PostgresSaver
from psycopg_pool import ConnectionPool

# 连接配置
connection_kwargs = {
    "autocommit": True,
    "prepare_threshold": 0,
    "row_factory": dict_row,
}

# 方案一：连接池方式（推荐生产环境）
pool = ConnectionPool(
    conninfo="postgresql://user:password@localhost:5432/database",
    kwargs=connection_kwargs,
    max_size=20,
    min_size=5,
)
checkpointer = PostgresSaver(pool)
await checkpointer.setup()

# 数据库表结构
"""
checkpoints 表：
- thread_id: 会话线程ID
- checkpoint_id: 检查点ID  
- parent_checkpoint_id: 父检查点ID
- checkpoint: JSON格式的状态数据
- metadata: 元数据信息

checkpoint_writes 表：
- thread_id: 会话线程ID
- checkpoint_id: 检查点ID
- task_id: 任务ID
- idx: 写入索引
- channel: 通道名称
- type: 数据类型
- blob: 二进制数据
"""
```

### 2. Redis Checkpointer (高性能方案)

```python
from langgraph.checkpoint.redis import RedisSaver
import redis

# Redis配置
redis_client = redis.Redis(
    host='localhost',
    port=6379,
    db=0,
    decode_responses=False
)

checkpointer = RedisSaver(redis_client)

# 数据结构
"""
Redis键结构：
- checkpoints:{thread_id}:{checkpoint_id}
- checkpoint_writes:{thread_id}:{checkpoint_id}:{task_id}
- index:thread_id -> 线程索引
"""
```

### 3. SQLite Checkpointer (开发测试方案)

```python
from langgraph.checkpoint.sqlite import SqliteSaver

# 本地开发
checkpointer = SqliteSaver.from_conn_string("sqlite:///memory.db")
checkpointer.setup()
```

## 🔧 中间数据存储最佳方案

### 方案一：分层存储架构 (推荐)

```python
from dataclasses import dataclass
from typing import Any, Dict, List, Optional
import json
import asyncio
from datetime import datetime

@dataclass
class DataStorageManager:
    """分层数据存储管理器"""
    
    # 检查点存储器（LangGraph状态）
    checkpointer: BaseCheckpointSaver
    
    # 业务数据存储器（结构化数据）
    business_store: BaseStore
    
    # 向量存储器（语义搜索）
    vector_store: Any
    
    # 缓存存储器（临时数据）
    cache_store: Any

class BusinessDataManager:
    """业务数据管理器"""
    
    def __init__(self, postgres_store: PostgresStore):
        self.store = postgres_store
        
    async def save_intermediate_result(
        self, 
        session_id: str,
        stage: str,
        data: Dict[str, Any],
        metadata: Optional[Dict] = None
    ):
        """保存中间结果"""
        namespace = (session_id, "intermediate_results")
        key = f"{stage}_{datetime.now().isoformat()}"
        
        await self.store.aput(
            namespace=namespace,
            key=key,
            value={
                "stage": stage,
                "data": data,
                "metadata": metadata or {},
                "timestamp": datetime.now().isoformat()
            }
        )
        
    async def get_intermediate_results(
        self, 
        session_id: str, 
        stage: Optional[str] = None
    ) -> List[Dict]:
        """获取中间结果"""
        namespace = (session_id, "intermediate_results")
        
        if stage:
            # 获取特定阶段的结果
            results = await self.store.asearch(
                namespace=namespace,
                query=stage,
                limit=10
            )
        else:
            # 获取所有中间结果
            results = await self.store.asearch(
                namespace=namespace,
                query="",
                limit=100
            )
            
        return [result.value for result in results]
        
    async def save_final_result(
        self,
        session_id: str,
        result_type: str,
        data: Dict[str, Any]
    ):
        """保存最终结果"""
        namespace = (session_id, "final_results")
        key = f"{result_type}_{datetime.now().isoformat()}"
        
        await self.store.aput(
            namespace=namespace,
            key=key,
            value={
                "type": result_type,
                "data": data,
                "created_at": datetime.now().isoformat()
            }
        )
```

### 方案二：混合存储策略

```python
class HybridStorageStrategy:
    """混合存储策略"""
    
    def __init__(
        self,
        postgres_pool: ConnectionPool,
        redis_client: redis.Redis,
        vector_store: Any
    ):
        # PostgreSQL用于持久化存储
        self.postgres_store = PostgresStore(postgres_pool)
        
        # Redis用于缓存和临时数据
        self.redis_client = redis_client
        
        # 向量数据库用于语义搜索
        self.vector_store = vector_store
        
    async def store_by_data_type(
        self,
        data_type: str,
        session_id: str,
        data: Any
    ):
        """根据数据类型选择存储策略"""
        
        if data_type == "checkpoint":
            # 检查点数据 -> PostgreSQL
            await self._store_checkpoint(session_id, data)
            
        elif data_type == "cache":
            # 缓存数据 -> Redis (带TTL)
            await self._store_cache(session_id, data, ttl=3600)
            
        elif data_type == "business":
            # 业务数据 -> PostgreSQL + Redis缓存
            await self._store_business_data(session_id, data)
            
        elif data_type == "semantic":
            # 语义数据 -> 向量数据库
            await self._store_semantic_data(session_id, data)
            
    async def _store_checkpoint(self, session_id: str, data: Any):
        """存储检查点数据"""
        # 由LangGraph自动管理
        pass
        
    async def _store_cache(self, session_id: str, data: Any, ttl: int):
        """存储缓存数据"""
        key = f"cache:{session_id}:{datetime.now().timestamp()}"
        await self.redis_client.setex(
            key, 
            ttl, 
            json.dumps(data, default=str)
        )
        
    async def _store_business_data(self, session_id: str, data: Any):
        """存储业务数据"""
        # 持久化到PostgreSQL
        namespace = (session_id, "business")
        await self.postgres_store.aput(
            namespace=namespace,
            key=f"data_{datetime.now().timestamp()}",
            value=data
        )
        
        # 缓存到Redis
        cache_key = f"business:{session_id}:latest"
        await self.redis_client.setex(
            cache_key,
            1800,  # 30分钟缓存
            json.dumps(data, default=str)
        )
        
    async def _store_semantic_data(self, session_id: str, data: Any):
        """存储语义数据到向量数据库"""
        # 实现向量存储逻辑
        pass
```

## 📊 业务数据使用模式

### 1. 事件驱动的数据处理

```python
from typing import Protocol, Callable
from enum import Enum

class DataEvent(Enum):
    """数据事件类型"""
    INTERMEDIATE_RESULT_SAVED = "intermediate_result_saved"
    FINAL_RESULT_GENERATED = "final_result_generated"
    ERROR_OCCURRED = "error_occurred"
    STAGE_COMPLETED = "stage_completed"

class DataEventHandler(Protocol):
    """数据事件处理器接口"""
    async def handle(self, event: DataEvent, data: Dict[str, Any]) -> None:
        ...

class BusinessDataProcessor:
    """业务数据处理器"""
    
    def __init__(self, storage_manager: DataStorageManager):
        self.storage = storage_manager
        self.event_handlers: Dict[DataEvent, List[DataEventHandler]] = {}
        
    def register_handler(
        self, 
        event: DataEvent, 
        handler: DataEventHandler
    ):
        """注册事件处理器"""
        if event not in self.event_handlers:
            self.event_handlers[event] = []
        self.event_handlers[event].append(handler)
        
    async def emit_event(self, event: DataEvent, data: Dict[str, Any]):
        """触发事件"""
        handlers = self.event_handlers.get(event, [])
        await asyncio.gather(*[
            handler.handle(event, data) for handler in handlers
        ])
        
    async def process_node_result(
        self,
        node_name: str,
        session_id: str,
        result: Any,
        is_final: bool = False
    ):
        """处理节点结果"""
        
        # 保存中间结果
        await self.storage.business_store.aput(
            namespace=(session_id, "node_results"),
            key=f"{node_name}_{datetime.now().timestamp()}",
            value={
                "node": node_name,
                "result": result,
                "is_final": is_final,
                "timestamp": datetime.now().isoformat()
            }
        )
        
        # 触发事件
        if is_final:
            await self.emit_event(
                DataEvent.FINAL_RESULT_GENERATED,
                {"node": node_name, "result": result}
            )
        else:
            await self.emit_event(
                DataEvent.INTERMEDIATE_RESULT_SAVED,
                {"node": node_name, "result": result}
            )
```

### 2. 数据生命周期管理

```python
from datetime import timedelta

class DataLifecycleManager:
    """数据生命周期管理器"""
    
    def __init__(self, storage_manager: DataStorageManager):
        self.storage = storage_manager
        
    async def cleanup_expired_data(self):
        """清理过期数据"""
        
        # 清理超过30天的中间结果
        cutoff_date = datetime.now() - timedelta(days=30)
        await self._cleanup_intermediate_results(cutoff_date)
        
        # 清理超过7天的缓存数据
        cache_cutoff = datetime.now() - timedelta(days=7)
        await self._cleanup_cache_data(cache_cutoff)
        
    async def _cleanup_intermediate_results(self, cutoff_date: datetime):
        """清理中间结果"""
        # 实现清理逻辑
        pass
        
    async def archive_completed_sessions(self, session_ids: List[str]):
        """归档已完成的会话"""
        for session_id in session_ids:
            # 将数据移动到归档存储
            await self._move_to_archive(session_id)
            
    async def _move_to_archive(self, session_id: str):
        """移动数据到归档存储"""
        # 实现归档逻辑
        pass
```

## 🚀 生产环境最佳实践

### 1. 性能优化配置

```python
# PostgreSQL优化配置
POSTGRES_CONFIG = {
    "connection_pool": {
        "min_size": 5,
        "max_size": 20,
        "max_inactive_connection_lifetime": 300,
    },
    "connection_kwargs": {
        "autocommit": True,
        "prepare_threshold": 0,
        "row_factory": dict_row,
    }
}

# Redis优化配置
REDIS_CONFIG = {
    "connection_pool": {
        "max_connections": 50,
        "socket_keepalive": True,
        "socket_keepalive_options": {},
    },
    "serialization": {
        "json_serializer": orjson.dumps,
        "json_deserializer": orjson.loads,
    }
}
```

### 2. 监控和日志

```python
import logging
from typing import Any
import time

class StorageMonitor:
    """存储监控器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.metrics = {}
        
    async def monitor_storage_operation(
        self,
        operation: str,
        storage_type: str,
        data_size: int
    ):
        """监控存储操作"""
        start_time = time.time()
        
        try:
            yield  # 执行实际操作
            
            duration = time.time() - start_time
            self.logger.info(
                f"Storage operation completed",
                extra={
                    "operation": operation,
                    "storage_type": storage_type,
                    "data_size": data_size,
                    "duration": duration
                }
            )
            
        except Exception as e:
            self.logger.error(
                f"Storage operation failed",
                extra={
                    "operation": operation,
                    "storage_type": storage_type,
                    "error": str(e)
                }
            )
            raise
```

### 3. 错误处理和重试机制

```python
import asyncio
from functools import wraps

def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        await asyncio.sleep(delay * (2 ** attempt))
                        continue
                    break
                    
            raise last_exception
        return wrapper
    return decorator

class RobustStorageManager:
    """健壮的存储管理器"""
    
    @retry_on_failure(max_retries=3)
    async def safe_store_data(
        self, 
        storage_type: str, 
        data: Any
    ):
        """安全存储数据"""
        try:
            if storage_type == "postgres":
                await self._store_to_postgres(data)
            elif storage_type == "redis":
                await self._store_to_redis(data)
            else:
                raise ValueError(f"Unknown storage type: {storage_type}")
                
        except Exception as e:
            # 记录错误并重试
            logging.error(f"Failed to store data: {e}")
            raise
```

## 📋 总结与建议

### 推荐的存储架构

1. **检查点数据**：使用PostgreSQL Checkpointer
2. **业务中间数据**：PostgreSQL Store + Redis缓存
3. **长期记忆数据**：PostgreSQL + pgvector扩展
4. **临时缓存数据**：Redis (带TTL)

### 性能优化要点

1. **连接池**：使用连接池管理数据库连接
2. **批量操作**：批量写入减少I/O开销
3. **异步处理**：使用异步I/O提高并发性能
4. **缓存策略**：热数据缓存，冷数据归档

### 生产部署建议

1. **监控告警**：实时监控存储性能和错误率
2. **备份策略**：定期备份重要数据
3. **扩展性**：设计支持水平扩展的架构
4. **安全性**：数据加密和访问控制

这套方案提供了完整的LangGraph数据存储解决方案，可以根据具体业务需求进行调整和优化。 