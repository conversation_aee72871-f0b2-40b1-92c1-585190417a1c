# LangGraph 官方任务状态管理方案详解

根据你的需求，这里是LangGraph官方推荐的任务状态管理解决方案，主要通过LangGraph Platform和内置组件实现。

## 🎯 LangGraph官方任务状态管理架构

### 1. **LangGraph Platform** (官方推荐的企业级方案)

#### **核心功能**
- **任务执行状态跟踪**：自动记录每个任务的执行状态
- **Checkpointer系统**：内置的状态持久化机制
- **Thread管理**：会话级别的任务状态管理
- **LangGraph Studio**：可视化任务状态监控

#### **任务状态表结构**
```sql
-- LangGraph Platform内置的任务状态表
checkpoints 表：
- thread_id: VARCHAR(255) -- 线程/会话ID
- checkpoint_id: VARCHAR(255) -- 检查点ID
- parent_checkpoint_id: VARCHAR(255) -- 父检查点ID
- type: VARCHAR(50) -- 任务类型
- checkpoint: JSONB -- 完整状态数据
- metadata: JSONB -- 任务元数据
- created_at: TIMESTAMP
- updated_at: TIMESTAMP

checkpoint_writes 表：
- thread_id: VARCHAR(255)
- checkpoint_id: VARCHAR(255)
- task_id: VARCHAR(255) -- 任务唯一标识
- idx: INTEGER -- 写入顺序
- channel: VARCHAR(255) -- 通道名称
- type: VARCHAR(50) -- 写入类型
- blob: BYTEA -- 序列化数据
```

### 2. **内置Checkpointer组件**

#### **PostgresSaver (生产推荐)**
```python
from langgraph.checkpoint.postgres import PostgresSaver
from psycopg_pool import ConnectionPool

# 任务状态管理配置
connection_kwargs = {
    "autocommit": True,
    "prepare_threshold": 0,
    "row_factory": dict_row,
}

# 创建连接池
pool = ConnectionPool(
    conninfo="postgresql://user:pass@host:port/db",
    kwargs=connection_kwargs,
    max_size=20,
    min_size=5,
)

# 初始化任务状态管理器
task_state_manager = PostgresSaver(pool)
await task_state_manager.setup()

# 编译图时集成任务状态管理
graph = workflow.compile(checkpointer=task_state_manager)
```

#### **任务状态查询和管理**
```python
class TaskStateManager:
    """LangGraph官方任务状态管理器"""
    
    def __init__(self, checkpointer: PostgresSaver):
        self.checkpointer = checkpointer
        
    async def get_task_status(self, thread_id: str, task_id: str = None) -> Dict:
        """获取任务状态"""
        config = {"configurable": {"thread_id": thread_id}}
        
        if task_id:
            # 获取特定任务状态
            checkpoint = await self.checkpointer.aget(config)
            return self._extract_task_status(checkpoint, task_id)
        else:
            # 获取线程所有任务状态
            return await self._get_thread_status(thread_id)
    
    async def get_task_history(self, thread_id: str) -> List[Dict]:
        """获取任务执行历史"""
        config = {"configurable": {"thread_id": thread_id}}
        
        # 获取所有检查点历史
        history = []
        async for checkpoint in self.checkpointer.alist(config):
            history.append({
                "checkpoint_id": checkpoint.config["configurable"]["checkpoint_id"],
                "timestamp": checkpoint.metadata.get("timestamp"),
                "step": checkpoint.metadata.get("step", 0),
                "status": self._get_checkpoint_status(checkpoint),
                "tasks": self._extract_tasks_from_checkpoint(checkpoint)
            })
        
        return sorted(history, key=lambda x: x["timestamp"])
    
    async def update_task_status(
        self, 
        thread_id: str, 
        task_id: str, 
        status: str,
        result: Any = None,
        error: str = None
    ):
        """更新任务状态"""
        config = {"configurable": {"thread_id": thread_id}}
        
        # 获取当前状态
        current_state = await self.checkpointer.aget(config)
        
        # 更新任务状态
        if current_state:
            state_values = current_state.values.copy()
            
            # 更新任务信息
            if "task_statuses" not in state_values:
                state_values["task_statuses"] = {}
                
            state_values["task_statuses"][task_id] = {
                "status": status,
                "result": result,
                "error": error,
                "updated_at": datetime.now().isoformat()
            }
            
            # 保存更新后的状态
            await self.checkpointer.aput(
                config=config,
                checkpoint={
                    **current_state.checkpoint,
                    "channel_values": state_values
                },
                metadata={
                    **current_state.metadata,
                    "task_update": {
                        "task_id": task_id,
                        "status": status,
                        "timestamp": datetime.now().isoformat()
                    }
                },
                writes=[]
            )
    
    def _extract_task_status(self, checkpoint, task_id: str) -> Dict:
        """从检查点提取任务状态"""
        if not checkpoint:
            return {"status": "not_found"}
            
        task_statuses = checkpoint.values.get("task_statuses", {})
        return task_statuses.get(task_id, {"status": "unknown"})
    
    def _get_checkpoint_status(self, checkpoint) -> str:
        """获取检查点状态"""
        if checkpoint.metadata.get("step") == -1:
            return "failed"
        elif checkpoint.next:
            return "running"
        else:
            return "completed"
```

### 3. **集成到你的AGUI研究系统**

#### **为投资分析图添加任务状态管理**
```python
from datetime import datetime
from typing import Any, Dict, Optional
from langgraph.types import Command

class InvestmentAnalysisState(TypedDict):
    """投资分析状态，集成任务管理"""
    
    # 原有业务字段
    profile: str
    fact_check_questions: List[str]
    initial_analysis_results: List[str]
    
    # 任务状态管理字段
    task_statuses: Dict[str, Dict[str, Any]]  # {task_id: {status, result, error}}
    current_stage: str
    stage_progress: Dict[str, Any]
    execution_metadata: Dict[str, Any]

async def generate_fact_questions_with_tracking(
    state: InvestmentAnalysisState, 
    config: RunnableConfig, 
    nodeName: str
):
    """生成事实核查问题（带任务状态跟踪）"""
    
    # 生成任务ID
    task_id = f"{nodeName}_{datetime.now().timestamp()}"
    
    # 更新任务状态：开始执行
    await update_task_status(state, task_id, "running", config)
    
    try:
        # 原有业务逻辑
        configurable = VestConfiguration.from_runnable_config(config)
        node_config = get_node_config(nodeName)
        
        # ... 执行业务逻辑 ...
        
        # 更新任务状态：执行成功
        await update_task_status(
            state, 
            task_id, 
            "completed", 
            config,
            result={"questions_count": len(result.questions)}
        )
        
        yield {
            "fact_check_questions": result.questions,
            "task_statuses": state.get("task_statuses", {}),
            "current_stage": node_config.stage.value
        }
        
    except Exception as e:
        # 更新任务状态：执行失败
        await update_task_status(
            state, 
            task_id, 
            "failed", 
            config,
            error=str(e)
        )
        raise

async def update_task_status(
    state: InvestmentAnalysisState,
    task_id: str,
    status: str,
    config: RunnableConfig,
    result: Any = None,
    error: str = None
):
    """更新任务状态的辅助函数"""
    
    if "task_statuses" not in state:
        state["task_statuses"] = {}
    
    state["task_statuses"][task_id] = {
        "status": status,
        "result": result,
        "error": error,
        "timestamp": datetime.now().isoformat(),
        "thread_id": config.get("configurable", {}).get("thread_id"),
        "node_name": task_id.split("_")[0]
    }
```

### 4. **任务状态监控和查询API**

#### **RESTful API接口**
```python
from fastapi import FastAPI, HTTPException
from typing import Optional

app = FastAPI()

@app.get("/api/tasks/{thread_id}/status")
async def get_task_status(
    thread_id: str,
    task_id: Optional[str] = None
):
    """获取任务状态API"""
    try:
        task_manager = TaskStateManager(checkpointer)
        status = await task_manager.get_task_status(thread_id, task_id)
        return {"success": True, "data": status}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/tasks/{thread_id}/history")
async def get_task_history(thread_id: str):
    """获取任务历史API"""
    try:
        task_manager = TaskStateManager(checkpointer)
        history = await task_manager.get_task_history(thread_id)
        return {"success": True, "data": history}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/tasks/{thread_id}/cancel")
async def cancel_task(thread_id: str, task_id: str):
    """取消任务API"""
    try:
        # 实现任务取消逻辑
        task_manager = TaskStateManager(checkpointer)
        await task_manager.update_task_status(
            thread_id, 
            task_id, 
            "cancelled"
        )
        return {"success": True, "message": "Task cancelled"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### 5. **实时状态监控**

#### **WebSocket状态推送**
```python
import asyncio
from fastapi import WebSocket

class TaskStatusBroadcaster:
    """任务状态实时广播器"""
    
    def __init__(self):
        self.connections: Dict[str, List[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, thread_id: str):
        """建立WebSocket连接"""
        await websocket.accept()
        if thread_id not in self.connections:
            self.connections[thread_id] = []
        self.connections[thread_id].append(websocket)
    
    async def disconnect(self, websocket: WebSocket, thread_id: str):
        """断开WebSocket连接"""
        if thread_id in self.connections:
            self.connections[thread_id].remove(websocket)
    
    async def broadcast_status_update(
        self, 
        thread_id: str, 
        task_update: Dict
    ):
        """广播任务状态更新"""
        if thread_id in self.connections:
            for websocket in self.connections[thread_id]:
                try:
                    await websocket.send_json(task_update)
                except:
                    # 连接已断开，移除
                    await self.disconnect(websocket, thread_id)

# WebSocket端点
@app.websocket("/ws/tasks/{thread_id}")
async def websocket_endpoint(websocket: WebSocket, thread_id: str):
    broadcaster = TaskStatusBroadcaster()
    await broadcaster.connect(websocket, thread_id)
    
    try:
        while True:
            # 保持连接活跃
            await asyncio.sleep(1)
    except:
        await broadcaster.disconnect(websocket, thread_id)
```

## 🎯 关键优势

### 1. **官方支持**
- LangGraph团队维护和支持
- 与LangGraph核心功能深度集成
- 版本兼容性保证

### 2. **企业级可靠性**
- 内置错误处理和恢复机制
- 支持分布式部署
- 高可用性设计

### 3. **开箱即用**
- 无需额外开发任务状态管理
- 自动状态持久化
- 内置监控和调试工具

### 4. **可扩展性**
- 支持水平扩展
- 与Kubernetes集成
- 云原生架构

## 📊 实施建议

### 短期实施 (1-2周)
1. 集成PostgresSaver作为checkpointer
2. 在现有节点函数中添加状态跟踪
3. 创建基础的状态查询API

### 中期优化 (1个月)
1. 实现WebSocket实时状态推送
2. 添加任务取消和重试机制
3. 集成监控和告警系统

### 长期规划 (3个月)
1. 迁移到LangGraph Platform
2. 使用LangGraph Studio进行可视化监控
3. 实现完整的任务生命周期管理

这套官方方案完全解决了你提到的"前端关闭SSE连接但任务仍在执行"的问题，通过LangGraph的内置状态管理机制，能够可靠地跟踪和管理所有任务状态。 