# LangGraph异步任务取消方案

## 方案一：使用AbortController进行任务取消

### 前端实现（Next.js）

```typescript
// apps/web-app/src/hooks/useResearchStream.ts
import { useCallback, useRef, useState } from 'react';

export function useResearchStream() {
  const [isStreaming, setIsStreaming] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  const startResearch = useCallback(async (query: string) => {
    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // 创建新的AbortController
    abortControllerRef.current = new AbortController();
    setIsStreaming(true);

    try {
      const response = await fetch('/api/research/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query }),
        signal: abortControllerRef.current.signal, // 传递取消信号
      });

      if (!response.ok) throw new Error('Network response was not ok');
      if (!response.body) throw new Error('ReadableStream not supported');

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        // 处理SSE数据...
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('Request was cancelled');
      } else {
        console.error('Stream error:', error);
      }
    } finally {
      setIsStreaming(false);
      abortControllerRef.current = null;
    }
  }, []);

  const cancelResearch = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // 组件卸载时取消请求
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return { startResearch, cancelResearch, isStreaming };
}
```

### 后端API改进

```typescript
// apps/web-app/src/app/api/research/chat/route.ts
import { NextRequest } from 'next/server';

export async function POST(request: NextRequest) {
  const traceId = generateServerTraceId();
  const startTime = Date.now();
  
  try {
    const body = await request.json();
    
    // 创建AbortController用于取消后端请求
    const controller = new AbortController();
    
    // 监听客户端断开连接
    request.signal.addEventListener('abort', () => {
      controller.abort();
    });

    // 转发请求到后端，传递取消信号
    const response = await fetch('http://localhost:8000/research-v2b/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Trace-ID': traceId,
      },
      body: JSON.stringify(body),
      signal: controller.signal, // 传递取消信号
    });

    // 返回流式响应
    return new Response(response.body, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'X-Trace-ID': traceId,
      },
    });
  } catch (error: any) {
    if (error.name === 'AbortError') {
      return new Response('Request cancelled', { status: 499 });
    }
    
    return Response.json({ 
      error: 'Internal Server Error',
      traceId 
    }, { status: 500 });
  }
}
```

## 方案二：LangGraph后端任务管理

### 1. 任务管理器实现

```python
# libs/research-v2b-bs/src/research_v2b_bs/api/task_manager.py
import asyncio
import uuid
from typing import Dict, Optional
from dataclasses import dataclass
from enum import Enum

class TaskStatus(Enum):
    RUNNING = "running"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    FAILED = "failed"

@dataclass
class TaskInfo:
    task_id: str
    status: TaskStatus
    task: Optional[asyncio.Task] = None
    result: Optional[any] = None
    error: Optional[str] = None

class TaskManager:
    def __init__(self):
        self._tasks: Dict[str, TaskInfo] = {}
    
    def create_task(self, coro, task_id: str = None) -> str:
        """创建并启动新任务"""
        if task_id is None:
            task_id = str(uuid.uuid4())
        
        task = asyncio.create_task(coro)
        task_info = TaskInfo(
            task_id=task_id,
            status=TaskStatus.RUNNING,
            task=task
        )
        
        self._tasks[task_id] = task_info
        
        # 添加完成回调
        task.add_done_callback(lambda t: self._on_task_done(task_id, t))
        
        return task_id
    
    def cancel_task(self, task_id: str) -> bool:
        """取消指定任务"""
        task_info = self._tasks.get(task_id)
        if task_info and task_info.task and not task_info.task.done():
            task_info.task.cancel()
            task_info.status = TaskStatus.CANCELLED
            return True
        return False
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        task_info = self._tasks.get(task_id)
        return task_info.status if task_info else None
    
    def _on_task_done(self, task_id: str, task: asyncio.Task):
        """任务完成回调"""
        task_info = self._tasks.get(task_id)
        if not task_info:
            return
        
        if task.cancelled():
            task_info.status = TaskStatus.CANCELLED
        elif task.exception():
            task_info.status = TaskStatus.FAILED
            task_info.error = str(task.exception())
        else:
            task_info.status = TaskStatus.COMPLETED
            task_info.result = task.result()
    
    def cleanup_completed_tasks(self):
        """清理已完成的任务"""
        completed_task_ids = [
            task_id for task_id, task_info in self._tasks.items()
            if task_info.status in [TaskStatus.COMPLETED, TaskStatus.CANCELLED, TaskStatus.FAILED]
        ]
        
        for task_id in completed_task_ids:
            del self._tasks[task_id]

# 全局任务管理器实例
task_manager = TaskManager()
```

### 2. 改进的AGUI适配器

```python
# libs/research-v2b-bs/src/research_v2b_bs/api/adapter/agui_adapter.py
import asyncio
from .task_manager import task_manager, TaskStatus

class AGUIAdapter:
    async def stream_events_with_cancellation(self, task: Task) -> AsyncGenerator[BaseEvent, None]:
        """带取消功能的事件流适配器"""
        event_translator = EventTranslator()
        
        try:
            effective_thread_id = task.thread_id if task.thread_id else task.id
            
            # 产生开始事件
            run_started = RunStartedEvent(
                type=EventType.RUN_STARTED,
                thread_id=effective_thread_id,
                run_id=task.id,
            )
            yield run_started
            
            # 创建可取消的LangGraph执行任务
            async def run_langgraph():
                if task.user_feedback:
                    input_data = Command(resume=task.user_feedback)
                else:
                    input_data = {
                        "messages": [HumanMessage(content=task.query)],
                        "profile": task.query,
                        # ... 其他初始状态
                    }
                
                config = RunnableConfig(
                    configurable={"thread_id": effective_thread_id},
                    tags=["research", "investment_analysis"],
                    metadata={"task_id": task.id, "thread_id": effective_thread_id}
                )
                
                async for event in self.agent.astream_events(input_data, config=config, version="v2"):
                    # 检查任务是否被取消
                    if task_manager.get_task_status(task.id) == TaskStatus.CANCELLED:
                        raise asyncio.CancelledError("Task was cancelled")
                    
                    async for ag_ui_event in event_translator.translate_event(event):
                        yield ag_ui_event
            
            # 将LangGraph执行注册为可管理的任务
            langgraph_task_id = task_manager.create_task(run_langgraph(), task.id)
            
            try:
                async for event in run_langgraph():
                    yield event
            except asyncio.CancelledError:
                logger.info(f"Task {task.id} was cancelled")
                # 发送取消事件
                cancel_event = RunErrorEvent(
                    type=EventType.RUN_ERROR, 
                    message="Task was cancelled by user"
                )
                yield cancel_event
                return
                
            # 发送完成事件
            run_finished = RunFinishedEvent(
                type=EventType.RUN_FINISHED,
                thread_id=effective_thread_id,
                run_id=task.id,
            )
            yield run_finished
            
        except Exception as e:
            logger.exception(f"AGUIAdapter error: {e}")
            error_event = RunErrorEvent(type=EventType.RUN_ERROR, message=str(e))
            yield error_event
        finally:
            # 清理任务
            task_manager.cancel_task(task.id)
```

### 3. 改进的API端点

```python
# libs/research-v2b-bs/src/research_v2b_bs/api/adapter/research_agent.py
from fastapi import HTTPException, Request
from .task_manager import task_manager

@app.post("/research/stream")
async def research_stream(request: ResearchRequest, http_request: Request):
    """带取消功能的投资研究分析流式端点"""
    try:
        task_id = str(uuid.uuid4())
        task = Task(
            id=task_id,
            query=request.query,
            thread_id=request.thread_id
        )
        
        adapter = get_agui_adapter()
        
        async def event_stream():
            try:
                # 检查客户端是否断开连接
                async def check_client_disconnect():
                    while True:
                        if await http_request.is_disconnected():
                            task_manager.cancel_task(task_id)
                            break
                        await asyncio.sleep(1)
                
                # 启动断连检测任务
                disconnect_task = asyncio.create_task(check_client_disconnect())
                
                try:
                    async for event_data in adapter.stream_events_with_cancellation(task):
                        yield event_data
                finally:
                    disconnect_task.cancel()
                    
            except asyncio.CancelledError:
                logger.info(f"Stream for task {task_id} was cancelled")
                yield f'data: {{"type": "RUN_CANCELLED", "message": "Task cancelled"}}\n\n'
            except Exception as e:
                logger.exception(f"Error in event stream for task {task_id}: {e}")
                yield f'data: {{"type": "RUN_ERROR", "message": "{str(e)}"}}\n\n'
        
        return StreamingResponse(
            event_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
            }
        )
        
    except Exception as e:
        logger.exception(f"Failed to start research stream: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/research/cancel/{task_id}")
async def cancel_research_task(task_id: str):
    """手动取消任务的端点"""
    success = task_manager.cancel_task(task_id)
    if success:
        return {"message": f"Task {task_id} cancelled successfully"}
    else:
        raise HTTPException(status_code=404, detail="Task not found or already completed")
```

## 方案三：使用LangGraph Platform的官方方案

根据搜索结果，LangGraph官方推荐使用LangGraph Platform来部署，它提供了更好的任务管理和取消机制：

```python
# 使用LangGraph Platform的官方SDK
from langgraph_sdk import get_client

async def use_langgraph_platform():
    client = get_client(url="your-langgraph-platform-url")
    
    # 创建线程
    thread = await client.threads.create()
    
    # 启动流式任务
    async for chunk in client.runs.stream(
        thread["thread_id"],
        "your-graph",
        input={"query": "your query"},
        stream_mode="values"
    ):
        # 处理chunk
        print(chunk)
        
        # 可以通过API取消任务
        # await client.runs.cancel(thread_id, run_id)
```

## 总结

1. **AbortController方案**：适合现有架构，实现相对简单
2. **任务管理器方案**：提供更细粒度的控制，适合复杂场景
3. **LangGraph Platform**：官方推荐，功能最完整但需要迁移

建议先实现方案一，然后逐步向方案二或三演进。 