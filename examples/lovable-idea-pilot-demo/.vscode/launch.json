{"version": "0.2.0", "configurations": [{"name": "🎯 Dev Server (Tailwind v3)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "console": "integratedTerminal", "env": {"NODE_OPTIONS": "--inspect=9230", "NODE_ENV": "development"}, "restart": true, "sourceMaps": true, "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"], "skipFiles": ["<node_internals>/**", "**/node_modules/**"]}, {"name": "🔨 Build Project", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "build"], "console": "integratedTerminal"}, {"name": "👀 Preview Build", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "preview"], "console": "integratedTerminal"}, {"name": "🔍 Lint Code", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "npm", "runtimeArgs": ["run", "lint"], "console": "integratedTerminal"}, {"name": "🔗 Attach to Dev Server", "type": "node", "request": "attach", "port": 9230, "localRoot": "${workspaceFolder}", "remoteRoot": "/app"}]}