import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { She<PERSON>, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { FileText, RefreshCw, Clock, Edit3, Trash2, Send, MessageCircle, Hash } from "lucide-react";
interface FutureProjectionProps {
  ideaId: number;
  isReadOnly?: boolean;
}
interface ThinkingReport {
  id: string;
  version: string;
  timestamp: string;
  title: string;
  content: string;
  status: "latest" | "previous";
}
interface ChatMessage {
  id: string;
  type: "user" | "ai";
  content: string;
  timestamp: string;
}
export function FutureProjection({
  ideaId,
  isReadOnly = false
}: FutureProjectionProps) {
  const [reports, setReports] = useState<ThinkingReport[]>([{
    id: "v1.0",
    version: "v1.0",
    timestamp: "2024-01-15 14:30",
    title: "初步分析报告",
    content: "基于当前H200芯片发布的市场反应，初步判断NVIDIA在AI芯片领域的领导地位将进一步巩固。主要驱动因素包括：1）性能提升显著：相比H100，H200在训练效率上提升30%；2）成本优势明显：单位算力成本下降约20%；3）生态系统完善：CUDA平台的广泛支持...",
    status: "previous"
  }, {
    id: "v1.1",
    version: "v1.1",
    timestamp: "2024-01-16 09:15",
    title: "市场竞争分析",
    content: "在深入分析竞争对手动态后，更新判断如下：AMD的MI300X虽然在某些场景下性能接近，但在软件生态和开发者支持方面仍有差距。Intel的Gaudi系列主要面向推理场景，与H200的训练优化定位存在差异化。预计未来6个月内，NVIDIA将保持在高端AI训练芯片市场的主导地位...",
    status: "previous"
  }, {
    id: "v2.0",
    version: "v2.0",
    timestamp: "2024-01-18 16:45",
    title: "综合思考报告",
    content: "综合技术分析、市场动态和财务数据，形成以下核心判断：\n\n**技术优势分析**\n• H200采用HBM3e内存，带宽提升至4.8TB/s，相比H100提升约20%\n• 支持更大规模模型训练，单卡可支持80GB显存\n• 能效比优化，训练相同模型功耗降低15%\n\n**市场影响评估**\n• 短期（3-6个月）：主要云服务商加速采购，预计带来15-20亿美元新增收入\n• 中期（6-12个月）：AI应用开发门槛降低，催生新一轮创业潮\n• 长期（1-2年）：可能触发AGI技术突破，重塑整个AI产业格局\n\n**投资建议**\n基于以上分析，维持NVIDIA强烈买入评级，目标价位上调至$580。主要风险因素包括地缘政治限制和潜在的技术颠覆。",
    status: "latest"
  }]);
  const [selectedReport, setSelectedReport] = useState<string>("v2.0");
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [messageInput, setMessageInput] = useState("");
  const [isAiTyping, setIsAiTyping] = useState(false);
  const currentReport = reports.find(r => r.id === selectedReport) || reports[reports.length - 1];

  // 解析报告内容为可导航的章节
  const parseReportSections = (content: string) => {
    const sections = content.split('\n\n').filter(section => section.trim() !== '');
    return sections.map((section, index) => {
      const lines = section.split('\n');
      const title = lines[0].replace(/^\*\*/, '').replace(/\*\*$/, '').trim();
      return {
        id: `section-${index}`,
        title: title.startsWith('•') ? '主要观点' : title,
        content: section
      };
    });
  };
  const reportSections = parseReportSections(currentReport.content);
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };
  const handleGenerateReport = () => {
    const newVersion = `v${reports.length + 1}.0`;
    const newReport: ThinkingReport = {
      id: newVersion,
      version: newVersion,
      timestamp: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      }),
      title: "更新思考报告",
      content: "正在基于最新信息生成思考报告...",
      status: "latest"
    };
    setReports(prev => [...prev.map(r => ({
      ...r,
      status: "previous" as const
    })), newReport]);
    setSelectedReport(newVersion);
  };
  const handleEditReport = () => {
    setIsEditSheetOpen(true);
    setChatMessages([{
      id: "welcome",
      type: "ai",
      content: `你好！我是AI助手。当前正在编辑报告 "${currentReport.title}" (${currentReport.version})。你可以：\n\n1. 告诉我如何修改报告内容\n2. 询问关于报告的问题\n3. 请求重新分析某个部分\n\n请告诉我你需要什么帮助？`,
      timestamp: new Date().toLocaleString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }]);
  };
  const handleDeleteReport = () => {
    if (confirm(`确定要删除报告 "${currentReport.title}" (${currentReport.version}) 吗？`)) {
      setReports(prev => prev.filter(r => r.id !== selectedReport));
      const remainingReports = reports.filter(r => r.id !== selectedReport);
      if (remainingReports.length > 0) {
        setSelectedReport(remainingReports[remainingReports.length - 1].id);
      }
    }
  };
  const handleSendMessage = () => {
    if (!messageInput.trim()) return;
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      type: "user",
      content: messageInput,
      timestamp: new Date().toLocaleString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    };
    setChatMessages(prev => [...prev, userMessage]);
    setMessageInput("");
    setIsAiTyping(true);

    // 模拟AI回复
    setTimeout(() => {
      const aiMessage: ChatMessage = {
        id: `ai-${Date.now()}`,
        type: "ai",
        content: `基于你的要求："${messageInput}"，我建议对报告进行以下调整：\n\n• 增强技术分析的深度\n• 补充更多市场数据支撑\n• 优化投资建议的论证逻辑\n\n这些修改将会让报告更加完善。是否需要我立即应用这些更改？`,
        timestamp: new Date().toLocaleString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      };
      setChatMessages(prev => [...prev, aiMessage]);
      setIsAiTyping(false);
    }, 2000);
  };
  const closeEditSheet = () => {
    setIsEditSheetOpen(false);
    setChatMessages([]);
  };
  return <>
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <FileText className="h-5 w-5" />
            综合研究报告
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-5 gap-4 h-[400px]">
            {/* 左侧：更新思考记录 */}
            <div className="col-span-1 border-r pr-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-medium">更新思考记录</h4>
              </div>
              <div className="space-y-2 max-h-[320px] overflow-y-auto">
                {reports.slice(-10).map(report => <div key={report.id} onClick={() => setSelectedReport(report.id)} className={`p-2 rounded-md border cursor-pointer transition-colors text-xs ${selectedReport === report.id ? "border-primary bg-primary/5" : "border-border hover:bg-muted/50"}`}>
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium">{report.version}</span>
                      {report.status === "latest" && <Badge variant="default" className="text-xs px-1 py-0">
                          最新
                        </Badge>}
                    </div>
                    <div className="flex items-center gap-1 text-muted-foreground mb-1">
                      <Clock className="h-3 w-3" />
                      <span>{report.timestamp}</span>
                    </div>
                    <p className="text-muted-foreground truncate">{report.title}</p>
                  </div>)}
              </div>
            </div>

            {/* 右侧：详细报告内容 */}
            <div className="col-span-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h4 className="text-sm font-medium mb-1">{currentReport.title}</h4>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <span>{currentReport.version}</span>
                    <span>•</span>
                    <span>{currentReport.timestamp}</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {!isReadOnly && (
                    <>
                      <Button size="sm" variant="outline" onClick={handleEditReport} className="h-7 px-2">
                        <Edit3 className="h-3 w-3 mr-1" />
                        编辑
                      </Button>
                      <Button size="sm" variant="outline" onClick={handleDeleteReport} className="h-7 px-2 text-destructive hover:text-destructive">
                        <Trash2 className="h-3 w-3 mr-1" />
                        删除
                      </Button>
                    </>
                  )}
                </div>
              </div>
              <ScrollArea className="h-[320px]">
                <div className="text-sm leading-relaxed pr-4">
                  {reportSections.map((section, index) => <div key={section.id} id={section.id} className="mb-4">
                      <div className="whitespace-pre-line">
                        {section.content}
                      </div>
                    </div>)}
                </div>
              </ScrollArea>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 右侧弹出的编辑面板 */}
      <Sheet open={isEditSheetOpen} onOpenChange={setIsEditSheetOpen}>
        <SheetContent className="w-[500px] sm:w-[600px]">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5 text-primary" />
              AI编辑助手
            </SheetTitle>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-xs">
                编辑中: {currentReport.version}
              </Badge>
              <span className="text-xs text-muted-foreground">
                {currentReport.title}
              </span>
            </div>
          </SheetHeader>
          
          <div className="mt-6 h-[calc(100vh-120px)] flex flex-col">
            {/* 聊天消息区域 */}
            <div className="flex-1 border rounded-md mb-4 bg-muted/30">
              <ScrollArea className="h-full p-4">
                <div className="space-y-4">
                  {chatMessages.map(message => <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                      <div className={`max-w-[85%] p-3 rounded-lg ${message.type === 'user' ? 'bg-primary text-primary-foreground' : 'bg-background border'}`}>
                        <div className="text-sm whitespace-pre-line leading-relaxed">
                          {message.content}
                        </div>
                        <div className={`text-xs mt-2 ${message.type === 'user' ? 'text-primary-foreground/70' : 'text-muted-foreground'}`}>
                          {message.timestamp}
                        </div>
                      </div>
                    </div>)}
                  {isAiTyping && <div className="flex justify-start">
                      <div className="bg-background border p-3 rounded-lg">
                        <div className="flex items-center gap-2">
                          <span className="text-sm">AI正在思考</span>
                          <div className="flex gap-1">
                            <div className="w-2 h-2 bg-muted-foreground rounded-full animate-pulse"></div>
                            <div className="w-2 h-2 bg-muted-foreground rounded-full animate-pulse" style={{
                          animationDelay: '0.2s'
                        }}></div>
                            <div className="w-2 h-2 bg-muted-foreground rounded-full animate-pulse" style={{
                          animationDelay: '0.4s'
                        }}></div>
                          </div>
                        </div>
                      </div>
                    </div>}
                </div>
              </ScrollArea>
            </div>
            
            {/* 消息输入区域 */}
            <div className="flex gap-2">
              <Input placeholder="询问关于报告的问题或说明如何修改..." value={messageInput} onChange={e => setMessageInput(e.target.value)} onKeyPress={e => e.key === 'Enter' && handleSendMessage()} className="flex-1" />
              <Button onClick={handleSendMessage} disabled={!messageInput.trim() || isAiTyping} className="px-4">
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </>;
}