import { Link } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Brain, Star, Clock } from "lucide-react";

interface SimilarIdeasProps {
  currentIdeaId: number;
}

export function SimilarIdeas({ currentIdeaId }: SimilarIdeasProps) {
  const similarIdeas = [
    {
      id: 2,
      title: "微软Copilot全面集成Office365",
      description: "企业AI办公时代到来，生产力革命正在发生",
      tags: ["企业AI", "SaaS"],
      lastUpdated: "5小时前",
      unreadEvents: 1,
      isRecommended: false
    },
    {
      id: 101,
      title: "Grok-4即将发布，AI基础模型能力进一步提升",
      description: "xAI即将发布Grok-4模型，预期在推理能力和多模态理解方面实现重大突破",
      tags: ["AI模型", "大语言模型"],
      lastUpdated: "30分钟前",
      unreadEvents: 0,
      isRecommended: true
    },
    {
      id: 4,
      title: "苹果M4 Ultra芯片发布，端侧AI计算能力超越云端",
      description: "M4 Ultra集成200核GPU和40核CPU，AI算力达到75 TOPS",
      tags: ["端侧AI", "芯片"],
      lastUpdated: "8小时前",
      unreadEvents: 0,
      isRecommended: false
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <Brain className="h-5 w-5" />
          相似Ideas推荐
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {similarIdeas.map((idea) => (
            <Link
              key={idea.id}
              to={`/idea/${idea.id}`}
              className="block p-3 border rounded-lg hover:bg-muted/50 transition-colors"
            >
              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  {idea.isRecommended ? (
                    <Star className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                  ) : (
                    <Brain className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                  )}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm leading-tight line-clamp-2">
                      {idea.title}
                    </h4>
                    <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                      {idea.description}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex gap-1">
                    {idea.tags.slice(0, 2).map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>{idea.lastUpdated}</span>
                    {idea.unreadEvents > 0 && (
                      <Badge variant="destructive" className="text-xs ml-1">
                        {idea.unreadEvents}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}