import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { Clock, TrendingUp, TrendingDown, Zap, AlertCircle, ExternalLink } from "lucide-react";

interface LatestChangesProps {
  ideaId: number;
  isReadOnly?: boolean;
}

export function LatestChanges({ ideaId, isReadOnly = false }: LatestChangesProps) {
  const [selectedNews, setSelectedNews] = useState<typeof changes[0] | null>(null);
  const factChecks = [
    { fact: "H200内存容量确实从80GB提升至141GB", status: "verified" as const },
    { fact: "性能提升90%的数据来源于NVIDIA官方基准测试", status: "verified" as const },
    { fact: "AGI发展时间表尚未明确", status: "caution" as const }
  ];

  const changes = [
    {
      time: "2小时前",
      datetime: "2024年3月15日 14:30",
      type: "market" as const,
      title: "微软Azure率先部署H200集群",
      impact: "positive" as const,
      description: "微软宣布在其Azure云服务中率先部署10万颗H200芯片，成为首个大规模采用的云服务商",
      source: "TechCrunch",
      sourceUrl: "https://techcrunch.com/microsoft-azure-h200",
      isNew: true
    },
    {
      time: "5小时前",
      datetime: "2024年3月15日 11:30",
      type: "news" as const,
      title: "OpenAI签署20亿美元采购协议",
      impact: "positive" as const,
      description: "OpenAI与英伟达签署价值20亿美元的H200芯片采购协议，用于下一代GPT模型训练",
      source: "Reuters",
      sourceUrl: "https://reuters.com/openai-nvidia-deal",
      isNew: true
    },
    {
      time: "1天前",
      datetime: "2024年3月14日 09:15",
      type: "analysis" as const,
      title: "分析师上调目标价",
      impact: "positive" as const,
      description: "摩根士丹利将英伟达目标价从$850上调至$950，看好H200芯片的市场前景",
      source: "Bloomberg",
      sourceUrl: "https://bloomberg.com/nvidia-price-target",
      isNew: false
    },
    {
      time: "2天前",
      datetime: "2024年3月13日 16:45",
      type: "market" as const,
      title: "竞争对手AMD发布新品",
      impact: "negative" as const,
      description: "AMD发布新一代MI300X芯片，在某些AI工作负载上接近H200性能",
      source: "AnandTech",
      sourceUrl: "https://anandtech.com/amd-mi300x",
      isNew: false
    },
    {
      time: "3天前",
      datetime: "2024年3月12日 13:20",
      type: "news" as const,
      title: "谷歌云宣布大规模采购计划",
      impact: "positive" as const,
      description: "谷歌云宣布将在2024年采购50万颗H200芯片，用于扩展其AI计算服务",
      source: "The Information",
      sourceUrl: "https://theinformation.com/google-h200-order",
      isNew: false
    },
    {
      time: "4天前",
      datetime: "2024年3月11日 10:30",
      type: "analysis" as const,
      title: "供应链分析报告",
      impact: "positive" as const,
      description: "台积电确认H200芯片产能将在Q2达到月产能20万颗，满足市场需求",
      source: "DigiTimes",
      sourceUrl: "https://digitimes.com/tsmc-h200-capacity",
      isNew: false
    }
  ];

  const getImpactIcon = (impact: string) => {
    return impact === "positive" ? 
      <TrendingUp className="h-4 w-4 text-success" /> : 
      <TrendingDown className="h-4 w-4 text-destructive" />;
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "market": return "default";
      case "news": return "secondary";
      case "analysis": return "outline";
      default: return "secondary";
    }
  };

  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg flex items-center gap-2">
          <Clock className="h-5 w-5" />
          思考核实与变化整理
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 左右两栏布局 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左栏：事件核实总结 */}
          <div className="lg:col-span-1">
            <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              事件核实总结
            </h4>
            <div className="space-y-3">
              {factChecks.map((check, index) => (
                <div key={index} className="p-3 border rounded-lg bg-muted/20">
                  <div className="flex items-start gap-2">
                    <Badge 
                      variant={check.status === "verified" ? "default" : "secondary"}
                      className="text-xs mt-0.5 flex-shrink-0"
                    >
                      {check.status === "verified" ? "已验证" : "需注意"}
                    </Badge>
                    <span className="text-xs text-muted-foreground leading-relaxed">
                      {check.fact}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* 右栏：最新变化整理（更宽） */}
          <div className="lg:col-span-2">
            <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
              <Zap className="h-4 w-4" />
              最新变化整理
            </h4>
            <div className="max-h-80 overflow-y-auto space-y-2">
              {changes.map((change, index) => (
                <div 
                  key={index} 
                  className="border-l-2 border-muted pl-3 py-2 bg-muted/10 rounded-r-lg cursor-pointer hover:bg-muted/20 transition-colors"
                  onClick={() => setSelectedNews(change)}
                >
                  <div className="flex items-start gap-2">
                    {getImpactIcon(change.impact)}
                    <div className="flex-1 min-w-0">
                      {/* 标题、时间和来源在一行 */}
                      <div className="flex items-center gap-2 mb-1">
                        <h5 className="font-medium text-sm flex-1 truncate">{change.title}</h5>
                        <div className="flex items-center gap-1 text-xs text-muted-foreground flex-shrink-0">
                          <span>{change.datetime}</span>
                          <span>•</span>
                          <a 
                            href={change.sourceUrl} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-primary hover:underline font-medium"
                          >
                            {change.source}
                          </a>
                        </div>
                      </div>
                      
                      {/* 标签行 */}
                      <div className="flex items-center gap-2 mb-1">
                        {change.isNew && (
                          <Badge variant="destructive" className="text-xs px-1.5 py-0.5">新事件</Badge>
                        )}
                        <Badge variant={getTypeColor(change.type)} className="text-xs">
                          {change.type === "market" ? "市场" : 
                           change.type === "news" ? "新闻" : "分析"}
                        </Badge>
                      </div>
                      
                      {/* 描述 */}
                      <p className="text-xs text-muted-foreground leading-relaxed">
                        {change.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
      
      {/* 新闻详情侧边栏 */}
      <Sheet open={!!selectedNews} onOpenChange={(open) => !open && setSelectedNews(null)}>
        <SheetContent side="right" className="w-[500px] sm:w-[600px]">
          <SheetHeader>
            <SheetTitle className="text-left">新闻详情</SheetTitle>
          </SheetHeader>
          
          {selectedNews && (
            <div className="mt-6 space-y-6">
              {/* 标题和基本信息 */}
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  {getImpactIcon(selectedNews.impact)}
                  <h2 className="text-lg font-semibold leading-tight flex-1">
                    {selectedNews.title}
                  </h2>
                </div>
                
                <div className="flex flex-wrap items-center gap-2">
                  {selectedNews.isNew && (
                    <Badge variant="destructive" className="text-xs">新事件</Badge>
                  )}
                  <Badge variant={getTypeColor(selectedNews.type)} className="text-xs">
                    {selectedNews.type === "market" ? "市场" : 
                     selectedNews.type === "news" ? "新闻" : "分析"}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {selectedNews.impact === "positive" ? "正面影响" : "负面影响"}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>{selectedNews.datetime}</span>
                  </div>
                  <a 
                    href={selectedNews.sourceUrl} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 text-primary hover:underline"
                  >
                    <ExternalLink className="h-4 w-4" />
                    <span>{selectedNews.source}</span>
                  </a>
                </div>
              </div>
              
              {/* 详细内容 */}
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium text-sm mb-2">事件描述</h3>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {selectedNews.description}
                  </p>
                </div>
                
                <div>
                  <h3 className="font-medium text-sm mb-2">详细分析</h3>
                  <div className="text-sm text-muted-foreground leading-relaxed space-y-3">
                    <p>
                      此事件对于H200芯片及英伟达的发展具有重要意义。从技术角度来看，这表明了市场对高性能AI芯片的强烈需求，同时也验证了H200在实际应用中的优异表现。
                    </p>
                    <p>
                      从市场角度分析，这一发展可能会进一步巩固英伟达在AI芯片领域的领导地位，并可能影响整个行业的竞争格局。投资者需要关注这一趋势对相关公司股价和市场份额的影响。
                    </p>
                    <p>
                      建议持续关注后续发展，特别是其他大型云服务商的响应策略以及竞争对手的产品发布情况。
                    </p>
                  </div>
                </div>
                
                <div>
                  <h3 className="font-medium text-sm mb-2">影响评估</h3>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="p-3 border rounded-lg bg-muted/20">
                      <div className="text-xs font-medium text-muted-foreground mb-1">短期影响</div>
                      <div className="text-sm">
                        {selectedNews.impact === "positive" ? "积极推动股价上涨" : "可能带来价格压力"}
                      </div>
                    </div>
                    <div className="p-3 border rounded-lg bg-muted/20">
                      <div className="text-xs font-medium text-muted-foreground mb-1">长期影响</div>
                      <div className="text-sm">
                        {selectedNews.impact === "positive" ? "巩固市场地位" : "增加竞争压力"}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </SheetContent>
      </Sheet>
    </Card>
  );
}