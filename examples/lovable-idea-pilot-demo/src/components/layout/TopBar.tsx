import { <PERSON>, Plus, Brain } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { useNavigate } from "react-router-dom";
import { PointsDisplay } from "@/components/points/PointsDisplay";

export function TopBar() {
  const navigate = useNavigate();
  return (
    <header className="h-16 border-b bg-card/50 backdrop-blur-sm flex items-center justify-between px-6 shadow-sm">
      <div className="flex items-center gap-4">
        <SidebarTrigger />
        <div className="flex items-center gap-2">
          <Brain className="h-8 w-8 text-primary" />
          <span className="text-xl font-bold bg-gradient-primary bg-clip-text text-transparent">
            AI Tracker
          </span>
        </div>
      </div>
      
      <div className="flex-1 max-w-md mx-8">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input 
            placeholder="搜索 Idea 或股票..." 
            className="pl-10 bg-background/50"
          />
        </div>
      </div>
      
      <div className="flex items-center gap-4">
        <PointsDisplay />
        <Button variant="premium" className="gap-2" onClick={() => navigate('/')}>
          <Plus className="h-4 w-4" />
          新 Idea
        </Button>
      </div>
    </header>
  );
}