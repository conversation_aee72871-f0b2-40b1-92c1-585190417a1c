import { useState } from "react";
import { MessageSquare, FileText, Bell, ChevronUp, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export function BottomDrawer() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState<'notes' | 'comments' | 'notifications'>('notes');

  const tabs = [
    { id: 'notes', label: '笔记', icon: FileText, count: 0 },
    { id: 'comments', label: '评论', icon: MessageSquare, count: 3 },
    { id: 'notifications', label: '通知', icon: Bell, count: 5 },
  ] as const;

  return (
    <div className={`fixed bottom-0 left-0 right-0 bg-card border-t transition-all duration-300 z-40 ${
      isExpanded ? 'h-80' : 'h-16'
    }`}>
      <div className="flex items-center justify-between px-6 h-16 border-b">
        <div className="flex items-center gap-4">
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? "default" : "ghost"}
              size="sm"
              className="gap-2"
              onClick={() => setActiveTab(tab.id)}
            >
              <tab.icon className="h-4 w-4" />
              {tab.label}
              {tab.count > 0 && (
                <Badge variant="secondary" className="ml-1 text-xs">
                  {tab.count}
                </Badge>
              )}
            </Button>
          ))}
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
        </Button>
      </div>
      
      {isExpanded && (
        <div className="p-6 h-64 overflow-y-auto">
          {activeTab === 'notes' && (
            <div className="text-muted-foreground">
              <p>暂无笔记</p>
            </div>
          )}
          {activeTab === 'comments' && (
            <div className="space-y-4">
              <div className="bg-muted/50 p-4 rounded-lg">
                <p className="text-sm">用户评论了你的想法 "AI 编码效率提升"</p>
                <p className="text-xs text-muted-foreground mt-2">5分钟前</p>
              </div>
            </div>
          )}
          {activeTab === 'notifications' && (
            <div className="space-y-4">
              <div className="bg-warning/10 p-4 rounded-lg border border-warning/20">
                <p className="text-sm">⚡ 新事件确认：NVDA Q4 业绩超预期</p>
                <p className="text-xs text-muted-foreground mt-2">10分钟前</p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}