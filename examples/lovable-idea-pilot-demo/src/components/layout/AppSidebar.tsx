import { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import { Brain, TrendingUp, Star, Archive, Share2, ChevronDown, ChevronRight } from "lucide-react";
import { Sidebar, SidebarContent, SidebarGroup, SidebarGroupContent, SidebarGroupLabel, SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from "@/components/ui/sidebar";
const myIdeasData = [{
  id: 1,
  title: 'NVIDIA H200超级芯片量产，AI训练效率革命性提升',
  stocks: ['NVDA', 'TSM', 'AMZN']
}, {
  id: 2,
  title: '微软Copilot全面集成Office365，企业AI办公时代到来',
  stocks: ['MSFT', 'CRM', 'ORCL']
}, {
  id: 3,
  title: '特斯拉FSD V13实现L4级自动驾驶，商业化应用在即',
  stocks: ['TSLA', 'NVDA', 'AMD']
}, {
  id: 4,
  title: '苹果M4 Ultra芯片发布，端侧AI计算能力超越云端',
  stocks: ['AAPL', 'TSM', 'QCOM']
}];

// 从所有ideas中提取股票并生成股票数据
const extractStocksFromIdeas = (ideas: typeof myIdeasData) => {
  const stockMap = new Map();
  ideas.forEach(idea => {
    idea.stocks.forEach(stock => {
      if (!stockMap.has(stock)) {
        // 模拟股票数据
        const mockPrices = {
          'NVDA': {
            name: '英伟达',
            change: '+2.3%'
          },
          'TSM': {
            name: '台积电',
            change: '+1.5%'
          },
          'AMZN': {
            name: '亚马逊',
            change: '+0.8%'
          },
          'MSFT': {
            name: '微软',
            change: '+1.2%'
          },
          'CRM': {
            name: 'Salesforce',
            change: '-0.5%'
          },
          'ORCL': {
            name: '甲骨文',
            change: '+0.3%'
          },
          'TSLA': {
            name: '特斯拉',
            change: '-1.1%'
          },
          'AMD': {
            name: 'AMD',
            change: '+1.8%'
          },
          'AAPL': {
            name: '苹果',
            change: '+0.6%'
          },
          'QCOM': {
            name: '高通',
            change: '+2.1%'
          }
        };
        stockMap.set(stock, {
          symbol: stock,
          name: mockPrices[stock]?.name || stock,
          change: mockPrices[stock]?.change || '+0.0%'
        });
      }
    });
  });
  return Array.from(stockMap.values()).sort((a, b) => a.symbol.localeCompare(b.symbol));
};
const stocksData = extractStocksFromIdeas(myIdeasData);
export function AppSidebar() {
  const {
    state
  } = useSidebar();
  const location = useLocation();
  const [expandedIdeas, setExpandedIdeas] = useState<Record<number, boolean>>({});
  const isCollapsed = state === "collapsed";
  const toggleIdea = (ideaId: number) => {
    setExpandedIdeas(prev => ({
      ...prev,
      [ideaId]: !prev[ideaId]
    }));
  };
  const getNavClasses = (isActive: boolean) => isActive ? "bg-sidebar-accent text-sidebar-accent-foreground font-medium" : "hover:bg-sidebar-accent/50 text-sidebar-foreground";
  return <Sidebar className={`${isCollapsed ? "w-16" : "w-56"} ml-4`}>
      <SidebarContent className="text-white bg-slate-700">
        
        {/* 我的 Ideas */}
        <SidebarGroup>
          <SidebarGroupLabel className="text-sidebar-foreground/70">
            我的 Ideas
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {myIdeasData.map(idea => <div key={idea.id}>
                  <SidebarMenuItem>
                    <SidebarMenuButton onClick={() => toggleIdea(idea.id)} className="justify-between">
                      <div className="flex items-center gap-2 min-w-0 flex-1">
                        <Brain className="h-4 w-4 flex-shrink-0" />
                        {!isCollapsed && <span className="truncate text-sm font-medium" title={idea.title}>
                            {idea.title}
                          </span>}
                      </div>
                      {!isCollapsed && (expandedIdeas[idea.id] ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />)}
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                  
                  {expandedIdeas[idea.id] && !isCollapsed && <div className="ml-6 space-y-1">
                      {idea.stocks.map(stock => <SidebarMenuItem key={stock}>
                          <SidebarMenuButton asChild size="sm">
                            <NavLink to={`/stock/${stock}`} className={({
                      isActive
                    }) => getNavClasses(isActive)}>
                              <TrendingUp className="h-3 w-3" />
                              <span className="text-xs">{stock}</span>
                            </NavLink>
                          </SidebarMenuButton>
                        </SidebarMenuItem>)}
                    </div>}
                </div>)}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* 我的股票 */}
        <SidebarGroup>
          <SidebarGroupLabel className="text-sidebar-foreground/70">
            我的股票
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {stocksData.map(stock => <SidebarMenuItem key={stock.symbol}>
                  <SidebarMenuButton asChild>
                    <NavLink to={`/stock/${stock.symbol}`} className={({
                  isActive
                }) => getNavClasses(isActive)}>
                      <TrendingUp className="h-4 w-4" />
                      {!isCollapsed && <div className="flex-1 flex justify-between items-center">
                          <div>
                            <div className="font-medium">{stock.symbol}</div>
                            <div className="text-xs text-sidebar-foreground/60">{stock.name}</div>
                          </div>
                          <span className={`text-xs ${stock.change.startsWith('+') ? 'text-success' : 'text-destructive'}`}>
                            {stock.change}
                          </span>
                        </div>}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>)}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* 标签 */}
        <SidebarGroup>
          <SidebarGroupLabel className="text-sidebar-foreground/70">
            标签
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <NavLink to="/favorites" className={({
                  isActive
                }) => getNavClasses(isActive)}>
                    <Star className="h-4 w-4" />
                    {!isCollapsed && <span>收藏</span>}
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <NavLink to="/archive" className={({
                  isActive
                }) => getNavClasses(isActive)}>
                    <Archive className="h-4 w-4" />
                    {!isCollapsed && <span>归档</span>}
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <NavLink to="/shared" className={({
                  isActive
                }) => getNavClasses(isActive)}>
                    <Share2 className="h-4 w-4" />
                    {!isCollapsed && <span>分享</span>}
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        
      </SidebarContent>
    </Sidebar>;
}