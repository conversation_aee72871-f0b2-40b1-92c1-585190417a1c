import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/layout/AppSidebar";
import { TopBar } from "@/components/layout/TopBar";
import { BottomDrawer } from "@/components/layout/BottomDrawer";

interface MainLayoutProps {
  children: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  return (
    <SidebarProvider>
      <div className="min-h-screen flex flex-col w-full bg-background">
        <TopBar />
        <div className="flex flex-1">
          <AppSidebar />
          <main className="flex-1 p-6">
            {children}
          </main>
        </div>
        <BottomDrawer />
      </div>
    </SidebarProvider>
  );
}