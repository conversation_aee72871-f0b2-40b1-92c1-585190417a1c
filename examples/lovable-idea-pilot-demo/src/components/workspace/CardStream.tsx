import { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { IdeaCard } from "./IdeaCard";
import { TagFilter } from "./TagFilter";

const myIdeasData = [
  {
    type: 'idea',
    id: 1,
    title: 'NVIDIA H200超级芯片量产，AI训练效率革命性提升',
    description: 'H200芯片相比H100性能提升90%，内存容量翻倍达到141GB，各大云服务商争相采购。HBM3e内存技术首次商用，带宽达到4.8TB/s，大模型训练成本降低30%。',
    stocks: ['NVDA', 'TSM', 'AMZN', 'GOOGL', 'MSFT'],
    allStocks: ['NVDA', 'TSM', 'AMZN', 'GOOGL', 'MSFT', 'META', 'CRM', 'ORCL'],
    unreadEvents: 2,
    lastUpdated: '2小时前',
    tags: ['AI芯片', '云计算'],
    category: 'AI芯片',
    aiInsight: '受益：AI芯片设计商(NVDA +15%)、代工厂(TSM +8%)、云计算厂商(AMZN/GOOGL/MSFT +5-7%)；受损：传统数据中心设备商(-3-5%)；最新订单数据显示Q1需求超预期40%，确认强劲投资逻辑。',
    newsUpdates: [
      { content: '微软Azure率先部署H200集群，与OpenAI深度合作训练GPT-5', timestamp: '1小时前' },
      { content: 'AWS宣布明年Q1开始提供H200实例服务，预订量已超80%', timestamp: '3小时前' },
      { content: '谷歌云预订500万美元H200算力，加速Gemini模型训练', timestamp: '5小时前' }
    ]
  },
  {
    type: 'idea',
    id: 2,
    title: '微软Copilot全面集成Office365，企业AI办公时代到来',
    description: 'Copilot在Word、Excel、PPT中实现深度集成，企业订阅用户数突破千万，生产力革命正在发生。智能文档生成、数据分析自动化、PPT设计AI化，办公效率提升平均达到45%。',
    stocks: ['MSFT', 'CRM', 'ORCL', 'SNOW', 'WDAY'],
    allStocks: ['MSFT', 'CRM', 'ORCL', 'SNOW', 'WDAY', 'ADBE', 'NOW', 'TEAM'],
    unreadEvents: 1,
    lastUpdated: '5小时前',
    tags: ['企业AI', 'SaaS'],
    category: '企业软件',
    aiInsight: '受益：领先SaaS厂商(MSFT +12%, CRM +8%)、AI工具开发商、企业服务平台；受损：传统办公软件公司(-5-8%)、人工文档服务商；用户增长率150%验证了市场需求爆发，续费率达到95%。',
    newsUpdates: [
      { content: 'Copilot Pro企业版用户增长率达到150%，月活跃用户突破1200万', timestamp: '2小时前' },
      { content: '财富500强中已有78%企业部署Copilot，生产力提升45%', timestamp: '4小时前' },
      { content: '微软宣布Copilot for Teams功能，智能会议纪要准确率99%', timestamp: '6小时前' }
    ]
  },
  {
    type: 'idea',
    id: 3,
    title: '特斯拉FSD V13实现L4级自动驾驶，商业化应用在即',
    description: 'FSD V13在城市复杂路况测试中表现完美，无人工接管里程突破50000英里，自动驾驶出租车服务启动。神经网络参数增至1200亿，端到端学习能力大幅提升，安全性超越人类驾驶员。',
    stocks: ['TSLA', 'NVDA', 'AMD', 'INTC', 'QCOM'],
    allStocks: ['TSLA', 'NVDA', 'AMD', 'INTC', 'QCOM', 'MOBILEYE', 'GM', 'F'],
    unreadEvents: 3,
    lastUpdated: '1天前',
    tags: ['自动驾驶', '智能汽车'],
    category: '智能汽车',
    aiInsight: '受益：自动驾驶技术公司(TSLA +18%)、AI芯片制造商(NVDA +10%)、激光雷达厂商；受损：传统出租车行业(-15%)、人工驾驶培训(-20%)、保险公司调整定价；奥斯汀试点成功，加速全球商业化进程。',
    newsUpdates: [
      { content: '马斯克宣布Robotaxi服务将在德州奥斯汀率先上线，投入1000辆车队', timestamp: '12小时前' },
      { content: 'FSD V13测试数据显示安全性提升500%，事故率降至人类驾驶员1/10', timestamp: '18小时前' },
      { content: '特斯拉与Uber达成合作，Robotaxi将接入Uber平台', timestamp: '1天前' }
    ]
  },
  {
    type: 'idea',
    id: 4,
    title: '苹果M4 Ultra芯片发布，端侧AI计算能力超越云端',
    description: 'M4 Ultra集成200核GPU和40核CPU，AI算力达到75 TOPS，本地运行大模型成为现实。3nm工艺节点、统一内存架构，功耗仅为120W，能效比超越NVIDIA H100。隐私计算和实时AI成为新标准。',
    stocks: ['AAPL', 'TSM', 'QCOM', 'AVGO', 'MU'],
    allStocks: ['AAPL', 'TSM', 'QCOM', 'AVGO', 'MU', 'ARM', 'NVDA', 'AMD'],
    unreadEvents: 0,
    lastUpdated: '8小时前',
    tags: ['端侧AI', '芯片'],
    category: '芯片技术',
    aiInsight: '受益：端侧芯片设计商(AAPL +10%, QCOM +8%)、代工厂(TSM +6%)、AI应用开发商；受损：云端AI服务商(-5%)、数据中心运营商营收增长放缓；75 TOPS算力超预期，重新定义移动AI计算范式。',
    newsUpdates: [
      { content: '苹果M4 Ultra性能测试：AI计算能效比超越NVIDIA H100达30%', timestamp: '3小时前' },
      { content: 'Adobe宣布Creative Suite全面优化M4 Ultra，渲染速度提升3倍', timestamp: '6小时前' },
      { content: 'M4 Ultra芯片首批产能已被预订完毕，供应链紧张推升股价', timestamp: '8小时前' }
    ]
  },
  {
    type: 'idea',
    id: 5,
    title: '亚马逊Bedrock平台推出企业级AI Agent，打造AI工作流生态',
    description: 'Bedrock AI Agent能够自主执行复杂业务流程，集成AWS全生态服务，企业数字化转型加速。支持多模态输入输出，代码生成、数据分析、客户服务全面智能化。',
    stocks: ['AMZN', 'MSFT', 'GOOGL', 'CRM', 'SNOW'],
    allStocks: ['AMZN', 'MSFT', 'GOOGL', 'CRM', 'SNOW', 'ORCL', 'IBM', 'PLTR'],
    unreadEvents: 2,
    lastUpdated: '3小时前',
    tags: ['云计算', 'AI Agent'],
    category: '云服务',
    aiInsight: '受益：云计算巨头(AMZN +9%)、企业软件厂商(CRM +7%)、AI基础设施供应商；受损：传统IT服务商(-4%)、人工客服外包；企业AI Agent市场规模预计2024年达到250亿美元。',
    newsUpdates: [
      { content: 'Bedrock AI Agent beta版本发布，已有500家企业申请试用', timestamp: '1小时前' },
      { content: 'Amazon与Salesforce深度合作，AI Agent集成CRM系统', timestamp: '4小时前' },
      { content: 'AWS re:Invent大会宣布AI Agent开发者激励计划', timestamp: '6小时前' }
    ]
  },
  {
    type: 'idea',
    id: 6,
    title: 'Anthropic Claude 3.5发布，AI安全性与能力并重突破',
    description: 'Claude 3.5在保持安全性的同时，推理能力媲美GPT-4，长文本处理能力达到200万tokens。Constitutional AI技术确保AI输出符合人类价值观，企业级安全部署成为新标准。',
    stocks: ['GOOGL', 'MSFT', 'NVDA', 'AMZN', 'META'],
    allStocks: ['GOOGL', 'MSFT', 'NVDA', 'AMZN', 'META', 'CRM', 'ORCL', 'IBM'],
    unreadEvents: 1,
    lastUpdated: '4小时前',
    tags: ['AI模型', 'AI安全'],
    category: 'AI安全',
    aiInsight: '受益：AI安全解决方案提供商、企业级AI服务商(GOOGL +6%)、安全芯片制造商；受损：缺乏安全保障的AI产品供应商；AI安全合规要求提升，推动行业标准化发展。',
    newsUpdates: [
      { content: 'Claude 3.5通过欧盟AI法案认证，成为首个合规的大模型', timestamp: '2小时前' },
      { content: '多家金融机构选择Claude 3.5处理敏感数据，安全性获认可', timestamp: '5小时前' },
      { content: 'Anthropic宣布Claude 3.5 API正式商用，定价较GPT-4低20%', timestamp: '7小时前' }
    ]
  }
];

const recommendedIdeasData = [
  {
    type: 'idea',
    id: 101,
    title: 'Grok-4即将发布，AI基础模型能力进一步提升',
    description: 'xAI即将发布Grok-4模型，预期在推理能力和多模态理解方面实现重大突破，将推动AI应用场景拓展。参数规模达到3万亿，多模态能力覆盖文本、图像、视频、音频，推理速度提升5倍。',
    stocks: ['NVDA', 'MSFT', 'GOOGL', 'META', 'TSLA'],
    allStocks: ['NVDA', 'MSFT', 'GOOGL', 'META', 'TSLA', 'AMZN', 'CRM', 'ORCL'],
    unreadEvents: 0,
    lastUpdated: '30分钟前',
    tags: ['AI模型', '大语言模型'],
    category: 'AI模型',
    aiInsight: '受益：高性能芯片供应商(NVDA +12%)、云计算平台(MSFT +8%)、模型训练服务商；受损：传统AI应用厂商(-5%)、低端AI服务提供商；Grok-4测试数据显示超越GPT-4o达25%，强化马斯克AI战略布局。',
    newsUpdates: [
      { content: 'Grok-4测试版本性能指标曝光，推理能力超越GPT-4o达25%', timestamp: '15分钟前' },
      { content: 'xAI获得60亿美元新一轮融资，估值突破500亿美元', timestamp: '1小时前' },
      { content: '特斯拉宣布Grok-4将集成到车载系统，提供智能助手服务', timestamp: '2小时前' }
    ],
    isRecommended: true
  },
  {
    type: 'idea',
    id: 102,
    title: 'OpenAI发布Sora视频生成模型商业版本',
    description: 'Sora商业版即将上线，视频生成质量达到专业级别，内容创作行业面临变革。支持4K分辨率、60帧率，单次可生成60秒高质量视频，创作成本降低90%。',
    stocks: ['MSFT', 'ADBE', 'NVDA', 'NFLX', 'DIS'],
    allStocks: ['MSFT', 'ADBE', 'NVDA', 'NFLX', 'DIS', 'META', 'GOOGL', 'CRM'],
    unreadEvents: 0,
    lastUpdated: '1小时前',
    tags: ['AI视频', '内容创作'],
    category: 'AI视频',
    aiInsight: '受益：AI视频技术公司、创意平台运营商(ADBE +10%)、视频内容平台(NFLX +6%)；受损：传统视频制作工作室(-15%)、后期制作公司(-12%)；月费$200定价偏高但企业接受度良好，预计年内降价推广。',
    newsUpdates: [
      { content: 'Sora Pro版本定价公布，月费$200瞄准专业市场', timestamp: '45分钟前' },
      { content: 'Adobe宣布与OpenAI合作，Sora将集成到Premiere Pro', timestamp: '3小时前' },
      { content: 'Netflix试用Sora生成预告片，效果获得业界认可', timestamp: '5小时前' }
    ],
    isRecommended: true
  },
  {
    type: 'idea',
    id: 103,
    title: '苹果M4芯片AI性能大幅提升，端侧计算爆发',
    description: 'M4芯片集成更强AI算力，推动iPhone和Mac设备AI功能升级，端侧AI计算需求激增。Neural Engine性能提升40%，支持实时语音翻译、图像生成、代码助手等功能。',
    stocks: ['AAPL', 'TSM', 'NVDA', 'ARM', 'QCOM'],
    allStocks: ['AAPL', 'TSM', 'NVDA', 'ARM', 'QCOM', 'AVGO', 'MU', 'AMD'],
    unreadEvents: 0,
    lastUpdated: '2小时前',
    tags: ['端侧AI', '芯片'],
    category: '端侧芯片',
    aiInsight: '受益：端侧芯片设计商(AAPL +9%, QCOM +7%)、芯片代工厂(TSM +5%)、AI应用开发商；受损：云端AI服务商营收增长放缓、数据中心运营商；M4芯片AI benchmarks提升300%超预期，重新定义移动计算能力边界。',
    newsUpdates: [
      { content: 'M4芯片AI benchmarks公布，Neural Engine性能提升300%', timestamp: '1小时前' },
      { content: 'iPhone 16 Pro系列M4芯片订单量超预期，供应链紧张', timestamp: '4小时前' },
      { content: '开发者大会展示M4芯片实时运行Stable Diffusion', timestamp: '6小时前' }
    ],
    isRecommended: true
  },
  {
    type: 'idea',
    id: 104,
    title: 'FDA批准首款AI辅助诊断设备大规模应用',
    description: 'AI医疗诊断技术获得重大突破，准确率超过95%，医疗AI商业化进程加速。涵盖肺癌早筛、心血管疾病诊断、糖尿病并发症预测等多个领域，诊断效率提升500%。',
    stocks: ['JNJ', 'PFE', 'NVDA', 'UNH', 'TMO'],
    allStocks: ['JNJ', 'PFE', 'NVDA', 'UNH', 'TMO', 'ABBV', 'MDT', 'DHR'],
    unreadEvents: 0,
    lastUpdated: '3小时前',
    tags: ['医疗AI', '诊断设备'],
    category: '医疗AI',
    aiInsight: '受益：医疗AI解决方案提供商、医疗设备制造商(JNJ +8%, TMO +6%)、AI芯片供应商(NVDA +5%)；受损：传统诊断设备厂商(-4%)、医学影像外包服务；FDA绿色通道审批加速商业化，预计2024年市场规模达150亿美元。',
    newsUpdates: [
      { content: 'FDA绿色通道批准首款AI肺癌筛查设备，预计年内上市', timestamp: '2小时前' },
      { content: '强生公司宣布AI诊断设备订单突破10万台', timestamp: '5小时前' },
      { content: '美国医院协会发布AI诊断设备应用指南', timestamp: '8小时前' }
    ],
    isRecommended: true
  },
  {
    type: 'idea',
    id: 105,
    title: '特斯拉FSD V13版本实现完全自动驾驶',
    description: 'FSD V13在城市道路测试中表现完美，无人工接管里程突破10000英里，自动驾驶商业化在即。神经网络架构全面升级，支持复杂交通场景，安全性超越人类驾驶员10倍。',
    stocks: ['TSLA', 'NVDA', 'AMD', 'MOBILEYE', 'GM'],
    allStocks: ['TSLA', 'NVDA', 'AMD', 'MOBILEYE', 'GM', 'F', 'QCOM', 'INTC'],
    unreadEvents: 0,
    lastUpdated: '4小时前',
    tags: ['自动驾驶', '智能汽车'],
    category: '自动驾驶',
    aiInsight: '受益：自动驾驶技术公司(TSLA +20%)、AI芯片制造商(NVDA +12%)、传感器供应商；受损：传统出租车行业(-20%)、驾驶培训机构(-25%)、汽车保险公司重新定价；FSD订阅用户突破100万验证商业模式成功。',
    newsUpdates: [
      { content: '马斯克宣布FSD订阅用户突破100万，月收入达30亿美元', timestamp: '3小时前' },
      { content: 'FSD V13获得加州DMV完全自动驾驶测试许可', timestamp: '6小时前' },
      { content: '特斯拉与Lyft合作，FSD车辆将加入网约车平台', timestamp: '8小时前' }
    ],
    isRecommended: true
  },
  {
    type: 'idea',
    id: 106,
    title: '量子计算IBM突破1000量子比特里程碑',
    description: 'IBM发布1121量子比特处理器，量子优势在特定算法中得到验证，商业应用前景明朗。Condor芯片采用新型量子纠错技术，计算精度提升1000倍，加密解码、药物发现、金融建模等领域应用加速。',
    stocks: ['IBM', 'GOOGL', 'MSFT', 'INTC', 'AMZN'],
    allStocks: ['IBM', 'GOOGL', 'MSFT', 'INTC', 'AMZN', 'NVDA', 'QCOM', 'CRM'],
    unreadEvents: 0,
    lastUpdated: '5小时前',
    tags: ['量子计算', '科技前沿'],
    category: '量子计算',
    aiInsight: '受益：量子计算技术公司(IBM +15%)、云计算平台(GOOGL +8%, MSFT +6%)、量子软件开发商；受损：传统超算厂商(-5%)、经典加密技术公司；IBM与金融机构合作协议验证商业化路径，预计3年内实现盈利。',
    newsUpdates: [
      { content: 'IBM Condor量子处理器正式发布，1121量子比特创纪录', timestamp: '4小时前' },
      { content: '摩根大通与IBM签署量子计算金融建模服务协议', timestamp: '7小时前' },
      { content: '欧洲核子研究中心采用IBM量子计算进行粒子物理研究', timestamp: '10小时前' }
    ],
    isRecommended: true
  },
  {
    type: 'idea',
    id: 107,
    title: '微软Copilot企业版用户数突破千万级别',
    description: 'Copilot在企业市场快速渗透，生产力提升效果明显，SaaS订阅模式获得验证。集成Teams、SharePoint、Power BI等全套办公工具，企业数字化转型加速，续费率高达98%。',
    stocks: ['MSFT', 'CRM', 'ORCL', 'ADBE', 'SNOW'],
    allStocks: ['MSFT', 'CRM', 'ORCL', 'ADBE', 'SNOW', 'NOW', 'WDAY', 'TEAM'],
    unreadEvents: 0,
    lastUpdated: '6小时前',
    tags: ['企业AI', 'SaaS'],
    category: '企业软件',
    aiInsight: '受益：企业软件巨头(MSFT +14%)、SaaS平台提供商(CRM +10%)、AI工具开发商；受损：传统企业软件公司(-8%)、办公自动化外包服务；Copilot企业版定价调整瞄准中小企业，TAM扩大至2000亿美元。',
    newsUpdates: [
      { content: 'Copilot企业版定价调整，中小企业版月费降至每用户$15', timestamp: '5小时前' },
      { content: '全球财富1000强企业中已有85%部署Copilot服务', timestamp: '8小时前' },
      { content: '微软发布Copilot Studio，让企业自定义AI助手', timestamp: '12小时前' }
    ],
    isRecommended: true
  },
  {
    type: 'idea',
    id: 108,
    title: '英伟达发布H200芯片，AI训练效率提升50%',
    description: 'H200芯片在AI训练和推理性能上实现重大突破，云服务商纷纷下单采购。HBM3e内存容量达到141GB，带宽4.8TB/s，功耗降低20%，大模型训练成本下降40%。',
    stocks: ['NVDA', 'TSM', 'AMZN', 'GOOGL', 'META'],
    allStocks: ['NVDA', 'TSM', 'AMZN', 'GOOGL', 'META', 'MSFT', 'CRM', 'ORCL'],
    unreadEvents: 0,
    lastUpdated: '7小时前',
    tags: ['AI芯片', '云计算'],
    category: 'AI芯片',
    aiInsight: '受益：AI芯片龙头(NVDA +18%)、代工厂(TSM +9%)、云计算厂商(AMZN/GOOGL +7%)；受损：传统GPU厂商(-6%)、老一代AI芯片持有者；OpenAI、谷歌等巨头预订H200产能超100万片，供需紧张推升毛利率。',
    newsUpdates: [
      { content: 'OpenAI、谷歌、Meta等公司已预订H200芯片产能超100万片', timestamp: '6小时前' },
      { content: 'H200芯片测试显示训练GPT-4级别模型成本降低40%', timestamp: '9小时前' },
      { content: '英伟达H200芯片供应紧张，交付周期延长至8个月', timestamp: '12小时前' }
    ],
    isRecommended: true
  },
  {
    type: 'idea',
    id: 109,
    title: 'Meta发布Reality Labs新一代VR头显，元宇宙体验革命',
    description: 'Quest 4头显采用全新光学系统，分辨率达到8K per eye，视场角扩大到220度，重量减轻40%。集成眼球追踪、手势识别、脑机接口，真正实现沉浸式元宇宙体验。',
    stocks: ['META', 'NVDA', 'QCOM', 'AAPL', 'MSFT'],
    allStocks: ['META', 'NVDA', 'QCOM', 'AAPL', 'MSFT', 'GOOGL', 'AMD', 'INTC'],
    unreadEvents: 2,
    lastUpdated: '1小时前',
    tags: ['元宇宙', 'VR/AR'],
    category: '元宇宙',
    aiInsight: '受益：VR硬件厂商(META +12%)、芯片供应商(QCOM +8%)、元宇宙内容开发商；受损：传统游戏主机厂商(-5%)、线下娱乐场所；Quest 4预订量突破200万台，验证消费者对高质量VR体验的强烈需求。',
    newsUpdates: [
      { content: 'Quest 4头显预订量突破200万台，创VR设备预售纪录', timestamp: '30分钟前' },
      { content: 'Meta与微软合作，Office应用将原生支持VR环境', timestamp: '3小时前' },
      { content: 'Reality Labs宣布VR社交平台Horizon Worlds用户突破1亿', timestamp: '5小时前' }
    ],
    isRecommended: true
  },
  {
    type: 'idea',
    id: 110,
    title: '亚马逊AWS推出云原生AI服务平台，企业AI部署门槛大降',
    description: 'AWS AI Services平台集成机器学习、自然语言处理、计算机视觉等AI能力，企业无需AI专家即可快速部署AI应用。一键部署、按需计费，AI应用开发周期从月缩短至天。',
    stocks: ['AMZN', 'MSFT', 'GOOGL', 'CRM', 'SNOW'],
    allStocks: ['AMZN', 'MSFT', 'GOOGL', 'CRM', 'SNOW', 'ORCL', 'IBM', 'NVDA'],
    unreadEvents: 1,
    lastUpdated: '2小时前',
    tags: ['云计算', 'AI平台'],
    category: '云服务',
    aiInsight: '受益：云计算平台(AMZN +11%)、AI基础设施供应商(NVDA +7%)、企业软件厂商；受损：传统IT集成商(-6%)、AI咨询服务公司；AWS AI Services降低企业AI部署门槛，TAM从500亿扩大至2000亿美元。',
    newsUpdates: [
      { content: 'AWS AI Services平台beta版发布，已有1000家企业申请试用', timestamp: '1小时前' },
      { content: '星巴克使用AWS AI优化门店运营，销售额提升15%', timestamp: '4小时前' },
      { content: 'AWS re:Invent大会宣布AI Services正式商用时间表', timestamp: '6小时前' }
    ],
    isRecommended: true
  }
];

export function CardStream() {
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const navigate = useNavigate();

  const handleCardClick = (item: any) => {
    if (item.type === 'idea') {
      navigate(`/idea/${item.id}`);
    }
  };

  // 提取所有推荐ideas的标签
  const allRecommendedTags = useMemo(() => {
    const tags = new Set<string>();
    recommendedIdeasData.forEach(idea => {
      idea.tags.forEach(tag => tags.add(tag));
    });
    return Array.from(tags).sort();
  }, []);

  // 根据选中的标签筛选推荐ideas
  const filteredRecommendedIdeas = useMemo(() => {
    if (selectedTags.length === 0) {
      return recommendedIdeasData;
    }
    // 由于改为单选模式，只取第一个选中的标签
    const selectedTag = selectedTags[0];
    return recommendedIdeasData.filter(idea => 
      idea.tags.includes(selectedTag)
    );
  }, [selectedTags]);

  return (
    <div className="space-y-8">
      {/* 我的 Ideas */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">我的 Ideas</h2>
          <div className="text-sm text-muted-foreground">
            共 {myIdeasData.length} 个想法追踪中
          </div>
        </div>
        
        <div className="grid gap-6 lg:grid-cols-2">
          {myIdeasData.map((item) => (
            <IdeaCard
              key={`my-idea-${item.id}`}
              idea={item as any}
              onClick={() => handleCardClick(item)}
            />
          ))}
        </div>
      </div>

      {/* 推荐 Ideas */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">市场推荐</h2>
          <div className="text-sm text-muted-foreground">
            发现更多投资机会 · 共 {filteredRecommendedIdeas.length} 个
          </div>
        </div>
        
        {/* 标签筛选 */}
        <div className="flex items-center gap-6 pb-4 border-b border-slate-200">
          <TagFilter
            allTags={allRecommendedTags}
            selectedTags={selectedTags}
            onTagChange={setSelectedTags}
          />
        </div>
        
        <div className="grid gap-6 lg:grid-cols-2">
          {filteredRecommendedIdeas.map((item) => (
            <IdeaCard
              key={`recommended-idea-${item.id}`}
              idea={item as any}
              onClick={() => handleCardClick(item)}
              isRecommended={true}
            />
          ))}
        </div>
        
        {filteredRecommendedIdeas.length === 0 && selectedTags.length > 0 && (
          <div className="text-center py-12 text-muted-foreground">
            <p>没有找到符合筛选条件的Ideas</p>
            <p className="text-sm mt-2">试试调整筛选标签或清除全部筛选</p>
          </div>
        )}
      </div>
    </div>
  );
}