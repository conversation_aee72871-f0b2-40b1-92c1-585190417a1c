import { Badge } from "@/components/ui/badge";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card";
import { TrendingUp, TrendingDown, DollarSign, MessageSquare, BarChart } from "lucide-react";

interface StockCardProps {
  stock: {
    id: string;
    symbol: string;
    name: string;
    price: number;
    change: number;
    changePercent: number;
    volume: string;
    marketCap: string;
    unreadComments: number;
  };
  onClick: () => void;
}

export function StockCard({ stock, onClick }: StockCardProps) {
  const isPositive = stock.change >= 0;
  
  return (
    <Card 
      className="cursor-pointer bg-gradient-card hover:shadow-elevated transition-all duration-300 hover:scale-[1.02] group"
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            {isPositive ? (
              <TrendingUp className="h-5 w-5 text-success" />
            ) : (
              <TrendingDown className="h-5 w-5 text-destructive" />
            )}
            <div>
              <h3 className="font-semibold group-hover:text-primary transition-colors">
                {stock.symbol}
              </h3>
              <p className="text-xs text-muted-foreground">{stock.name}</p>
            </div>
          </div>
          {stock.unreadComments > 0 && (
            <Badge variant="outline" className="text-xs px-2 py-1 gap-1">
              <MessageSquare className="h-3 w-3" />
              {stock.unreadComments}
            </Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-muted-foreground" />
            <span className="text-2xl font-bold">${stock.price.toFixed(2)}</span>
          </div>
          <div className={`text-right ${isPositive ? 'text-success' : 'text-destructive'}`}>
            <div className="font-semibold">
              {isPositive ? '+' : ''}{stock.change.toFixed(2)}
            </div>
            <div className="text-sm">
              ({isPositive ? '+' : ''}{stock.changePercent.toFixed(2)}%)
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <div className="text-muted-foreground flex items-center gap-1">
              <BarChart className="h-3 w-3" />
              成交量
            </div>
            <div className="font-medium">{stock.volume}</div>
          </div>
          <div>
            <div className="text-muted-foreground">市值</div>
            <div className="font-medium">{stock.marketCap}</div>
          </div>
        </div>
        
        {/* Mini chart placeholder */}
        <div className="h-16 bg-muted/30 rounded-md flex items-center justify-center">
          <span className="text-xs text-muted-foreground">7日走势图</span>
        </div>
      </CardContent>
    </Card>
  );
}