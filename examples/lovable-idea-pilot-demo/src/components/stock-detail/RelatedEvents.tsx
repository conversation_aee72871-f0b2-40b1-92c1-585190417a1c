import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Brain, TrendingUp, Clock, MessageSquare, Sparkles, Zap, BarChart3 } from "lucide-react";
interface RelatedEventsProps {
  symbol: string;
}
export function RelatedEvents({
  symbol
}: RelatedEventsProps) {
  // 模拟该股票历史直接相关事件
  const stockHistoricalEvents = symbol === 'NVDA' ? [{
    id: 1,
    title: "H200芯片正式发布",
    date: "2024年1月10日",
    description: "英伟达发布新一代H200 GPU，AI推理性能提升90%",
    impact: "股价上涨12%，数据中心订单激增",
    tags: ["产品发布", "技术突破"],
    change: "+12.3%"
  }, {
    id: 2,
    title: "Q1财报超预期",
    date: "2023年11月21日",
    description: "Q3财报营收180亿美元，同比增长206%，远超分析师预期",
    impact: "盘后股价暴涨8%，多家投行上调目标价",
    tags: ["财报", "业绩"],
    change: "+8.7%"
  }, {
    id: 3,
    title: "与微软签署长期合作协议",
    date: "2023年10月15日",
    description: "与微软Azure签署多年期GPU供应协议，总价值超过100亿美元",
    impact: "确保未来18个月订单可见性，投资者信心大增",
    tags: ["合作协议", "订单"],
    change: "+5.4%"
  }] : [{
    id: 1,
    title: "重大产品发布",
    date: "2024年1月10日",
    description: "公司发布重要新产品，市场反应积极",
    impact: "股价表现强劲，分析师普遍看好",
    tags: ["产品发布"],
    change: "+6.2%"
  }, {
    id: 2,
    title: "季度财报发布",
    date: "2023年11月21日",
    description: "季度业绩超出市场预期，各项指标表现优异",
    impact: "投资者信心提升，股价走强",
    tags: ["财报"],
    change: "+4.1%"
  }];

  // 模拟其他类似Ideas
  const similarIdeas = [{
    id: 1,
    title: "AMD AI芯片竞争格局分析",
    category: "竞争对手",
    description: "AMD在AI芯片领域的技术布局和市场策略",
    stocks: ["AMD", "INTC", "NVDA"],
    lastUpdated: "3小时前",
    tags: ["AI芯片", "竞争"],
    latestChange: "竞争分析更新",
    aiInsights: ["【中性】AMD MI300系列对H100构成一定竞争压力", "【观察】价格策略激进，但生态系统仍需完善"],
    newsUpdates: [{
      content: "AMD发布MI300X GPU，定价比H100低30%",
      timestamp: "2小时前"
    }]
  }, {
    id: 2,
    title: "云服务商GPU采购趋势",
    category: "行业趋势",
    description: "主要云服务商在AI基础设施方面的投资策略",
    stocks: ["MSFT", "AMZN", "GOOGL", "NVDA"],
    lastUpdated: "5小时前",
    tags: ["云计算", "GPU"],
    latestChange: "采购预测更新",
    aiInsights: ["【利好】云服务商GPU采购预算2024年增长80%", "【趋势】从训练转向推理，GPU需求结构性变化"],
    newsUpdates: [{
      content: "谷歌云宣布扩建AI数据中心，GPU需求大增",
      timestamp: "4小时前"
    }]
  }, {
    id: 3,
    title: "AI应用商业化进展",
    category: "应用场景",
    description: "企业级AI应用落地情况和商业化前景分析",
    stocks: ["NVDA", "MSFT", "CRM", "ORCL"],
    lastUpdated: "7小时前",
    tags: ["AI应用", "商业化"],
    latestChange: "市场调研更新",
    aiInsights: ["【利好】企业AI应用渗透率快速提升至25%", "【趋势】从POC转向规模化部署，算力需求激增"],
    newsUpdates: [{
      content: "ChatGPT企业版用户数突破100万",
      timestamp: "6小时前"
    }]
  }];
  return <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <BarChart3 className="h-4 w-4" />
          <span className="font-semibold text-base">其他相关事件</span>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 左栏：股票历史直接相关事件 */}
          <div className="space-y-4">
            <h3 className="font-medium text-sm text-primary border-b pb-2">
              {symbol} 历史重要事件
            </h3>
            <div className="space-y-3">
              {stockHistoricalEvents.map(event => <Card key={event.id} className="border-l-4 border-l-primary">
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <div className="flex items-start justify-between">
                        <h4 className="font-medium text-sm">{event.title}</h4>
                        <span className={`text-xs px-2 py-1 rounded ${event.change.startsWith('+') ? 'bg-success/20 text-success' : 'bg-destructive/20 text-destructive'}`}>
                          {event.change}
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground">{event.date}</div>
                      <p className="text-xs text-muted-foreground leading-relaxed">
                        {event.description}
                      </p>
                      
                      <div className="flex gap-1">
                        {event.tags.map((tag, index) => <Badge key={index} variant="outline" className="text-xs">
                            {tag}
                          </Badge>)}
                      </div>
                    </div>
                  </CardContent>
                </Card>)}
            </div>
          </div>

          {/* 右栏：其他类似Ideas */}
          <div className="space-y-4">
            <h3 className="font-medium text-sm text-foreground border-b pb-2">
              其他推荐
            </h3>
            <div className="space-y-3">
              {similarIdeas.map(idea => <Card key={idea.id} className="cursor-pointer bg-gradient-card hover:shadow-elevated transition-all duration-300 hover:scale-[1.01] group border">
                  <div className="flex h-full">
                    {/* Ideas信息 */}
                    <div className="flex-1 p-3 flex flex-col gap-2">
                      {/* 标题+分类 */}
                      <div className="flex items-start gap-2">
                        <Brain className="h-3 w-3 text-primary flex-shrink-0 mt-1" />
                        <div className="flex-1 min-w-0">
                          <h3 className="font-bold text-xs leading-tight line-clamp-2 group-hover:text-primary transition-colors mb-1">
                            {idea.title}
                          </h3>
                          <div className="flex items-center justify-between gap-2">
                            <Badge variant="outline" className="text-xs px-1 py-0">
                              {idea.category}
                            </Badge>
                            <div className="flex items-center gap-1">
                              <Clock className="h-2 w-2 text-muted-foreground" />
                              <span className="text-xs text-muted-foreground">{idea.lastUpdated}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* AI观点 */}
                      <div className="p-2 bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200/50 rounded">
                        <div className="flex items-start gap-1">
                          <Sparkles className="h-2 w-2 text-primary mt-0.5 flex-shrink-0" />
                          <div className="flex-1">
                            <ul className="space-y-1 text-xs text-foreground">
                              {idea.aiInsights.slice(0, 2).map((insight, index) => <li key={index} className="flex items-start gap-1">
                                  <span className="w-0.5 h-0.5 bg-primary rounded-full mt-1 flex-shrink-0"></span>
                                  <span className="line-clamp-1">{insight}</span>
                                </li>)}
                            </ul>
                          </div>
                        </div>
                      </div>

                      {/* 相关新闻 */}
                      {idea.newsUpdates && <div className="px-1">
                          <div className="flex items-start gap-1">
                            <MessageSquare className="h-2 w-2 text-muted-foreground mt-0.5 flex-shrink-0" />
                            <div className="flex-1">
                              <div className="text-xs text-muted-foreground flex items-start justify-between gap-1">
                                <span className="line-clamp-1 flex-1">{idea.newsUpdates[0]?.content}</span>
                                <span className="text-xs text-muted-foreground flex-shrink-0">
                                  {idea.newsUpdates[0]?.timestamp}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>}
                    </div>

                    {/* 右侧股票代码 */}
                    <div className="w-16 border-l bg-muted/20 p-2 flex flex-col">
                      <div className="text-xs text-muted-foreground mb-1 font-medium">Stocks</div>
                      <div className="space-y-1 flex-1">
                        {idea.stocks.slice(0, 2).map((stock, index) => {
                      const changes = ['+2.3%', '-1.2%'];
                      const change = changes[index % changes.length];
                      const isPositive = change.startsWith('+');
                      return <button key={stock} onClick={e => {
                        e.stopPropagation();
                        window.location.href = `/stock/${stock}`;
                      }} className="group relative w-full p-1 bg-card border border-border rounded text-xs hover:border-primary/30 hover:bg-accent/50 transition-all duration-200 text-left">
                              <div className="flex flex-col items-center">
                                <div className="font-sans text-xs font-medium text-foreground group-hover:text-primary transition-colors">
                                  {stock}
                                </div>
                                <div className={`text-xs font-medium ${isPositive ? 'text-success' : 'text-destructive'}`}>
                                  {change}
                                </div>
                              </div>
                            </button>;
                    })}
                      </div>
                    </div>
                  </div>
                </Card>)}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>;
}