import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { BarChart3 } from "lucide-react";
import { useState } from "react";
interface FinancialTableProps {
  symbol: string;
  currentData: {
    marketCap: string;
    revenue: string;
    profit: string;
    peRatio: string;
  };
}
export function FinancialTable({
  symbol,
  currentData
}: FinancialTableProps) {
  const [viewMode, setViewMode] = useState<'quarterly' | 'yearly'>('quarterly');
  const [chartMetric, setChartMetric] = useState<'revenue' | 'profit' | 'eps' | 'margin'>('revenue');
  // 季度数据（包含预期）
  const quarterlyData = [{
    period: 'Q1 2024',
    revenue: 60.9,
    profit: 14.9,
    eps: 5.98,
    margin: 72.7,
    type: 'actual'
  }, {
    period: 'Q4 2023',
    revenue: 22.1,
    profit: 12.3,
    eps: 4.93,
    margin: 73.0,
    type: 'actual'
  }, {
    period: 'Q3 2023',
    revenue: 18.1,
    profit: 9.2,
    eps: 3.71,
    margin: 75.1,
    type: 'actual'
  }, {
    period: 'Q2 2024E',
    revenue: 28.5,
    profit: 16.8,
    eps: 6.75,
    margin: 74.2,
    type: 'forecast'
  }, {
    period: 'Q3 2024E',
    revenue: 32.1,
    profit: 19.2,
    eps: 7.85,
    margin: 75.8,
    type: 'forecast'
  }];

  // 年度数据（包含预期）
  const yearlyData = [{
    period: '2023',
    revenue: 60.9,
    profit: 29.8,
    eps: 12.01,
    margin: 72.7,
    type: 'actual'
  }, {
    period: '2022',
    revenue: 27.0,
    profit: 4.4,
    eps: 1.76,
    margin: 26.0,
    type: 'actual'
  }, {
    period: '2021',
    revenue: 26.9,
    profit: 9.8,
    eps: 3.85,
    margin: 36.2,
    type: 'actual'
  }, {
    period: '2024E',
    revenue: 125.8,
    profit: 68.5,
    eps: 27.95,
    margin: 74.5,
    type: 'forecast'
  }, {
    period: '2025E',
    revenue: 156.2,
    profit: 89.1,
    eps: 36.12,
    margin: 76.1,
    type: 'forecast'
  }];
  const currentDataArray = viewMode === 'quarterly' ? quarterlyData : yearlyData;

  // 获取图表数据
  const getChartData = () => {
    return currentDataArray.map(item => ({
      period: item.period,
      value: item[chartMetric]
    }));
  };
  const chartData = getChartData();
  const maxValue = Math.max(...chartData.map(d => d.value));

  // 格式化显示值
  const formatValue = (value: number, metric: string) => {
    switch (metric) {
      case 'revenue':
      case 'profit':
        return `$${value}B`;
      case 'eps':
        return `$${value}`;
      case 'margin':
        return `${value}%`;
      default:
        return value.toString();
    }
  };
  const getMetricLabel = (metric: string) => {
    switch (metric) {
      case 'revenue':
        return '营收';
      case 'profit':
        return '净利润';
      case 'eps':
        return 'EPS';
      case 'margin':
        return '利润率';
      default:
        return metric;
    }
  };
  return <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-base">
            <BarChart3 className="h-4 w-4" />
            财务数据与表现
          </CardTitle>
          <div className="flex gap-1">
            <Button variant={viewMode === 'quarterly' ? 'default' : 'outline'} size="sm" onClick={() => setViewMode('quarterly')} className="text-xs h-7">
              按季度
            </Button>
            <Button variant={viewMode === 'yearly' ? 'default' : 'outline'} size="sm" onClick={() => setViewMode('yearly')} className="text-xs h-7">
              按年度
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 核心指标概览 */}
        <div className="grid grid-cols-4 gap-4 p-3 bg-muted/20 rounded">
          <div className="text-center">
            <div className="text-xs text-muted-foreground">市值</div>
            <div className="font-semibold text-sm">{currentData.marketCap}</div>
          </div>
          <div className="text-center">
            <div className="text-xs text-muted-foreground">年营收</div>
            <div className="font-semibold text-sm">{currentData.revenue}</div>
          </div>
          <div className="text-center">
            <div className="text-xs text-muted-foreground">净利润</div>
            <div className="font-semibold text-sm">{currentData.profit}</div>
          </div>
          <div className="text-center">
            <div className="text-xs text-muted-foreground">P/E比率</div>
            <div className="font-semibold text-sm">{currentData.peRatio}</div>
          </div>
        </div>

        {/* 趋势图表 - 横轴时间，纵轴业务指标 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">业务指标趋势</h4>
            <div className="flex gap-1">
              {['revenue', 'profit', 'eps', 'margin'].map(metric => <Button key={metric} variant={chartMetric === metric ? 'default' : 'outline'} size="sm" onClick={() => setChartMetric(metric as any)} className="text-xs h-6 px-2">
                  {getMetricLabel(metric)}
                </Button>)}
            </div>
          </div>
          
          {/* 简化的趋势图 */}
          <div className="relative h-32 bg-muted/10 rounded p-4">
            <div className="flex items-end justify-between h-full">
              {chartData.map((point, index) => {
              const height = point.value / maxValue * 100;
              return <div key={index} className="flex flex-col items-center gap-1">
                    <div className="w-6 bg-primary rounded-t transition-all duration-300" style={{
                  height: `${Math.max(height, 5)}%`
                }} />
                    <span className="text-xs text-muted-foreground">
                      {point.period}
                    </span>
                    <span className="text-xs font-medium">
                      {formatValue(point.value, chartMetric)}
                    </span>
                  </div>;
            })}
            </div>
          </div>
        </div>

        {/* 重新设计的财务表格 - 指标为行，时间为列 */}
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-xs font-semibold">财务指标</TableHead>
                {currentDataArray.map((row, index) => <TableHead key={index} className="text-xs text-center">
                    <div className="space-y-1">
                      <div className="font-medium">{row.period}</div>
                      
                    </div>
                  </TableHead>)}
              </TableRow>
            </TableHeader>
            <TableBody>
              {/* 营收行 */}
              <TableRow>
                <TableCell className="font-medium text-xs">营收</TableCell>
                {currentDataArray.map((row, index) => <TableCell key={index} className="text-xs text-center font-medium">
                    {formatValue(row.revenue, 'revenue')}
                  </TableCell>)}
              </TableRow>
              
              {/* 净利润行 */}
              <TableRow className="bg-muted/5">
                <TableCell className="font-medium text-xs">净利润</TableCell>
                {currentDataArray.map((row, index) => <TableCell key={index} className="text-xs text-center font-medium">
                    {formatValue(row.profit, 'profit')}
                  </TableCell>)}
              </TableRow>
              
              {/* EPS行 */}
              <TableRow>
                <TableCell className="font-medium text-xs">每股收益(EPS)</TableCell>
                {currentDataArray.map((row, index) => <TableCell key={index} className="text-xs text-center font-medium">
                    {formatValue(row.eps, 'eps')}
                  </TableCell>)}
              </TableRow>
              
              {/* 利润率行 */}
              <TableRow className="bg-muted/5">
                <TableCell className="font-medium text-xs">利润率</TableCell>
                {currentDataArray.map((row, index) => <TableCell key={index} className="text-xs text-center font-medium">
                    {formatValue(row.margin, 'margin')}
                  </TableCell>)}
              </TableRow>
            </TableBody>
          </Table>
        </div>

        {/* 分析师预期说明 */}
        <div className="text-xs text-muted-foreground bg-blue-50 p-2 rounded">
          <strong>分析师预期:</strong> 预期数据基于主流分析师报告综合，实际结果可能有所差异。
          数据包含未来{viewMode === 'quarterly' ? '2个季度' : '2年'}的业绩预测。
        </div>
      </CardContent>
    </Card>;
}