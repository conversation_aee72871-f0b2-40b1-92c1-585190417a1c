import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Brain, Activity, BarChart3, Calendar, AlertTriangle, Target } from "lucide-react";

interface EventAnalysisProps {
  ideaId?: string | null;
  symbol: string;
}

interface AnalysisData {
  correlation: string;
  impactLevel: string;
  timeframe: string;
  keyFactors: string[];
  riskFactors: string[];
  priceTargets: Array<{
    period: string;
    target: string;
    probability: string;
  }>;
}

export function EventAnalysis({ ideaId, symbol }: EventAnalysisProps) {
  // 根据是否有ideaId显示不同的分析内容
  const hasSpecificEvent = !!ideaId;

  // 模拟特定事件分析数据
  const specificEventAnalysis: AnalysisData = {
    correlation: "高度相关",
    impactLevel: "直接影响",
    timeframe: "立即生效",
    keyFactors: [
      "H200芯片技术突破直接提升公司核心竞争力",
      "云服务商采购需求激增推动营收增长",
      "AI应用普及加速带来长期增长动力",
      "技术护城河进一步巩固市场地位"
    ],
    riskFactors: [
      "市场竞争加剧可能影响毛利率",
      "技术迭代风险需要持续研发投入",
      "宏观经济变化影响客户采购决策"
    ],
    priceTargets: [
      { period: "1个月", target: "$780", probability: "85%" },
      { period: "3个月", target: "$850", probability: "75%" },
      { period: "6个月", target: "$920", probability: "65%" }
    ]
  };

  // 模拟其他相关事件
  const relatedEvents = [
    {
      title: "AMD推出新一代GPU芯片",
      impact: "竞争压力",
      timeAgo: "2天前",
      sentiment: "negative"
    },
    {
      title: "台积电3nm工艺产能扩张",
      impact: "供应链利好",
      timeAgo: "1周前",
      sentiment: "positive"
    },
    {
      title: "美国AI芯片出口政策调整",
      impact: "政策风险",
      timeAgo: "3天前",
      sentiment: "neutral"
    }
  ];

  if (!hasSpecificEvent) {
    return (
      <div className="space-y-6">
        {/* 一般市场分析 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              市场事件分析
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              以下是影响 {symbol} 股价的相关市场事件分析：
            </p>
            
            <div className="space-y-3">
              {relatedEvents.map((event, index) => (
                <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                  <div className={`w-3 h-3 rounded-full mt-2 flex-shrink-0 ${
                    event.sentiment === 'positive' ? 'bg-success' :
                    event.sentiment === 'negative' ? 'bg-destructive' : 'bg-muted-foreground'
                  }`} />
                  <div className="flex-1">
                    <h4 className="font-medium mb-1">{event.title}</h4>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <span>{event.impact}</span>
                      <span>•</span>
                      <span>{event.timeAgo}</span>
                    </div>
                  </div>
                  <Badge variant={
                    event.sentiment === 'positive' ? 'default' :
                    event.sentiment === 'negative' ? 'destructive' : 'secondary'
                  } className="text-xs">
                    {event.sentiment === 'positive' ? '利好' :
                     event.sentiment === 'negative' ? '利空' : '中性'}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 特定事件的联动分析 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            与Ideas的联动分析
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 关联度概览 */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center p-4 bg-muted/20 rounded-lg">
              <div className="text-sm text-muted-foreground">关联度</div>
              <div className="text-lg font-semibold text-primary">{specificEventAnalysis.correlation}</div>
            </div>
            <div className="text-center p-4 bg-muted/20 rounded-lg">
              <div className="text-sm text-muted-foreground">影响级别</div>
              <div className="text-lg font-semibold">{specificEventAnalysis.impactLevel}</div>
            </div>
            <div className="text-center p-4 bg-muted/20 rounded-lg">
              <div className="text-sm text-muted-foreground">生效时间</div>
              <div className="text-lg font-semibold">{specificEventAnalysis.timeframe}</div>
            </div>
          </div>

          {/* 核心驱动因素 */}
          <div>
            <h4 className="font-semibold mb-3 flex items-center gap-2">
              <Activity className="h-4 w-4" />
              核心驱动因素
            </h4>
            <div className="space-y-2">
              {specificEventAnalysis.keyFactors.map((factor, index) => (
                <div key={index} className="flex items-start gap-2 p-3 bg-success/5 rounded-lg border border-success/20">
                  <div className="w-2 h-2 rounded-full bg-success mt-2 flex-shrink-0" />
                  <p className="text-sm">{factor}</p>
                </div>
              ))}
            </div>
          </div>

          {/* 风险因素 */}
          <div>
            <h4 className="font-semibold mb-3 flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              风险因素
            </h4>
            <div className="space-y-2">
              {specificEventAnalysis.riskFactors.map((risk, index) => (
                <div key={index} className="flex items-start gap-2 p-3 bg-destructive/5 rounded-lg border border-destructive/20">
                  <div className="w-2 h-2 rounded-full bg-destructive mt-2 flex-shrink-0" />
                  <p className="text-sm">{risk}</p>
                </div>
              ))}
            </div>
          </div>

          {/* 价格目标 */}
          <div>
            <h4 className="font-semibold mb-3 flex items-center gap-2">
              <Target className="h-4 w-4" />
              价格目标预测
            </h4>
            <div className="grid grid-cols-3 gap-4">
              {specificEventAnalysis.priceTargets.map((target, index) => (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{target.period}</span>
                  </div>
                  <div className="text-xl font-bold text-primary">{target.target}</div>
                  <div className="text-sm text-muted-foreground">概率: {target.probability}</div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 其他相关事件 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            其他相关事件
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {relatedEvents.map((event, index) => (
              <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                <div className={`w-3 h-3 rounded-full mt-2 flex-shrink-0 ${
                  event.sentiment === 'positive' ? 'bg-success' :
                  event.sentiment === 'negative' ? 'bg-destructive' : 'bg-muted-foreground'
                }`} />
                <div className="flex-1">
                  <h4 className="font-medium mb-1">{event.title}</h4>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <span>{event.impact}</span>
                    <span>•</span>
                    <span>{event.timeAgo}</span>
                  </div>
                </div>
                <Badge variant={
                  event.sentiment === 'positive' ? 'default' :
                  event.sentiment === 'negative' ? 'destructive' : 'secondary'
                } className="text-xs">
                  {event.sentiment === 'positive' ? '利好' :
                   event.sentiment === 'negative' ? '利空' : '中性'}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}