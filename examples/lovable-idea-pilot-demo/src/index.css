@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* AI-inspired color palette */
    --background: 220 13% 98%;
    --foreground: 220 20% 15%;

    --card: 0 0% 100%;
    --card-foreground: 220 20% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 20% 15%;

    /* Deep blue-purple primary palette */
    --primary: 240 50% 20%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 240 100% 80%;

    /* Financial green for success states */
    --success: 142 71% 45%;
    --success-foreground: 0 0% 98%;

    /* Warning orange for alerts */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;

    --secondary: 220 14% 96%;
    --secondary-foreground: 220 20% 15%;

    --muted: 220 14% 96%;
    --muted-foreground: 220 13% 46%;

    --accent: 240 5% 96%;
    --accent-foreground: 240 50% 20%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 240 50% 20%;

    --radius: 0.75rem;

    /* Gradients for premium feel */
    --gradient-primary: linear-gradient(135deg, hsl(240 50% 20%), hsl(260 50% 25%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(220 14% 98%));
    --gradient-success: linear-gradient(135deg, hsl(142 71% 45%), hsl(152 81% 55%));

    /* Shadows for depth */
    --shadow-card: 0 4px 6px -1px hsl(220 13% 46% / 0.1), 0 2px 4px -1px hsl(220 13% 46% / 0.06);
    --shadow-elevated: 0 10px 15px -3px hsl(240 50% 20% / 0.1), 0 4px 6px -2px hsl(240 50% 20% / 0.05);
    --shadow-glow: 0 0 40px hsl(240 100% 80% / 0.3);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Sidebar */
    --sidebar-background: 240 10% 8%;
    --sidebar-foreground: 240 20% 80%;
    --sidebar-primary: 240 50% 20%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 15% 15%;
    --sidebar-accent-foreground: 240 20% 80%;
    --sidebar-border: 240 15% 15%;
    --sidebar-ring: 240 100% 80%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}