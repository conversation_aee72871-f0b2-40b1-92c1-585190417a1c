import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, BarChart3 } from "lucide-react";
import { useNavigate } from "react-router-dom";

export default function Pricing() {
  const navigate = useNavigate();

  const plans = [
    {
      name: "免费版",
      price: "¥0",
      period: "/月",
      description: "适合个人投资者入门使用",
      features: [
        "每月5次AI分析",
        "基础事件分析",
        "标准数据源",
        "社区支持",
        "基础报告导出"
      ],
      popular: false,
      buttonText: "开始使用",
      buttonVariant: "outline" as const
    },
    {
      name: "专业版",
      price: "¥299",
      period: "/月",
      description: "适合专业投资者和分析师",
      features: [
        "无限次AI分析",
        "深度事件分析",
        "多数据源整合",
        "实时市场监控",
        "高级报告定制",
        "API访问权限",
        "优先客服支持"
      ],
      popular: true,
      buttonText: "立即升级",
      buttonVariant: "default" as const
    },
    {
      name: "企业版",
      price: "¥999",
      period: "/月",
      description: "适合投资机构和大型企业",
      features: [
        "专业版全部功能",
        "团队协作工具",
        "自定义模型训练",
        "白标解决方案",
        "专属客户经理",
        "SLA保障",
        "私有部署选项"
      ],
      popular: false,
      buttonText: "联系销售",
      buttonVariant: "outline" as const
    }
  ];

  return (
    <div className="min-h-screen bg-slate-50">
      {/* 导航栏 */}
      <header className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-slate-800">
                <BarChart3 className="h-5 w-5 text-white" />
              </div>
              <Button 
                variant="ghost" 
                onClick={() => navigate('/')}
                className="font-semibold text-xl text-slate-800 hover:bg-transparent"
              >
                专业投资分析AI
              </Button>
            </div>
            
            <div className="flex items-center gap-8">
              <nav className="flex items-center gap-6">
                <a href="#" className="text-slate-600 hover:text-slate-800 transition-colors">
                  Community
                </a>
                <span className="text-slate-800 font-medium">Pricing</span>
                <Button 
                  variant="ghost" 
                  onClick={() => navigate('/blog')}
                  className="text-slate-600 hover:text-slate-800"
                >
                  Blog
                </Button>
              </nav>
              <Button 
                variant="outline" 
                onClick={() => navigate('/workspace')}
                className="text-slate-600 border-slate-300 hover:bg-slate-50"
              >
                工作台
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-6 py-16">
        {/* 头部 */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-slate-800 mb-4">
            选择适合您的方案
          </h1>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            从个人投资者到专业机构，我们为不同需求提供灵活的定价方案
          </p>
        </div>

        {/* 价格卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan, index) => (
            <Card 
              key={index} 
              className={`relative border-slate-200 ${
                plan.popular ? 'ring-2 ring-slate-800 shadow-lg' : ''
              }`}
            >
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-slate-800 text-white">
                  最受欢迎
                </Badge>
              )}
              
              <CardHeader className="text-center pb-4">
                <CardTitle className="text-xl font-semibold text-slate-800">
                  {plan.name}
                </CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-slate-800">{plan.price}</span>
                  <span className="text-slate-600">{plan.period}</span>
                </div>
                <p className="text-sm text-slate-600 mt-2">{plan.description}</p>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <ul className="space-y-3">
                  {plan.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center gap-3">
                      <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                      <span className="text-sm text-slate-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button 
                  variant={plan.buttonVariant}
                  className="w-full mt-6"
                  onClick={() => {
                    if (plan.name === "免费版") {
                      navigate('/workspace');
                    }
                  }}
                >
                  {plan.buttonText}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 常见问题 */}
        <div className="bg-white rounded-lg border border-slate-200 p-8">
          <h2 className="text-2xl font-semibold text-slate-800 mb-8 text-center">
            常见问题
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="font-medium text-slate-800 mb-2">如何升级我的计划？</h3>
              <p className="text-sm text-slate-600">
                您可以随时在账户设置中升级您的计划，升级后立即生效。
              </p>
            </div>
            
            <div>
              <h3 className="font-medium text-slate-800 mb-2">是否支持按年付费？</h3>
              <p className="text-sm text-slate-600">
                支持，按年付费可享受2个月免费优惠。
              </p>
            </div>
            
            <div>
              <h3 className="font-medium text-slate-800 mb-2">可以随时取消订阅吗？</h3>
              <p className="text-sm text-slate-600">
                是的，您可以随时取消订阅，取消后服务将持续到当前计费周期结束。
              </p>
            </div>
            
            <div>
              <h3 className="font-medium text-slate-800 mb-2">企业版包含哪些定制服务？</h3>
              <p className="text-sm text-slate-600">
                包含专属模型训练、私有部署、API定制等，具体需求请联系销售团队。
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}