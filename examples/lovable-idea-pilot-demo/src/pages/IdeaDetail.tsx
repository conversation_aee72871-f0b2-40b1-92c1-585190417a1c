import { useParams, useNavigate } from "react-router-dom";
import { MainLayout } from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AiThinkingFlow } from "@/components/idea-detail/AiThinkingFlow";

import { LatestChanges } from "@/components/idea-detail/LatestChanges";
import { FutureProjection } from "@/components/idea-detail/FutureProjection";
import { StockRecommendations } from "@/components/idea-detail/StockRecommendations";
import { 
  ArrowLeft, 
  Brain, 
  Star,
  Share2,
  BookmarkPlus,
  Zap,
  Clock
} from "lucide-react";

// 合并我的Ideas和推荐Ideas数据
const allIdeasData = [
  // 我的Ideas
  {
    id: 1,
    title: 'NVIDIA H200超级芯片量产，AI训练效率革命性提升',
    description: 'H200芯片相比H100性能提升90%，内存容量翻倍达到141GB，各大云服务商争相采购，AI训练成本有望大幅下降，推动AI应用普及加速。',
    stocks: ['NVDA', 'TSM', 'AMZN'],
    unreadEvents: 2,
    unreadThoughts: 3,
    unreadComments: 1,
    lastUpdated: '2小时前',
    tags: ['AI芯片', '云计算'],
    latestChange: '⚡ 微软Azure率先部署H200集群，OpenAI独家合作',
    isRecommended: false,
    fullContent: {
      keyPoints: [
        'H200芯片内存容量从H100的80GB提升至141GB，提升76%',
        '内存带宽达到4.8TB/s，相比H100提升2.4倍',
        '在大语言模型训练中性能提升高达90%',
        '功耗控制在700W，与H100保持一致'
      ],
      marketImpact: '预计H200芯片将在2024年Q2大规模出货，各大云服务商已开始预订。微软、亚马逊、谷歌等巨头纷纷计划升级数据中心，预计将推动英伟达2024年营收增长30%以上。',
      relatedNews: [
        {
          title: '微软Azure首批部署10万颗H200芯片',
          time: '1小时前',
          source: 'TechCrunch'
        },
        {
          title: 'OpenAI与英伟达签署20亿美元H200采购协议',
          time: '3小时前',
          source: 'Reuters'
        },
        {
          title: 'Meta计划在2024年采购35万颗H200芯片',
          time: '5小时前',
          source: 'Bloomberg'
        }
      ],
      priceTargets: [
        { analyst: '摩根士丹利', target: '$950', current: '$722' },
        { analyst: '高盛', target: '$880', current: '$722' },
        { analyst: '花旗', target: '$900', current: '$722' }
      ]
    }
  },
  {
    id: 2,
    title: '微软Copilot全面集成Office365，企业AI办公时代到来',
    description: 'Copilot在Word、Excel、PPT中实现深度集成，企业订阅用户数突破千万，生产力革命正在发生，SaaS市场格局重塑。',
    stocks: ['MSFT', 'CRM', 'ORCL'],
    unreadEvents: 1,
    lastUpdated: '5小时前',
    tags: ['企业AI', 'SaaS'],
    latestChange: '⚡ Copilot Pro企业版定价策略调整，加速中小企业渗透',
    isRecommended: false
  },
  {
    id: 3,
    title: '特斯拉FSD V13实现L4级自动驾驶，商业化应用在即',
    description: 'FSD V13在城市复杂路况测试中表现完美，无人工接管里程突破50000英里，自动驾驶出租车服务启动，行业变革拐点到来。',
    stocks: ['TSLA', 'NVDA', 'AMD'],
    unreadEvents: 3,
    lastUpdated: '1天前',
    tags: ['自动驾驶', '智能汽车'],
    latestChange: '马斯克宣布Robotaxi服务将在德州奥斯汀率先上线',
    isRecommended: false
  },
  {
    id: 4,
    title: '苹果M4 Ultra芯片发布，端侧AI计算能力超越云端',
    description: 'M4 Ultra集成200核GPU和40核CPU，AI算力达到75 TOPS，本地运行大模型成为现实，隐私保护与性能兼得。',
    stocks: ['AAPL', 'TSM', 'QCOM'],
    unreadEvents: 0,
    lastUpdated: '8小时前',
    tags: ['端侧AI', '芯片'],
    latestChange: '⚡ M4 Ultra benchmarks曝光，本地运行70B模型仅需8GB内存',
    isRecommended: false
  },
  // 推荐Ideas
  {
    id: 101,
    title: 'Grok-4即将发布，AI基础模型能力进一步提升',
    description: 'xAI即将发布Grok-4模型，预期在推理能力和多模态理解方面实现重大突破，将推动AI应用场景拓展...',
    stocks: ['NVDA', 'MSFT', 'GOOGL'],
    unreadEvents: 0,
    lastUpdated: '30分钟前',
    tags: ['AI模型', '大语言模型'],
    latestChange: '⚡ Grok-4测试版本性能指标曝光，超越GPT-4',
    isRecommended: true
  },
  {
    id: 102,
    title: 'OpenAI发布Sora视频生成模型商业版本',
    description: 'Sora商业版即将上线，视频生成质量达到专业级别，内容创作行业面临变革...',
    stocks: ['MSFT', 'ADBE', 'NVDA'],
    unreadEvents: 0,
    lastUpdated: '1小时前',
    tags: ['AI视频', '内容创作'],
    latestChange: 'Sora Pro版本定价公布，月费$200瞄准专业市场',
    isRecommended: true
  }
];

export default function IdeaDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  
  const idea = allIdeasData.find(item => item.id === parseInt(id || '0'));
  
  if (!idea) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-4">
          <h2 className="text-2xl font-bold text-muted-foreground">Ideas未找到</h2>
          <p className="text-muted-foreground">请检查链接是否正确</p>
          <Button onClick={() => navigate('/workspace')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回工作台
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* 顶部导航 */}
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            onClick={() => navigate('/workspace')}
            className="text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回工作台
          </Button>
          
          <div className="flex items-center gap-2">
            {!idea.isRecommended && (
              <>
                <Button variant="outline" size="sm">
                  <BookmarkPlus className="h-4 w-4 mr-2" />
                  收藏
                </Button>
                <Button variant="outline" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  分享
                </Button>
              </>
            )}
            {idea.isRecommended && (
              <div className="text-sm text-muted-foreground">
                这是推荐内容，仅可查看
              </div>
            )}
          </div>
        </div>

        {/* 主要内容 */}
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Ideas标题和基本信息 */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-start gap-3">
                {idea.isRecommended ? (
                  <div className="p-2 rounded-full bg-primary/10">
                    <Star className="h-6 w-6 text-primary" />
                  </div>
                ) : (
                  <div className="p-2 rounded-full bg-primary/10">
                    <Brain className="h-6 w-6 text-primary" />
                  </div>
                )}
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <CardTitle className="text-xl leading-relaxed">
                      {idea.title}
                    </CardTitle>
                    {idea.isRecommended && (
                      <Badge variant="outline" className="text-primary border-primary/30">
                        推荐
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center flex-wrap gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>更新于 {idea.lastUpdated}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {idea.unreadEvents > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          {idea.unreadEvents} 新事件
                        </Badge>
                      )}
                      {idea.unreadThoughts > 0 && (
                        <Badge variant="default" className="text-xs bg-blue-600 hover:bg-blue-700">
                          {idea.unreadThoughts} 新思考
                        </Badge>
                      )}
                      {idea.unreadComments > 0 && (
                        <Badge variant="secondary" className="text-xs">
                          {idea.unreadComments} 新评论
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-3 pt-0">
              <p className="text-muted-foreground leading-relaxed text-sm">
                {idea.description}
              </p>
              
              {/* 标签 */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">标签:</span>
                <div className="flex gap-1">
                  {idea.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 思考核实与变化整理 (左右两栏布局) */}
          <LatestChanges ideaId={idea.id} isReadOnly={idea.isRecommended} />

          {/* AI思考流程区域 */}
          <div className="w-full">
            <AiThinkingFlow ideaId={idea.id} isReadOnly={idea.isRecommended} />
          </div>

          {/* 其他分析区域 */}
          <div className="space-y-6">
            {/* 事件未来推演 */}
            <FutureProjection ideaId={idea.id} isReadOnly={idea.isRecommended} />
            
            {/* 股票推荐 */}
            <StockRecommendations ideaId={idea.id} isReadOnly={idea.isRecommended} />
          </div>
        </div>
      </div>
    </MainLayout>
  );
}