import React from "react";
import { useParams, useSearchParams, useNavigate } from "react-router-dom";
import { MainLayout } from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import { StockChart } from "@/components/stock-detail/StockChart";
import { EnhancedCompanyInfo } from "@/components/stock-detail/EnhancedCompanyInfo";
import { FinancialTable } from "@/components/stock-detail/FinancialTable";
import { IdeasAnalysis } from "@/components/stock-detail/IdeasAnalysis";
import { ComprehensiveThinking } from "@/components/stock-detail/ComprehensiveThinking";
import { RelatedEvents } from "@/components/stock-detail/RelatedEvents";
import { ArrowLeft } from "lucide-react";

export default function StockDetail() {
  const { symbol } = useParams();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const ideaId = searchParams.get('ideaId');

  // 模拟股票数据
  const stockData = {
    symbol: symbol?.toUpperCase() || '',
    name: getStockName(symbol?.toUpperCase() || ''),
    price: getStockPrice(symbol?.toUpperCase() || ''),
    change: getStockChange(symbol?.toUpperCase() || ''),
    type: getStockType(symbol?.toUpperCase() || ''),
    ...getFinancialData(symbol?.toUpperCase() || '')
  };

  function getStockName(symbol: string): string {
    const stockNames: Record<string, string> = {
      'NVDA': '英伟达公司',
      'TSM': '台湾积体电路制造公司',
      'MSFT': '微软公司',
      'AMZN': '亚马逊公司',
      'GOOGL': '谷歌公司',
      'AMD': 'AMD公司',
      'INTC': '英特尔公司',
      'CRM': 'Salesforce公司',
      'ORCL': '甲骨文公司',
      'IBM': 'IBM公司',
      'AAPL': '苹果公司',
      'TSLA': '特斯拉公司',
      'META': 'Meta平台公司',
      'NFLX': '奈飞公司',
      'DIS': '迪士尼公司',
      'BA': '波音公司',
      'GE': '通用电气公司',
      'JPM': '摩根大通银行',
      'V': 'Visa公司',
      'JNJ': '强生公司',
      'WMT': '沃尔玛公司',
      'PG': '宝洁公司',
      'KO': '可口可乐公司',
      'MCD': '麦当劳公司',
      'ADBE': '奥多比公司'
    };
    return stockNames[symbol] || symbol;
  }

  function getStockPrice(symbol: string): string {
    const stockPrices: Record<string, string> = {
      'NVDA': '$722.48',
      'TSM': '$97.85',
      'MSFT': '$378.24',
      'AMZN': '$151.94',
      'GOOGL': '$138.21',
      'AMD': '$136.78',
      'INTC': '$21.84',
      'CRM': '$249.63',
      'ORCL': '$134.27',
      'IBM': '$210.45',
      'AAPL': '$182.31',
      'TSLA': '$243.84',
      'META': '$485.12',
      'NFLX': '$491.05',
      'DIS': '$109.27',
      'BA': '$187.92',
      'GE': '$152.38',
      'JPM': '$214.67',
      'V': '$289.45',
      'JNJ': '$157.83',
      'WMT': '$158.92',
      'PG': '$162.40',
      'KO': '$62.85',
      'MCD': '$290.18',
      'ADBE': '$515.23'
    };
    return stockPrices[symbol] || '$0.00';
  }

  function getStockChange(symbol: string): string {
    const stockChanges: Record<string, string> = {
      'NVDA': '+2.3%',
      'TSM': '+1.8%',
      'MSFT': '+0.9%',
      'AMZN': '+1.2%',
      'GOOGL': '+0.7%',
      'AMD': '-1.5%',
      'INTC': '-2.1%',
      'CRM': '-0.8%',
      'ORCL': '-1.2%',
      'IBM': '-0.5%',
      'AAPL': '+1.4%',
      'TSLA': '+3.2%',
      'META': '+2.1%',
      'NFLX': '+1.8%',
      'DIS': '-0.9%',
      'BA': '-1.8%',
      'GE': '+2.5%',
      'JPM': '+1.1%',
      'V': '+0.8%',
      'JNJ': '+0.3%',
      'WMT': '+0.6%',
      'PG': '+0.4%',
      'KO': '+0.2%',
      'MCD': '+1.5%',
      'ADBE': '+2.8%'
    };
    return stockChanges[symbol] || '0.0%';
  }

  function getStockType(symbol: string): "bullish" | "bearish" {
    const bearishStocks = ['AMD', 'INTC', 'CRM', 'ORCL', 'IBM', 'DIS', 'BA'];
    return bearishStocks.includes(symbol) ? 'bearish' : 'bullish';
  }

  function getFinancialData(symbol: string) {
    const financialData: Record<string, any> = {
      'NVDA': {
        marketCap: '$1.78T',
        peRatio: '65.4',
        revenue: '$60.9B',
        profit: '$29.8B',
        employees: '26,196',
        founded: '1993',
        headquarters: '加利福尼亚州圣克拉拉',
        description: '英伟达是一家专注于图形处理器和AI芯片设计的科技公司，在数据中心、游戏和自动驾驶领域占据领导地位。H200芯片的发布进一步巩固了其在AI训练和推理领域的技术优势。',
        recentQuarters: [
          { quarter: 'Q4 2024', revenue: '$22.1B', growth: '+22%' },
          { quarter: 'Q3 2024', revenue: '$18.1B', growth: '+34%' },
          { quarter: 'Q2 2024', revenue: '$13.5B', growth: '+88%' },
          { quarter: 'Q1 2024', revenue: '$7.2B', growth: '+19%' }
        ]
      },
      'MSFT': {
        marketCap: '$2.89T',
        peRatio: '32.1',
        revenue: '$211.9B',
        profit: '$72.4B',
        employees: '221,000',
        founded: '1975',
        headquarters: '华盛顿州雷德蒙德',
        description: '微软是全球领先的软件和云服务提供商，在企业软件、云计算和AI领域具有强大竞争力。Copilot的推出标志着企业AI办公时代的到来。',
        recentQuarters: [
          { quarter: 'Q4 2024', revenue: '$56.2B', growth: '+15%' },
          { quarter: 'Q3 2024', revenue: '$52.9B', growth: '+17%' },
          { quarter: 'Q2 2024', revenue: '$50.4B', growth: '+18%' },
          { quarter: 'Q1 2024', revenue: '$48.1B', growth: '+13%' }
        ]
      },
      'AAPL': {
        marketCap: '$2.91T',
        peRatio: '28.9',
        revenue: '$383.3B',
        profit: '$97.0B',
        employees: '164,000',
        founded: '1976',
        headquarters: '加利福尼亚州库比蒂诺',
        description: '苹果公司是全球领先的消费电子产品设计和制造商，以iPhone、iPad、Mac等产品闻名。在智能手机、可穿戴设备和服务领域具有强大的生态系统。',
        recentQuarters: [
          { quarter: 'Q4 2024', revenue: '$89.5B', growth: '+6%' },
          { quarter: 'Q3 2024', revenue: '$81.8B', growth: '+5%' },
          { quarter: 'Q2 2024', revenue: '$90.8B', growth: '+2%' },
          { quarter: 'Q1 2024', revenue: '$119.6B', growth: '+2%' }
        ]
      },
      'TSLA': {
        marketCap: '$774.8B',
        peRatio: '63.2',
        revenue: '$96.8B',
        profit: '$15.0B',
        employees: '140,473',
        founded: '2003',
        headquarters: '得克萨斯州奥斯汀',
        description: '特斯拉是全球电动汽车和清洁能源公司的领导者，致力于推动世界向可持续能源转型。在自动驾驶、储能和太阳能领域也有重要布局。',
        recentQuarters: [
          { quarter: 'Q4 2024', revenue: '$25.2B', growth: '+3%' },
          { quarter: 'Q3 2024', revenue: '$23.4B', growth: '+8%' },
          { quarter: 'Q2 2024', revenue: '$24.9B', growth: '+2%' },
          { quarter: 'Q1 2024', revenue: '$21.3B', growth: '-9%' }
        ]
      },
      'META': {
        marketCap: '$1.26T',
        peRatio: '25.1',
        revenue: '$134.9B',
        profit: '$39.1B',
        employees: '70,799',
        founded: '2004',
        headquarters: '加利福尼亚州门洛帕克',
        description: 'Meta平台公司是全球领先的社交媒体和虚拟现实技术公司，旗下拥有Facebook、Instagram、WhatsApp等平台。正在积极布局元宇宙和AI领域。',
        recentQuarters: [
          { quarter: 'Q4 2024', revenue: '$40.1B', growth: '+25%' },
          { quarter: 'Q3 2024', revenue: '$34.1B', growth: '+23%' },
          { quarter: 'Q2 2024', revenue: '$39.1B', growth: '+22%' },
          { quarter: 'Q1 2024', revenue: '$36.5B', growth: '+27%' }
        ]
      },
      'AMZN': {
        marketCap: '$1.54T',
        peRatio: '43.8',
        revenue: '$574.8B',
        profit: '$30.4B',
        employees: '1,541,000',
        founded: '1994',
        headquarters: '华盛顿州西雅图',
        description: '亚马逊是全球最大的电子商务和云计算公司，AWS云服务是其主要利润来源。在物流、人工智能和数字广告领域也有重要地位。',
        recentQuarters: [
          { quarter: 'Q4 2024', revenue: '$170.0B', growth: '+14%' },
          { quarter: 'Q3 2024', revenue: '$158.9B', growth: '+11%' },
          { quarter: 'Q2 2024', revenue: '$147.98B', growth: '+10%' },
          { quarter: 'Q1 2024', revenue: '$143.3B', growth: '+13%' }
        ]
      },
      'GOOGL': {
        marketCap: '$1.73T',
        peRatio: '23.4',
        revenue: '$307.4B',
        profit: '$73.8B',
        employees: '182,502',
        founded: '1998',
        headquarters: '加利福尼亚州山景城',
        description: '谷歌是全球最大的搜索引擎和在线广告公司，在云计算、人工智能、自动驾驶等前沿技术领域投资巨大。YouTube和Android也是其重要业务。',
        recentQuarters: [
          { quarter: 'Q4 2024', revenue: '$86.2B', growth: '+13%' },
          { quarter: 'Q3 2024', revenue: '$88.3B', growth: '+15%' },
          { quarter: 'Q2 2024', revenue: '$84.7B', growth: '+14%' },
          { quarter: 'Q1 2024', revenue: '$80.5B', growth: '+15%' }
        ]
      }
    };
    
    return financialData[symbol] || {
      marketCap: 'N/A',
      peRatio: 'N/A',
      revenue: 'N/A',
      profit: 'N/A',
      employees: 'N/A',
      founded: 'N/A',
      headquarters: 'N/A',
      description: '暂无详细信息',
      recentQuarters: []
    };
  }

  if (!symbol) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-4">
          <h2 className="text-2xl font-bold text-muted-foreground">股票代码未找到</h2>
          <p className="text-muted-foreground">请检查链接是否正确</p>
          <Button onClick={() => navigate('/workspace')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            返回工作台
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* 顶部返回按钮 */}
        <div className="flex items-center justify-between">
          <Button 
            variant="ghost" 
            onClick={() => navigate(ideaId ? `/idea/${ideaId}` : '/workspace')}
            className="text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {ideaId ? '返回Ideas详情' : '返回工作台'}
          </Button>
        </div>

        <div className="space-y-4">
          {/* 股价图表 */}
          <StockChart
            symbol={stockData.symbol}
            name={stockData.name}
            price={stockData.price}
            change={stockData.change}
            type={stockData.type}
          />

          {/* 两栏布局：公司信息 + 财务数据 */}
          <div className="grid lg:grid-cols-2 gap-4">
            <EnhancedCompanyInfo
              symbol={stockData.symbol}
              name={stockData.name}
              employees={stockData.employees}
              founded={stockData.founded}
              headquarters={stockData.headquarters}
              description={stockData.description}
            />
            <FinancialTable
              symbol={stockData.symbol}
              currentData={{
                marketCap: stockData.marketCap,
                revenue: stockData.revenue,
                profit: stockData.profit,
                peRatio: stockData.peRatio
              }}
            />
          </div>

          {/* Ideas联动分析 */}
          <IdeasAnalysis
            symbol={stockData.symbol}
            ideaId={ideaId}
          />

          {/* 综合思考 */}
          <ComprehensiveThinking
            symbol={stockData.symbol}
          />

          {/* 其他相关事件 */}
          <RelatedEvents
            symbol={stockData.symbol}
          />
        </div>
      </div>
    </MainLayout>
  );
}