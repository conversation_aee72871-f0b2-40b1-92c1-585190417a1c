# LangGraph异步任务处理开源案例参考

## 1. Lang<PERSON><PERSON><PERSON>官方示例

### FastAPI + LangGraph + SSE示例
基于搜索结果中的社区分享案例：

```python
# FastAPI + LangGraph异步流处理示例
from fastapi import FastAPI, Request
from fastapi.responses import StreamingResponse
import asyncio
import json

app = FastAPI()

async def generate_chat_events(message: str, config: RunnableConfig):
    """事件流生成器"""
    async for event in graph.astream_events(
        input=format_user_input(message),
        config=config,
        version="v2",
    ):
        kind = event["event"]
        tags = event.get("tags", [])
        
        # 处理不同类型的事件
        if kind == "on_chat_model_stream" and "chat_node" in tags:
            chunk = event["data"]["chunk"]
            if chunk.content:
                chunk_content = serialize_ai_message_chunk(chunk)
                yield build_response_message(
                    chunk_content=chunk_content,
                    config=config
                )
        
        # 工具调用状态更新
        elif kind == "on_tool_start":
            if event["name"] == "search_tool":
                yield build_response_message(
                    chunk_content="\n🔍 Searching... ",
                    config=config
                )
        
        elif kind == "on_tool_end":
            if event["name"] == "search_tool":
                yield build_response_message(
                    chunk_content="✅\n\n",
                    config=config
                )

def build_response_message(config: RunnableConfig, chunk_content: str = None):
    """构建SSE响应消息"""
    graph_state = graph.get_state(config).values
    messages = graph_state.get("messages", [])
    
    # 转换为可序列化格式
    messages = [
        {
            "role": message.type,
            "content": message.content
        }
        for message in messages
    ]
    
    data = json.dumps({
        "content": chunk_content,
        "messages": messages,
    })
    
    return f"event: message\ndata: {data}\n\n"

@app.post("/v1/chat")
async def chat_stream(request: Request, body: GraphRequest):
    """带断连检测的聊天流式端点"""
    try:
        server_config = await per_req_config_modifier({}, request)
        
        async def event_stream_with_disconnect_check():
            try:
                async for event in generate_chat_events(
                    message=body.message,
                    config=server_config,
                ):
                    # 检查客户端是否断开
                    if await request.is_disconnected():
                        break
                    yield event
            except asyncio.CancelledError:
                yield f"event: cancelled\ndata: {json.dumps({'message': 'Stream cancelled'})}\n\n"
        
        return StreamingResponse(
            event_stream_with_disconnect_check(),
            media_type="text/event-stream"
        )
    except Exception as e:
        return StreamingResponse(
            content=f"event: error\ndata: {str(e)}\n\n",
            status_code=500,
            media_type="text/event-stream"
        )
```

## 2. Streamlit + LangGraph集成案例

基于搜索结果中的Medium文章案例：

```python
# Streamlit + LangGraph异步集成
import streamlit as st
import asyncio
from langgraph import StateGraph
from langgraph.prebuilt import ToolNode

class StreamlitCallbackHandler:
    """Streamlit专用回调处理器"""
    
    def __init__(self):
        if 'messages' not in st.session_state:
            st.session_state.messages = []
        
        # 使用st.empty()创建占位符
        self.status_placeholder = st.empty()
        self.content_placeholder = st.empty()
    
    def on_llm_new_token(self, token: str, **kwargs):
        """处理新token"""
        if 'current_response' not in st.session_state:
            st.session_state.current_response = ""
        
        st.session_state.current_response += token
        self.content_placeholder.markdown(st.session_state.current_response)
    
    def on_tool_start(self, tool_name: str, **kwargs):
        """工具开始执行"""
        self.status_placeholder.info(f"🔧 Using {tool_name}...")
    
    def on_tool_end(self, tool_name: str, output: str, **kwargs):
        """工具执行完成"""
        self.status_placeholder.success(f"✅ {tool_name} completed")

# 异步执行LangGraph的包装器
async def run_langgraph_async(query: str, callback_handler):
    """异步运行LangGraph"""
    try:
        # 配置回调
        config = RunnableConfig(callbacks=[callback_handler])
        
        # 流式执行
        async for chunk in graph.astream(
            {"messages": [HumanMessage(content=query)]},
            config=config
        ):
            # 检查Streamlit是否仍在运行
            if st.session_state.get('stop_generation', False):
                break
                
            # 更新UI
            await asyncio.sleep(0.01)  # 让出控制权
            
    except Exception as e:
        st.error(f"Error: {e}")

def main():
    st.title("LangGraph + Streamlit Demo")
    
    # 初始化停止标志
    if 'stop_generation' not in st.session_state:
        st.session_state.stop_generation = False
    
    # 用户输入
    user_input = st.chat_input("Ask something...")
    
    # 停止按钮
    if st.button("Stop Generation"):
        st.session_state.stop_generation = True
    
    if user_input and not st.session_state.stop_generation:
        # 重置状态
        st.session_state.stop_generation = False
        st.session_state.current_response = ""
        
        # 创建回调处理器
        callback_handler = StreamlitCallbackHandler()
        
        # 运行异步任务
        try:
            # 在Streamlit中运行异步代码
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(
                run_langgraph_async(user_input, callback_handler)
            )
        except Exception as e:
            st.error(f"Execution error: {e}")

if __name__ == "__main__":
    main()
```

## 3. 使用astream_events的改进版本

```python
# 基于astream_events的异步流处理
async def advanced_stream_with_cancellation(
    graph, 
    input_data: dict, 
    config: RunnableConfig,
    cancellation_token: asyncio.Event = None
):
    """高级流处理，支持取消"""
    
    try:
        async for event in graph.astream_events(
            input_data, 
            config=config, 
            version="v2"
        ):
            # 检查取消信号
            if cancellation_token and cancellation_token.is_set():
                logger.info("Stream cancelled by user")
                break
            
            event_type = event.get("event")
            
            # 处理不同事件类型
            if event_type == "on_chat_model_start":
                yield {
                    "type": "model_start",
                    "data": {"message": "AI is thinking..."}
                }
            
            elif event_type == "on_chat_model_stream":
                chunk = event.get("data", {}).get("chunk", {})
                if hasattr(chunk, 'content') and chunk.content:
                    yield {
                        "type": "content_delta",
                        "data": {"content": chunk.content}
                    }
            
            elif event_type == "on_tool_start":
                tool_name = event.get("name", "Unknown")
                yield {
                    "type": "tool_start", 
                    "data": {"tool_name": tool_name}
                }
            
            elif event_type == "on_tool_end":
                tool_name = event.get("name", "Unknown")
                tool_output = event.get("data", {}).get("output", "")
                yield {
                    "type": "tool_end",
                    "data": {
                        "tool_name": tool_name,
                        "output": str(tool_output)
                    }
                }
            
            # 让出控制权，允许其他协程运行
            await asyncio.sleep(0)
            
    except asyncio.CancelledError:
        logger.info("Stream was cancelled")
        yield {
            "type": "cancelled",
            "data": {"message": "Generation was cancelled"}
        }
    except Exception as e:
        logger.error(f"Stream error: {e}")
        yield {
            "type": "error",
            "data": {"error": str(e)}
        }

# 使用示例
async def example_usage():
    # 创建取消令牌
    cancellation_token = asyncio.Event()
    
    # 启动流处理
    stream_task = asyncio.create_task(
        advanced_stream_with_cancellation(
            graph=your_graph,
            input_data={"query": "What's the weather?"},
            config=RunnableConfig(),
            cancellation_token=cancellation_token
        )
    )
    
    try:
        async for event in stream_task:
            print(f"Received: {event}")
            
            # 某些条件下取消
            if some_cancel_condition:
                cancellation_token.set()
                break
                
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # 确保任务被清理
        if not stream_task.done():
            stream_task.cancel()
            try:
                await stream_task
            except asyncio.CancelledError:
                pass
```

## 4. WebSocket替代方案

```python
# WebSocket实现，提供双向通信和更好的取消控制
from fastapi import WebSocket, WebSocketDisconnect
import asyncio
import json

@app.websocket("/ws/chat")
async def websocket_chat(websocket: WebSocket):
    await websocket.accept()
    
    active_tasks = {}
    
    try:
        while True:
            # 接收消息
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            if message_data["type"] == "start_chat":
                # 启动新的聊天任务
                task_id = str(uuid.uuid4())
                
                async def chat_task():
                    try:
                        async for event in graph.astream_events(
                            {"messages": [HumanMessage(content=message_data["query"])]},
                            config=RunnableConfig(),
                            version="v2"
                        ):
                            # 发送事件到WebSocket
                            await websocket.send_text(json.dumps({
                                "task_id": task_id,
                                "event": event
                            }))
                            
                    except asyncio.CancelledError:
                        await websocket.send_text(json.dumps({
                            "task_id": task_id,
                            "type": "cancelled"
                        }))
                    except Exception as e:
                        await websocket.send_text(json.dumps({
                            "task_id": task_id,
                            "type": "error",
                            "error": str(e)
                        }))
                    finally:
                        # 清理任务
                        active_tasks.pop(task_id, None)
                
                # 启动任务
                task = asyncio.create_task(chat_task())
                active_tasks[task_id] = task
                
                # 发送任务开始确认
                await websocket.send_text(json.dumps({
                    "type": "task_started",
                    "task_id": task_id
                }))
            
            elif message_data["type"] == "cancel_task":
                # 取消指定任务
                task_id = message_data.get("task_id")
                task = active_tasks.get(task_id)
                if task and not task.done():
                    task.cancel()
                    await websocket.send_text(json.dumps({
                        "type": "task_cancelled",
                        "task_id": task_id
                    }))
            
    except WebSocketDisconnect:
        # 客户端断开连接，取消所有活动任务
        for task in active_tasks.values():
            if not task.done():
                task.cancel()
        
        # 等待所有任务完成清理
        if active_tasks:
            await asyncio.gather(*active_tasks.values(), return_exceptions=True)
```

## 5. 生产环境最佳实践

```python
# 生产环境任务管理最佳实践
class ProductionTaskManager:
    def __init__(self):
        self.tasks = {}
        self.cleanup_interval = 300  # 5分钟清理一次
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """启动定期清理任务"""
        async def cleanup_loop():
            while True:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_stale_tasks()
        
        asyncio.create_task(cleanup_loop())
    
    async def _cleanup_stale_tasks(self):
        """清理过期任务"""
        current_time = time.time()
        stale_tasks = []
        
        for task_id, task_info in self.tasks.items():
            if (current_time - task_info.created_at) > 3600:  # 1小时超时
                stale_tasks.append(task_id)
        
        for task_id in stale_tasks:
            await self.cancel_task(task_id)
    
    async def create_managed_stream(
        self, 
        graph, 
        input_data: dict, 
        config: RunnableConfig,
        task_id: str = None,
        timeout: float = 3600
    ):
        """创建托管的流任务"""
        if task_id is None:
            task_id = str(uuid.uuid4())
        
        # 创建任务信息
        task_info = TaskInfo(
            task_id=task_id,
            created_at=time.time(),
            status=TaskStatus.RUNNING
        )
        
        self.tasks[task_id] = task_info
        
        try:
            # 设置超时
            async with asyncio.timeout(timeout):
                async for event in graph.astream_events(
                    input_data, 
                    config=config, 
                    version="v2"
                ):
                    # 检查任务状态
                    if task_info.status == TaskStatus.CANCELLED:
                        break
                    
                    yield event
                    
        except asyncio.TimeoutError:
            task_info.status = TaskStatus.FAILED
            task_info.error = "Task timed out"
            raise
        except asyncio.CancelledError:
            task_info.status = TaskStatus.CANCELLED
            raise
        except Exception as e:
            task_info.status = TaskStatus.FAILED
            task_info.error = str(e)
            raise
        else:
            task_info.status = TaskStatus.COMPLETED
        finally:
            # 延迟清理，保留一段时间供查询
            asyncio.create_task(self._delayed_cleanup(task_id, delay=300))
    
    async def _delayed_cleanup(self, task_id: str, delay: float):
        """延迟清理任务"""
        await asyncio.sleep(delay)
        self.tasks.pop(task_id, None)

# 使用示例
task_manager = ProductionTaskManager()

@app.post("/research/stream/{task_id}")
async def managed_research_stream(
    task_id: str,
    request: ResearchRequest,
    http_request: Request
):
    """生产级的研究流端点"""
    
    async def event_stream():
        try:
            async for event in task_manager.create_managed_stream(
                graph=research_graph,
                input_data={"query": request.query},
                config=RunnableConfig(
                    configurable={"thread_id": request.thread_id}
                ),
                task_id=task_id,
                timeout=1800  # 30分钟超时
            ):
                # 检查客户端断连
                if await http_request.is_disconnected():
                    await task_manager.cancel_task(task_id)
                    break
                
                yield f"data: {json.dumps(event)}\n\n"
                
        except Exception as e:
            yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"
    
    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream"
    )
```

## 参考资源

1. **LangGraph官方文档**：https://langchain-ai.github.io/langgraph/
2. **LangChain取消执行指南**：https://js.langchain.com/v0.2/docs/how_to/cancel_execution/
3. **Streamlit + LangGraph集成案例**：https://github.com/shiv248/Streamlit-x-LangGraph-Cookbooks
4. **LangGraph异步执行教程**：https://aiproduct.engineer/tutorials/langgraph-tutorial-asynchronous-tool-execution
5. **LangGraph Platform部署指南**：https://langchain-ai.github.io/langgraph/cloud/quick_start/

这些方案和案例涵盖了从简单的前端取消到复杂的生产级任务管理的各种场景，你可以根据实际需求选择合适的方案。 