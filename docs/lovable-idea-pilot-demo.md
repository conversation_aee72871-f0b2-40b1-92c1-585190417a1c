# Lovable Idea Pilot Demo 项目

## 项目概述

这是一个基于 Lovable 平台创建的演示项目，使用现代前端技术栈构建。

## 技术栈

- **构建工具**: Vite 5.4.10
- **前端框架**: React 18.3.1
- **语言**: TypeScript
- **UI 组件库**: shadcn-ui (基于 Radix UI)
- **样式**: Tailwind CSS
- **状态管理**: TanStack React Query
- **路由**: React Router DOM
- **图表**: Recharts
- **流程图**: XYFlow React

## 项目结构

```
examples/lovable-idea-pilot-demo/
├── src/
│   ├── components/     # UI 组件
│   ├── hooks/         # 自定义 Hooks
│   ├── lib/           # 工具库
│   ├── pages/         # 页面组件
│   ├── App.tsx        # 主应用组件
│   └── main.tsx       # 应用入口
├── public/            # 静态资源
├── package.json       # 项目配置
├── vite.config.ts     # Vite 配置
├── tailwind.config.ts # Tailwind 配置
└── tsconfig.json      # TypeScript 配置
```

## 快速开始

### 启动项目

```bash
# 使用项目脚本启动（推荐）
./scripts/start-lovable-demo.sh

# 或者手动启动
cd examples/lovable-idea-pilot-demo
npm install
npm run dev
```

### 停止项目

```bash
# 使用项目脚本停止
./scripts/stop-lovable-demo.sh
```

### 查看日志

```bash
# 实时查看项目日志
./scripts/logs-lovable-demo.sh
```

## 访问地址

- **本地访问**: http://localhost:8080/
- **网络访问**: http://************:8080/

## 开发说明

### 可用脚本

- `npm run dev` - 启动开发服务器
- `npm run build` - 构建生产版本
- `npm run build:dev` - 构建开发版本
- `npm run lint` - 代码检查
- `npm run preview` - 预览构建结果

### 配置说明

- **端口**: 8080 (在 vite.config.ts 中配置)
- **主机**: "::" (支持 IPv6)
- **开发模式**: 启用了 lovable-tagger 组件标记

### 日志管理

- 日志文件位置: `logs/lovable-demo.log`
- 进程ID文件: `logs/lovable-demo.pid`
- 日志包含完整的 Vite 开发服务器输出

## 项目特性

1. **现代化开发体验**
   - 热重载 (HMR)
   - TypeScript 支持
   - ESLint 代码检查

2. **丰富的 UI 组件**
   - 完整的 shadcn-ui 组件库
   - Radix UI 原语支持
   - Tailwind CSS 样式系统

3. **数据可视化**
   - Recharts 图表库
   - XYFlow 流程图支持

4. **状态管理**
   - TanStack React Query
   - React Hook Form

## 注意事项

- 项目使用 Vite 作为构建工具，启动速度快
- 支持 TypeScript 严格模式
- 集成了 Lovable 平台的组件标记功能
- 建议使用 Node.js 18+ 版本
