# 工作台路由跳转方案技术设计文档

## 文档信息
- **创建时间**: 2025-01-16
- **文档类型**: 技术架构设计
- **影响范围**: 工作台前端架构重构
- **优先级**: 高

## 背景分析

### 当前嵌入式方案的问题
1. **浏览器兼容性**：无法响应 back/forward 按钮
2. **URL 分享**：无法直接分享 idea detail 链接  
3. **SEO 友好性**：搜索引擎无法索引具体内容
4. **用户体验**：违背用户对浏览器导航的预期
5. **状态管理复杂**：需要手动同步 URL 和状态

### 路由跳转方案的优势
1. **标准浏览器行为**：天然支持前进后退
2. **URL 可分享性**：每个页面都有独立 URL
3. **更好的可访问性**：符合 Web 标准
4. **简化状态管理**：路由即状态
5. **更好的性能**：可以按需加载

## 技术方案设计

### 推荐方案：Layout + 嵌套路由

#### 路由结构设计
```
/workbench-v1a/                    # 工作台布局页面
├── page.tsx                       # 默认重定向到 overview
├── layout.tsx                     # 共享的左侧导航 + 基础布局
├── overview/                      # 概览页面
│   └── page.tsx
├── projects/                      # 项目管理页面  
│   └── page.tsx
├── tools/                         # 分析工具页面
│   └── page.tsx
└── ideas/
    └── [ideaId]/                  # 动态路由
        └── page.tsx               # idea 详情页面
```

#### 核心组件架构
```
WorkbenchLayout                    # 布局组件
├── LeftSidebar                    # 左侧导航（固定）
└── MainContent                    # 右侧内容区（路由渲染）
    ├── TopBar (条件渲染)          # 非 idea detail 时显示
    └── {children}                 # 路由页面内容
```

## 实现细节

### 1. 布局组件（layout.tsx）
```typescript
// app/workbench-v1a/layout.tsx
export default function WorkbenchLayout({ children }) {
  const pathname = usePathname()
  const isIdeaDetail = pathname.includes('/ideas/')
  
  return (
    <div className="h-screen flex">
      <LeftSidebar />
      <div className="flex-1 flex flex-col">
        {!isIdeaDetail && <TopBar />}
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  )
}
```

### 2. 导航组件更新
```typescript
// LeftSidebar 使用 Next.js Link
<Link href="/workbench-v1a/overview" 
      className={pathname === '/workbench-v1a/overview' ? 'active' : ''}>
  概览
</Link>
```

### 3. 页面组件
```typescript
// app/workbench-v1a/overview/page.tsx
export default function OverviewPage() {
  return <CardStream onCardClick={(item) => 
    router.push(`/workbench-v1a/ideas/${item.id}`)
  } />
}

// app/workbench-v1a/ideas/[ideaId]/page.tsx  
export default function IdeaDetailPage({ params }) {
  const { ideaId } = params
  return <IdeaDetailEmbedded ideaId={ideaId} 
           onBack={() => router.push('/workbench-v1a/overview')} />
}
```

## 方案对比

| 特性 | 嵌套路由方案 | 当前嵌入式方案 |
|------|--------------|----------------|
| SEO友好 | ✅ 优秀 | ❌ 差 |
| URL语义化 | ✅ 清晰 | ❌ 无 |
| 浏览器导航 | ✅ 原生支持 | ❌ 不支持 |
| 代码分割 | ✅ 支持 | ❌ 不支持 |
| URL分享 | ✅ 支持 | ❌ 不支持 |
| 实现复杂度 | ⚠️ 中等 | ✅ 简单 |
| 迁移成本 | ⚠️ 较高 | ✅ 无 |

## 实施策略

### 阶段1：创建新的路由结构（不破坏现有功能）
1. 创建 `/workbench-v1a/layout.tsx`
2. 创建各子页面路由
3. 保留现有的 `/workbench-v1a/page.tsx` 作为临时方案

### 阶段2：实现布局组件
1. 从现有 `WorkbenchContainer` 提取 `LeftSidebar`
2. 创建新的 `WorkbenchLayout`
3. 实现条件渲染 `TopBar`

### 阶段3：迁移子页面  
1. 创建 `overview/page.tsx`（迁移 CardStream）
2. 创建 `projects/page.tsx`
3. 创建 `tools/page.tsx`
4. 创建 `ideas/[ideaId]/page.tsx`（使用现有 IdeaDetailEmbedded）

### 阶段4：更新组件交互逻辑  
1. 将状态管理改为路由跳转
2. 更新 LeftSidebar 使用 Link 组件
3. 修改 CardStream 的点击事件为路由跳转

### 阶段5：优化和清理
1. 移除旧的状态管理代码
2. 添加加载状态和错误处理
3. 优化性能（预加载、懒加载）

### 阶段6：增强功能
1. 添加面包屑导航
2. 支持浏览器书签
3. 添加页面元数据（title、description）

## 技术实现要点

### 1. 路径匹配和激活状态
```typescript
const isActive = (path: string) => pathname.startsWith(path)
```

### 2. 条件渲染 TopBar
```typescript
const hideTopBar = pathname.includes('/ideas/')
```

### 3. 数据预加载
```typescript
// 可以使用 Next.js 的 prefetch
<Link href="/workbench-v1a/ideas/123" prefetch={true}>
```

### 4. 错误边界
```typescript
// 为每个路由添加错误处理
export default function IdeaNotFound() {
  return <div>Idea not found</div>
}
```

## 风险评估

### 技术风险
1. **迁移复杂度**：需要重构现有组件结构
2. **状态丢失**：页面跳转可能导致某些临时状态丢失
3. **性能影响**：路由跳转可能比状态切换稍慢

### 缓解措施
1. **渐进迁移**：保持现有功能的同时并行开发
2. **状态持久化**：关键状态使用 sessionStorage 保存
3. **性能优化**：使用 Next.js 的预加载和代码分割

## 成功指标

### 用户体验指标
- 浏览器后退按钮响应率：100%
- URL 分享成功率：100%
- 页面加载时间：< 500ms

### 技术指标
- 代码复用率：> 90%
- 路由覆盖率：100%
- 错误率：< 1%

## 后续优化方向

1. **SEO 优化**：添加页面元数据和结构化数据
2. **性能优化**：实现路由级别的代码分割
3. **用户体验**：添加页面转场动画
4. **可访问性**：增强键盘导航和屏幕阅读器支持

## 结论

路由跳转方案在用户体验、SEO友好性和技术标准方面都显著优于当前的嵌入式方案。虽然迁移成本较高，但长期收益明显，建议按阶段实施。

该方案将使工作台更符合现代 Web 应用的标准，提供更好的用户体验和更强的功能扩展性。