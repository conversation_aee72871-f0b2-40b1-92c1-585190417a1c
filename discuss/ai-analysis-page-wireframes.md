# AI 分析页面线框图设计

## 用户流程概述

```
工作台 → 点击「新 Idea」→ 输入模态框 → 点击「开始分析」→ 跳转到分析页面 → 
事实核查 → 供应链影响分析 → 未来情况推演 → 投资建议生成 → 社区讨论收集 → 完整报告 → 继续对话
```

**核心设计原则**：
- **模块化渐进**：每个模块单独完成，用户主动选择下一步
- **状态持久**：已完成的模块保持可见（可折叠）
- **用户控制**：每步都需要用户主动驱动
- **灵活路径**：用户可以选择不同的分析深度和方向

## 线框图 1：输入模态框

```
┌─────────────────────────────────────────────────────────────────────┐
│  ✕                     From event to edge in one search             │
│                                                                     │
│  基于AI驱动的事件分析，快速验证事实、预测影响、构建投资逻辑            │
│                                                                     │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │ 🔍 输入您想要分析的市场事件或投资信息...                      │   │
│  │                                                             │   │
│  │ 例如：分析苹果发布 Vision Pro 对供应链公司的影响             │   │
│  │                                                             │   │
│  │                                                             │   │
│  │                                                             │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│  💡 深度核实    📎 附件 (0)                                         │
│                                                                     │
│  ─────────────────────────────────────────────────────────────────  │
│                                                                     │
│  💡 快速开始：                                                      │
│  • "英伟达财报超预期，相关概念股投资机会分析"                       │
│  • "美联储加息对房地产板块的影响及投资策略"                         │
│  • "新能源汽车政策变化对产业链的影响"                               │
│                                                                     │
│                                          [取消]    [开始分析]       │
└─────────────────────────────────────────────────────────────────────┘
```

**关键要素**：
- 大文本输入框，支持多行输入
- 示例提示帮助用户快速开始
- 深度核实和附件功能（可选）
- 明确的行动按钮

---

## 线框图 2：分析页面 - 初始准备状态

```
┌─────────────────────────────────────────────────────────────────────┐
│  ← 返回工作台    AI 投资分析                           [保存草稿]     │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  📊 分析任务：分析苹果发布 Vision Pro 对供应链公司的影响              │
│                                                                     │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │  🤖 准备开始AI深度分析                                      │   │
│  │                                                             │   │
│  │  我将从事实核查开始，为您提供准确可靠的投资分析：           │   │
│  │                                                             │   │
│  │  📋 第一步：事实核查与信息验证                              │   │
│  │  • 验证Vision Pro产品规格和发布信息                        │   │
│  │  • 核实供应链公司名单和角色                                 │   │
│  │  • 收集最新市场动态和新闻                                   │   │
│  │                                                             │   │
│  │  🔍 后续深度分析（按需进行）：                              │   │
│  │  • 供应链影响评估与量化分析                                 │   │
│  │  • 未来情况推演与风险评估                                   │   │
│  │  • 具体投资建议与时机判断                                   │   │
│  │                                                             │   │
│  │                            [开始事实核查]                  │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

**关键要素**：
- 明确从事实核查开始的专业流程
- 说明第一步的具体工作内容
- 预告后续分析选项但不预先展示框架
- 需要用户主动点击开始按钮

---

## 线框图 3：事实核查进行中

```
┌─────────────────────────────────────────────────────────────────────┐
│  ← 返回工作台    AI 投资分析                           [保存草稿]     │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  📊 分析任务：分析苹果发布 Vision Pro 对供应链公司的影响              │
│                                                                     │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │  🔍 正在进行事实核查...                                     │   │
│  │                                                             │   │
│  │  ⏳ 验证产品信息和技术规格                                  │   │
│  │  ├─ ✅ Vision Pro基础信息收集完成                          │   │
│  │  ├─ 🔄 核实技术规格数据...                                 │   │
│  │  ├─ ⏸️ 识别核心供应商                                       │   │
│  │  └─ ⏸️ 收集相关新闻和市场动态                               │   │
│  │                                                             │   │
│  │  预计完成时间：1-2 分钟                                     │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

**关键要素**：
- 专注于事实核查的单一任务
- 清晰的验证步骤进度
- 简洁的界面，为结果展示预留空间

---

## 线框图 4：事实核查完成，提供下一步选择

```
┌─────────────────────────────────────────────────────────────────────┐
│  ← 返回工作台    AI 投资分析                    [收藏] [分享] [保存到工作台] │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  📊 分析任务：分析苹果发布 Vision Pro 对供应链公司的影响              │
│                                                                     │
│  ✅ 事实核查已完成                                                   │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │  🔍 事实核查与信息验证结果                                  │   │
│  │                                                             │   │
│  │  左栏：核实结果         │ 右栏：相关变化                    │   │
│  │  ────────────────────── │ ──────────────────────            │   │
│  │  ✅ 已验证              │ 📰 微软Azure率先部署              │   │
│  │  Vision Pro规格确实     │    H200集群                       │   │
│  │  采用双4K Micro-OLED屏  │ 📰 OpenAI签署20亿美元             │   │
│  │                         │    采购协议                       │   │
│  │  ✅ 已验证              │ 📰 台积电确认H200产能             │   │
│  │  核心供应商名单包括     │    将在Q2达到月产20万颗           │   │
│  │  台积电、歌尔股份等     │                                   │   │
│  │                         │ ⚠️ 需注意：竞争对手              │   │
│  │  ⚠️ 需注意              │    AMD发布MI300X芯片              │   │
│  │  初期出货量预估较为有限 │                                   │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│  💡 选择下一步深度分析                                              │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │  基于以上核实的事实，您希望深入分析哪个方面：                │   │
│  │                                                             │   │
│  │  [📈 供应链影响分析]  [🔮 未来情况推演]  [💰 投资建议生成]   │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

**关键要素**：
- 左右分栏展示核实结果和相关变化（参考LatestChanges组件）
- 用户主动选择下一步分析方向
- 事实核查结果为后续分析提供可靠基础

---

## 线框图 5：供应链影响分析进行中

```
┌─────────────────────────────────────────────────────────────────────┐
│  ← 返回工作台    AI 投资分析                    [收藏] [分享] [保存到工作台] │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  📊 分析任务：分析苹果发布 Vision Pro 对供应链公司的影响              │
│                                                                     │
│  ✅ 事实核查已完成                                                   │
│  [事实核查结果折叠显示...]                                          │
│                                                                     │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │  📈 正在分析供应链影响...                                   │   │
│  │                                                             │   │
│  │  ⏳ 评估各环节供应商的受益程度                              │   │
│  │  ├─ ✅ 产品技术门槛分析完成                                 │   │
│  │  ├─ 🔄 供应商分层评估中...                                  │   │
│  │  └─ ⏸️ 量化影响程度计算                                     │   │
│  │                                                             │   │
│  │  预计完成时间：2-3 分钟                                     │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

**关键要素**：
- 在事实核查基础上进行供应链影响分析
- 显示清晰的分析步骤进度
- 继续用简洁的界面设计

---

## 线框图 6：供应链影响分析完成

```
┌─────────────────────────────────────────────────────────────────────┐
│  ← 返回工作台    AI 投资分析                    [收藏] [分享] [保存到工作台] │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  📊 分析任务：分析苹果发布 Vision Pro 对供应链公司的影响              │
│                                                                     │
│  ✅ 事实核查已完成  ✅ 供应链影响分析已完成                          │
│  [事实核查结果折叠显示...]                                          │
│                                                                     │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │  📈 供应链影响分析结果                                      │   │
│  │                                                             │   │
│  │  🏭 核心受益供应商（高影响）                               │   │
│  │  • 歌尔股份 (002241.SZ) - 声学器件  ⭐⭐⭐⭐⭐           │   │
│  │  • 立讯精密 (002475.SZ) - 连接器  ⭐⭐⭐⭐⭐             │   │
│  │                                                             │   │
│  │  🏭 中等受益供应商（中影响）                               │   │
│  │  • 台积电 (TSM) - 芯片制造  ⭐⭐⭐⭐                     │   │
│  │  • 富士康 (2317.TW) - 组装制造  ⭐⭐⭐                   │   │
│  │                                                             │   │
│  │  📊 影响程度量化：核心光学供应商营收增长15-25%              │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│  💡 继续深度分析                                                    │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │  [🔮 未来情况推演]  [💰 投资建议生成]  [💬 获取讨论观点]     │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

**关键要素**：
- 展示供应链分析的完整结果
- 按影响程度分类展示供应商
- 提供量化的影响评估
- 继续提供多种分析选项

---

## 线框图 7：未来情况推演进行中

```
┌─────────────────────────────────────────────────────────────────────┐
│  ← 返回工作台    AI 投资分析                    [收藏] [分享] [保存到工作台] │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  📊 分析任务：分析苹果发布 Vision Pro 对供应链公司的影响              │
│                                                                     │
│  ✅ 事实核查  ✅ 供应链影响分析                                     │
│  [前面模块折叠显示...]                                              │
│                                                                     │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │  🔮 正在进行未来情况推演...                                 │   │
│  │                                                             │   │
│  │  ⏳ 基于市场数据构建多种可能情况                            │   │
│  │  ├─ ✅ 市场成功情况分析完成                                 │   │
│  │  ├─ 🔄 竞争加剧情况推演中...                                │   │
│  │  └─ ⏸️ 技术迭代影响评估                                     │   │
│  │                                                             │   │
│  │  预计完成时间：3-4 分钟                                     │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

**关键要素**：
- 在前面模块基础上进行情况推演
- 显示多种情况的构建进度
- 保持前面模块的折叠显示

---

## 线框图 8：未来情况推演完成

```
┌─────────────────────────────────────────────────────────────────────┐
│  ← 返回工作台    AI 投资分析                    [收藏] [分享] [保存到工作台] │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  📊 分析任务：分析苹果发布 Vision Pro 对供应链公司的影响              │
│                                                                     │
│  ✅ 事实核查  ✅ 供应链影响分析  ✅ 未来情况推演                     │
│  [前面模块折叠显示...]                                              │
│                                                                     │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │  🔮 未来情况推演结果                                        │   │
│  │                                                             │   │
│  │  ┌─────────────────────────────────┬─────────────────────┐   │   │
│  │  │ 情况1: 市场成功 (高可能性)     │ 📄 参考资料         │   │   │
│  │  │ 概率: 85%  影响: +20-30%        │ • 市场研究报告      │   │   │
│  │  │ AR市场迅速增长，供应商大幅受益 │ • 行业分析师观点  │   │   │
│  │  └─────────────────────────────────┴─────────────────────┘   │   │
│  │                                                             │   │
│  │  ┌─────────────────────────────────┬─────────────────────┐   │   │
│  │  │ 情况2: 竞争加剧 (中可能性)     │ 📄 参考资料         │   │   │
│  │  │ 概率: 65%  影响: +5-15%         │ • 竞争对手动态      │   │   │
│  │  │ 其他厂商加入，市场竞争激烈     │ • 价格竞争分析      │   │   │
│  │  └─────────────────────────────────┴─────────────────────┘   │   │
│  │                                                             │   │
│  │  [展开更多情况推演...]                                      │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│  💡 完善投资分析                                                    │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │  [💰 生成投资建议]  [💬 获取讨论观点]  [📊 查看股票推荐]     │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

**关键要素**：
- 展示多种情况的左右分栏设计（情况描述+参考资料）
- 提供概率评估和影响程度量化
- 继续提供多种后续分析选项

---

## 线框图 9：最终完整状态（参考 IdeaDetail 设计）

```
┌─────────────────────────────────────────────────────────────────────┐
│  ← 返回工作台    AI 投资分析                    [收藏] [分享] [保存到工作台] │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  📊 Vision Pro 供应链影响分析报告                                   │
│                                                                     │
│  ✅ 事实核查  ✅ 供应链分析  ✅ 情况推演  ✅ 投资建议  ✅ 讨论收集   │
│                                                                     │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │  🤖 Vision Pro 供应链影响分析总结                           │   │
│  │                                                             │   │
│  │  苹果 Vision Pro 的发布对供应链公司带来分化影响。核心光学   │   │
│  │  和显示技术供应商将显著受益，预计相关公司营收增长 15-25%。  │   │
│  │                                                             │   │
│  │  🕐 分析完成：2024-01-15 14:23  ⏱️ 用时：8分39秒           │   │
│  │  🏷️ 标签：AR设备 · 供应链 · 苹果生态                       │   │
│  │  📊 置信度：85%  🎯 投资评级：强烈推荐                      │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│  [所有模块完整展示，支持展开/折叠...]                               │
│                                                                     │
│  💬 继续深入对话                                                    │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │  [输入继续提问的内容...]                      [发送]         │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

**关键要素**：
- 所有模块完整展示，支持展开/折叠
- 模块完成状态清晰标识
- 支持继续深入对话和探索
- 参考IdeaDetail的成熟设计模式

---

## 线框图 10：对话继续状态

```
┌─────────────────────────────────────────────────────────────────────┐
│  ← 返回工作台    AI 投资分析                    [导出报告] [保存到工作台] │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  📊 Vision Pro 供应链影响分析报告                                   │
│                                                                     │
│  [分析报告内容折叠显示...]                                          │
│                                                                     │
│  💬 对话历史                                                        │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │  👤 用户：这些公司的具体财务数据如何？                       │   │
│  │                                                             │   │
│  │  🤖 AI：我来为您分析这些重点公司的财务表现：                │   │
│  │                                                             │   │
│  │  📊 歌尔股份 (002241.SZ)                                   │   │
│  │  • 2023年营收：¥95.2亿 (同比+12.3%)                       │   │
│  │  • 毛利率：18.5% (行业领先)                                │   │
│  │  • ROE：15.2% (盈利能力强)                                 │   │
│  │  • 负债率：45.3% (财务稳健)                                │   │
│  │                                                             │   │
│  │  📊 立讯精密 (002475.SZ)                                   │   │
│  │  • 2023年营收：¥1,456亿 (同比+8.7%)                       │   │
│  │  • 毛利率：12.8% (规模效应明显)                            │   │
│  │  • ROE：18.9% (行业顶尖)                                   │   │
│  │  • 负债率：52.1% (合理范围)                                │   │
│  │                                                             │   │
│  │  💡 财务亮点：两家公司都保持稳健增长，现金流充裕...         │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │  👤 用户：投资时机和买入价位建议？                           │   │
│  │                                                             │   │
│  │  🤖 AI：基于技术分析和基本面，我的建议是：                  │   │
│  │                                                             │   │
│  │  🎯 歌尔股份建议买入区间：                                  │   │
│  │  • 理想价位：¥18-20 (当前¥22.5)                           │   │
│  │  • 支撑位：¥17.8 (20日均线)                               │   │
│  │  • 目标价：¥28-32 (12个月)                                │   │
│  │                                                             │   │
│  │  🎯 立讯精密建议买入区间：                                  │   │
│  │  • 理想价位：¥32-35 (当前¥38.2)                           │   │
│  │  • 支撑位：¥31.5 (60日均线)                               │   │
│  │  • 目标价：¥45-50 (12个月)                                │   │
│  │                                                             │   │
│  │  ⏰ 建议分批建仓，关注Q1财报验证...                         │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
│  ┌─────────────────────────────────────────────────────────────┐   │
│  │  [继续提问...]                                  [发送]      │   │
│  └─────────────────────────────────────────────────────────────┘   │
│                                                                     │
└─────────────────────────────────────────────────────────────────────┘
```

**关键要素**：
- 对话历史清晰展示
- 详细的财务数据分析
- 具体的投资建议和价位
- 持续对话能力

---

## 技术实现要点

### 1. 路由设计
```
/workbench-v1a/analysis/[taskId]
```

### 2. 参考现有实现
基于 `examples/lovable-idea-pilot-demo/src/pages/IdeaDetail.tsx` 的设计模式：

**核心组件复用**：
- `MainLayout` - 主布局框架
- `Card` 系列组件 - 卡片式内容展示
- `Badge` 组件 - 状态和标签显示
- `AiThinkingFlow` - AI 思考流程（可改造为分析流程）

**数据结构参考**：
```typescript
interface AnalysisTask {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  progress: number;
  results?: {
    scenarios: FutureScenario[];
    recommendations: StockRecommendation[];
    riskFactors: RiskFactor[];
  };
  tags: string[];
  createdAt: string;
  completedAt?: string;
}
```

### 3. 组件结构
```
AnalysisPage/
├── AnalysisHeader.tsx      # 参考 IdeaDetail 顶部设计
├── AnalysisProgress.tsx    # 进度显示（新增）
├── AnalysisResults.tsx     # 参考 AiThinkingFlow 设计
├── StockRecommendations.tsx # 复用现有组件
├── DiscussionSection.tsx   # 参考现有讨论区设计
└── hooks/
    ├── useAnalysisTask.ts  # 任务管理
    └── useRealTimeUpdates.ts # 实时更新
```

### 4. 数据流设计
```
模态框输入 → 创建分析任务 → 跳转到分析页面 →
WebSocket 实时更新 → 显示分析结果（参考 IdeaDetail 布局） → 支持对话继续
```

### 5. 状态管理
- 分析任务状态：`pending` | `analyzing` | `completed` | `error`
- 实时进度更新：WebSocket 或 Server-Sent Events
- 对话历史管理
- 结果缓存机制

这个设计提供了完整的 AI 分析体验，从输入到结果展示，再到深入对话，形成了一个完整的投资分析工作流。同时充分复用了现有 IdeaDetail 页面的成熟设计模式和组件。
