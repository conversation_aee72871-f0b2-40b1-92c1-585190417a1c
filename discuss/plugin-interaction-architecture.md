# 插件间交互技术方案完整设计文档

## 目录
1. [交互形态分类](#1-交互形态分类)
2. [技术方案对比](#2-技术方案对比)
3. [具体实现方案](#3-具体实现方案)
4. [最佳实践建议](#4-最佳实践建议)
5. [架构演进路径](#5-架构演进路径)

---

## 1. 交互形态分类

### 1.1 组件嵌入型交互
插件A需要在插件B的界面中嵌入自己的组件。

#### 场景示例
```typescript
// workbench 需要在工具栏中嵌入其他插件的按钮/组件
<TopBar>
  <SearchInput />                    // workbench 自带
  <IdeaFeatureButton />             // idea-feature 插件提供
  <NotificationBell />              // notification 插件提供
  <UserProfile />                   // user-account 插件提供
</TopBar>
```

#### 挑战
- 如何动态发现和加载插件组件？
- 如何保证组件样式一致性？
- 如何处理组件间的数据流？

### 1.2 模态框/弹窗型交互
插件A触发插件B显示模态框或弹窗。

#### 场景示例
```typescript
// 点击按钮 -> 显示AI分析模态框
<Button onClick={() => showAIModal()}>新 Idea</Button>

// 或者级联模态框
AI分析完成 -> 显示结果模态框 -> 显示保存选项模态框
```

#### 挑战
- 模态框的层级管理（z-index）
- 多个插件同时请求显示模态框的冲突处理
- 模态框间的数据传递

### 1.3 页面路由型交互
插件A需要跳转到插件B提供的页面。

#### 场景示例
```typescript
// 从工作台跳转到分析页面
/workbench-v1a -> /workbench-v1a/analysis/[taskId]

// 跨插件页面跳转
/workbench-v1a -> /user-profile -> /settings
```

#### 挑战
- 路由命名空间冲突
- 页面间状态传递
- 深层路由的插件依赖

### 1.4 数据流型交互
插件间需要共享数据、状态或进行通信。

#### 场景示例
```typescript
// 全局状态共享
user-account 插件更新用户信息 -> workbench 显示新的用户名

// 实时数据流
stock-data 插件获取股价 -> chart 插件更新图表 -> alert 插件检查预警
```

#### 挑战
- 数据一致性保证
- 性能优化（避免不必要的重渲染）
- 数据流的可追踪性

### 1.5 生命周期型交互
插件需要在特定时机执行某些操作。

#### 场景示例
```typescript
// 应用启动时
app-init -> user-account 插件初始化 -> workbench 插件加载 -> idea-feature 插件就绪

// 用户操作时
用户登出 -> 清理所有插件状态 -> 跳转到登录页
```

#### 挑战
- 插件加载顺序管理
- 异步初始化的协调
- 错误恢复机制

### 1.6 服务型交互
插件A需要使用插件B提供的服务或功能。

#### 场景示例
```typescript
// AI 分析插件需要使用通知插件发送提醒
aiAnalysis.complete() -> notification.send("分析完成")

// 多个插件需要使用同一个数据源
stock-plugin.getPrice() -> chart-plugin, alert-plugin, analysis-plugin
```

#### 挑战
- 服务发现机制
- 服务依赖管理
- 服务版本兼容性

---

## 2. 技术方案对比

### 2.1 直接依赖方案 ❌

```typescript
// 插件A直接导入插件B
import { ComponentB } from '@project/plugin-b';
```

**优点**：
- 实现简单直接
- TypeScript 类型检查完整
- IDE 支持良好

**缺点**：
- 强耦合，无法独立开发
- 循环依赖风险
- 单元测试困难
- 无法动态加载插件

**适用场景**：❌ 不推荐

---

### 2.2 事件总线方案 ⭐⭐⭐⭐

```typescript
// 发布-订阅模式
eventBus.emit('idea:create', data);
eventBus.on('idea:created', handler);
```

**优点**：
- 完全解耦
- 支持一对多通信
- 动态插件加载
- 易于调试和监控

**缺点**：
- 运行时错误（事件名拼写错误）
- 数据流不直观
- 过度使用会导致代码难以理解

**适用场景**：
- ✅ 数据流型交互
- ✅ 生命周期型交互
- ✅ 服务型交互

#### 实现示例

```typescript
// libs/shared-fe-event-bus/src/index.ts
export interface EventMap {
  'idea:create-request': { source: string };
  'idea:created': { id: string, data: AITaskInput };
  'user:logout': void;
  'notification:send': { message: string, type: 'info' | 'error' };
}

export class TypedEventBus {
  private listeners = new Map<keyof EventMap, Function[]>();
  
  emit<K extends keyof EventMap>(event: K, data: EventMap[K]) {
    const handlers = this.listeners.get(event) || [];
    handlers.forEach(handler => handler(data));
  }
  
  on<K extends keyof EventMap>(event: K, handler: (data: EventMap[K]) => void) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(handler);
  }
}
```

---

### 2.3 应用层协调方案 ⭐⭐⭐⭐⭐

```typescript
// 在应用层统一管理插件交互
<App>
  <PluginA onAction={handleA} />
  <PluginB onTrigger={handleB} />
</App>
```

**优点**：
- 插件零依赖
- 类型安全
- 清晰的数据流
- 易于测试和维护

**缺点**：
- 应用层代码较多
- 需要提前规划接口

**适用场景**：
- ✅ 组件嵌入型交互
- ✅ 模态框型交互
- ✅ 页面路由型交互

#### 实现示例

```typescript
// apps/web-app/src/components/PluginOrchestrator.tsx
export function PluginOrchestrator() {
  const [modals, setModals] = useState({
    aiTask: false,
    notification: false,
  });
  
  const [sharedData, setSharedData] = useState({
    user: null,
    currentTask: null,
  });

  return (
    <>
      <WorkbenchPlugin 
        onNewIdea={() => setModals(prev => ({ ...prev, aiTask: true }))}
        userData={sharedData.user}
      />
      
      <IdeaFeaturePlugin
        showModal={modals.aiTask}
        onClose={() => setModals(prev => ({ ...prev, aiTask: false }))}
        onTaskCreated={(task) => setSharedData(prev => ({ ...prev, currentTask: task }))}
      />
      
      <NotificationPlugin
        showModal={modals.notification}
        onClose={() => setModals(prev => ({ ...prev, notification: false }))}
      />
    </>
  );
}
```

---

### 2.4 插件注册表方案 ⭐⭐⭐⭐

```typescript
// 中央注册表管理插件
pluginRegistry.register('idea-feature', IdeaFeaturePlugin);
const IdeaPlugin = pluginRegistry.get('idea-feature');
```

**优点**：
- 支持动态插件加载
- 统一的插件管理
- 版本控制和依赖管理

**缺点**：
- 复杂度较高
- 需要完善的插件规范

**适用场景**：
- ✅ 组件嵌入型交互
- ✅ 服务型交互
- ✅ 大型插件系统

#### 实现示例

```typescript
// libs/shared-fe-plugin-system/src/registry.ts
interface PluginDefinition {
  name: string;
  version: string;
  dependencies?: string[];
  components?: Record<string, React.ComponentType>;
  services?: Record<string, any>;
  lifecycle?: {
    onLoad?: () => void;
    onUnload?: () => void;
  };
}

export class PluginRegistry {
  private plugins = new Map<string, PluginDefinition>();
  private loadedComponents = new Map<string, React.ComponentType>();

  register(plugin: PluginDefinition) {
    // 检查依赖
    this.checkDependencies(plugin);
    
    // 注册插件
    this.plugins.set(plugin.name, plugin);
    
    // 注册组件
    if (plugin.components) {
      Object.entries(plugin.components).forEach(([name, component]) => {
        this.loadedComponents.set(`${plugin.name}.${name}`, component);
      });
    }
    
    // 执行生命周期
    plugin.lifecycle?.onLoad?.();
  }

  getComponent(fullName: string): React.ComponentType | null {
    return this.loadedComponents.get(fullName) || null;
  }
}
```

---

### 2.5 Context/Provider 方案 ⭐⭐⭐

```typescript
// 通过 React Context 共享状态
<PluginProvider>
  <WorkbenchPlugin />
  <IdeaFeaturePlugin />
</PluginProvider>
```

**优点**：
- React 生态原生支持
- 自动依赖更新
- 相对简单

**缺点**：
- 仅限 React 组件
- 深层嵌套性能问题
- 过度使用导致 Provider 地狱

**适用场景**：
- ✅ 数据流型交互
- ✅ 中小型项目

#### 实现示例

```typescript
// libs/shared-fe-context/src/PluginContext.tsx
interface PluginContextValue {
  // 状态
  user: User | null;
  currentTask: Task | null;
  modals: ModalState;
  
  // 操作
  setUser: (user: User) => void;
  showModal: (modal: string) => void;
  hideModal: (modal: string) => void;
  createTask: (data: AITaskInput) => Promise<string>;
}

export const PluginContext = createContext<PluginContextValue | null>(null);

export function PluginProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState({
    user: null,
    currentTask: null,
    modals: {},
  });

  const contextValue = useMemo(() => ({
    ...state,
    setUser: (user: User) => setState(prev => ({ ...prev, user })),
    showModal: (modal: string) => setState(prev => ({ 
      ...prev, 
      modals: { ...prev.modals, [modal]: true }
    })),
    // ... 更多操作
  }), [state]);

  return (
    <PluginContext.Provider value={contextValue}>
      {children}
    </PluginContext.Provider>
  );
}
```

---

### 2.6 微前端方案 ⭐⭐⭐

```typescript
// 每个插件是独立的应用
<iframe src="/plugin-workbench" />
<iframe src="/plugin-idea-feature" />
```

**优点**：
- 完全隔离
- 技术栈无关
- 可独立部署

**缺点**：
- 性能开销大
- 样式隔离复杂
- 通信机制复杂

**适用场景**：
- ✅ 大型企业级应用
- ✅ 多团队独立开发
- ⚠️ 需要完全隔离的场景

---

## 3. 具体实现方案

### 3.1 推荐架构：混合方案

基于不同交互形态采用不同技术方案：

```
交互形态 -> 技术方案映射

组件嵌入 -> 应用层协调 + 插件注册表
模态框交互 -> 应用层协调
页面路由 -> 应用层协调 + Next.js 路由
数据流交互 -> 事件总线 + Context
生命周期 -> 事件总线
服务交互 -> 插件注册表 + 依赖注入
```

### 3.2 项目结构

```
yai-investor-insight/
├── apps/
│   └── web-app/                     # 应用协调层
│       ├── src/components/
│       │   ├── PluginOrchestrator.tsx   # 插件编排
│       │   └── PluginRegistry.tsx       # 插件注册
│       └── src/app/
├── libs/
│   ├── shared-fe-plugin-system/     # 插件系统核心
│   │   ├── registry.ts              # 插件注册表
│   │   ├── types.ts                 # 插件接口定义
│   │   └── lifecycle.ts             # 生命周期管理
│   ├── shared-fe-event-bus/         # 事件通信
│   │   ├── EventBus.ts             # 事件总线
│   │   └── events.ts               # 事件类型定义
│   ├── shared-fe-context/           # 状态共享
│   │   └── PluginContext.tsx       # 全局上下文
│   ├── workbench-v1a-fe/           # 工作台插件（纯组件）
│   ├── idea-feature-v1a-fe/        # AI功能插件（纯组件）
│   └── shared-fe-types/            # 共享类型定义
```

### 3.3 插件标准接口定义

```typescript
// libs/shared-fe-plugin-system/src/types.ts
export interface PluginMetadata {
  name: string;
  version: string;
  displayName: string;
  description: string;
  author: string;
  dependencies?: string[];
  peerDependencies?: string[];
}

export interface PluginComponents {
  // 可嵌入组件
  [componentName: string]: React.ComponentType<any>;
}

export interface PluginServices {
  // 插件提供的服务
  [serviceName: string]: any;
}

export interface PluginRoutes {
  // 插件路由配置
  [routePath: string]: {
    component: React.ComponentType;
    exact?: boolean;
    guard?: (context: any) => boolean;
  };
}

export interface PluginLifecycle {
  onLoad?: (context: PluginContext) => Promise<void> | void;
  onUnload?: (context: PluginContext) => Promise<void> | void;
  onActivate?: (context: PluginContext) => Promise<void> | void;
  onDeactivate?: (context: PluginContext) => Promise<void> | void;
}

export interface PluginDefinition {
  metadata: PluginMetadata;
  components?: PluginComponents;
  services?: PluginServices;
  routes?: PluginRoutes;
  lifecycle?: PluginLifecycle;
}
```

### 3.4 应用协调层实现

```typescript
// apps/web-app/src/components/PluginOrchestrator.tsx
import { useState, useEffect } from 'react';
import { eventBus } from '@yai-investor-insight/shared-fe-event-bus';
import { pluginRegistry } from '@yai-investor-insight/shared-fe-plugin-system';

export function PluginOrchestrator() {
  const [appState, setAppState] = useState({
    user: null,
    modals: {},
    currentPage: 'workbench',
  });

  // 处理模态框交互
  const handleShowModal = (modalName: string, data?: any) => {
    setAppState(prev => ({
      ...prev,
      modals: { ...prev.modals, [modalName]: { visible: true, data } }
    }));
  };

  const handleHideModal = (modalName: string) => {
    setAppState(prev => ({
      ...prev,
      modals: { ...prev.modals, [modalName]: { visible: false } }
    }));
  };

  // 处理页面路由
  const handleNavigate = (page: string, params?: any) => {
    setAppState(prev => ({ ...prev, currentPage: page }));
    // 触发路由变化事件
    eventBus.emit('navigation:change', { page, params });
  };

  // 事件监听
  useEffect(() => {
    eventBus.on('modal:show', handleShowModal);
    eventBus.on('modal:hide', handleHideModal);
    eventBus.on('navigation:navigate', handleNavigate);

    return () => {
      eventBus.off('modal:show', handleShowModal);
      eventBus.off('modal:hide', handleHideModal);
      eventBus.off('navigation:navigate', handleNavigate);
    };
  }, []);

  // 获取插件组件
  const WorkbenchComponent = pluginRegistry.getComponent('workbench.MainPage');
  const IdeaModalComponent = pluginRegistry.getComponent('idea-feature.AITaskModal');

  return (
    <div className="app-container">
      {/* 主要内容区域 */}
      {appState.currentPage === 'workbench' && WorkbenchComponent && (
        <WorkbenchComponent
          user={appState.user}
          onNewIdea={() => handleShowModal('aiTask')}
          onNavigate={handleNavigate}
        />
      )}

      {/* 模态框区域 */}
      {appState.modals.aiTask?.visible && IdeaModalComponent && (
        <IdeaModalComponent
          open={true}
          data={appState.modals.aiTask.data}
          onClose={() => handleHideModal('aiTask')}
          onSubmit={(data) => {
            // 处理提交
            eventBus.emit('idea:created', data);
            handleHideModal('aiTask');
          }}
        />
      )}
    </div>
  );
}
```

### 3.5 插件实现示例

```typescript
// libs/workbench-v1a-fe/src/plugin.ts
import { PluginDefinition } from '@yai-investor-insight/shared-fe-plugin-system';
import { WorkbenchContainer } from './components/workbench-container';
import { TopBar } from './components/TopBar';

export const workbenchPlugin: PluginDefinition = {
  metadata: {
    name: 'workbench-v1a',
    version: '1.0.0',
    displayName: '工作台 V1A',
    description: '投资分析工作台',
    author: 'YAI Team',
  },
  
  components: {
    MainPage: WorkbenchContainer,
    TopBar: TopBar,
  },
  
  services: {
    navigation: {
      goToAnalysis: (taskId: string) => {
        eventBus.emit('navigation:navigate', { 
          page: 'analysis', 
          params: { taskId } 
        });
      },
    },
  },
  
  lifecycle: {
    onLoad: () => {
      console.log('Workbench plugin loaded');
    },
  },
};
```

```typescript
// libs/idea-feature-v1a-fe/src/plugin.ts
export const ideaFeaturePlugin: PluginDefinition = {
  metadata: {
    name: 'idea-feature-v1a',
    version: '1.0.0',
    displayName: 'AI Idea 分析',
    description: 'AI 驱动的投资想法分析功能',
    author: 'YAI Team',
    dependencies: ['workbench-v1a'], // 声明依赖关系
  },
  
  components: {
    AITaskModal: AITaskModal,
    AnalysisPage: AnalysisPage,
  },
  
  routes: {
    '/analysis/:taskId': {
      component: AnalysisPage,
      exact: true,
    },
  },
  
  services: {
    aiAnalysis: {
      createTask: async (input: AITaskInput) => {
        // 创建分析任务
        const taskId = await api.createAnalysisTask(input);
        return taskId;
      },
    },
  },
  
  lifecycle: {
    onLoad: () => {
      // 监听相关事件
      eventBus.on('idea:create-request', () => {
        eventBus.emit('modal:show', 'aiTask');
      });
    },
  },
};
```

---

## 4. 最佳实践建议

### 4.1 设计原则

1. **单一职责**：每个插件只负责一个核心功能
2. **松耦合**：插件间通过标准接口通信，不直接依赖
3. **高内聚**：插件内部组件紧密相关
4. **可测试**：插件可独立测试，接口明确
5. **可扩展**：支持动态加载和卸载插件

### 4.2 命名约定

```typescript
// 插件命名：{domain}-{version}-{platform}
workbench-v1a-fe
idea-feature-v1a-fe
user-account-v2-fe

// 组件命名：{Plugin}.{Component}
workbench.TopBar
idea-feature.AITaskModal

// 事件命名：{domain}:{action}
idea:create-request
idea:created
user:login
user:logout

// 服务命名：{domain}.{service}
idea.aiAnalysis
user.authentication
```

### 4.3 版本管理

```typescript
// 插件版本兼容性检查
interface VersionConstraint {
  min?: string;
  max?: string;
  exact?: string;
}

interface PluginDependency {
  name: string;
  version: VersionConstraint;
  optional?: boolean;
}
```

### 4.4 错误处理

```typescript
// 插件错误边界
export function PluginErrorBoundary({ 
  pluginName, 
  children 
}: { 
  pluginName: string;
  children: ReactNode;
}) {
  return (
    <ErrorBoundary
      fallback={<PluginErrorFallback pluginName={pluginName} />}
      onError={(error) => {
        eventBus.emit('plugin:error', { pluginName, error });
      }}
    >
      {children}
    </ErrorBoundary>
  );
}
```

### 4.5 性能优化

```typescript
// 懒加载插件
const LazyIdeaFeatureModal = lazy(() => 
  import('@yai-investor-insight/idea-feature-v1a-fe').then(module => ({
    default: module.AITaskModal
  }))
);

// 插件预加载策略
const preloadPlugin = (pluginName: string) => {
  // 在空闲时间预加载插件
  requestIdleCallback(() => {
    import(`@yai-investor-insight/${pluginName}`);
  });
};
```

---

## 5. 架构演进路径

### 阶段1：简单协调（当前推荐）
- 使用应用层协调处理主要交互
- 适合中小型项目和快速迭代

### 阶段2：混合方案
- 组件嵌入使用插件注册表
- 数据流使用事件总线
- 适合功能复杂度增加的情况

### 阶段3：完整插件系统
- 实现完整的插件生命周期管理
- 支持动态加载/卸载
- 插件商店和版本管理
- 适合大型企业级应用

### 阶段4：微前端架构
- 插件完全独立部署
- 跨技术栈支持
- 适合多团队大规模开发

---

## 6. 实际案例分析

### 6.1 当前项目插件分析

基于代码分析，项目中的 -fe 插件包括：

| 插件名称 | 主要组件 | 功能说明 |
|---------|----------|----------|
| **workbench-v1a-fe** | WorkbenchContainer, TopBar, CardStream | 工作台主界面，提供卡片流、标签过滤、顶部导航栏 |
| **idea-feature-v1a-fe** | AITaskModal, AIPromptInput, ExamplePrompts | AI 分析任务创建，模态框交互 |
| **research-v2-fe** | ResearchContainer, ResearchSidebar, 各种 Stage 组件 | 研究流程管理，多阶段研究界面 |
| **research-v2b-fe** | 增强版研究组件，SSE 事件处理 | 基于事件流的研究界面 |
| **research-v2h-fe** | 分层架构的研究组件 | 更完善的研究工作流 |
| **user-account-fe** | LoginForm, RegisterForm, UserProfile | 用户账户管理 |
| **demo-feature-fe** | 各种测试组件 | 演示和测试功能 |

### 6.2 当前项目问题

从代码分析发现，确实存在**直接插件依赖**的问题：

```typescript
// ❌ 问题代码 - workbench 直接依赖 idea-feature
import { AITaskModal } from "@yai-investor-insight/idea-feature-v1a-fe";
```

另外，还发现了一些其他架构问题：

1. **组件重复**：多个 -fe 插件都有自己的 UI 组件（button, dialog 等），缺乏统一的设计系统
2. **状态管理分散**：每个插件都有自己的状态管理，缺乏统一的数据流
3. **类型定义重复**：相似的类型定义在不同插件中重复出现

### 6.3 具体交互场景

基于实际代码，以下是典型的插件交互场景：

#### 场景1：工作台嵌入 AI 任务按钮
```typescript
// 当前实现（存在问题）
// workbench TopBar 中直接导入 idea-feature 的模态框
const handleAnalysisSubmit = (data: any) => {
  // 这里需要调用 idea-feature 的功能
};
```

#### 场景2：研究流程中嵌入用户信息
```typescript
// research-container 需要显示当前用户信息
// 但又不能直接依赖 user-account-fe
```

#### 场景3：多个研究组件的状态同步
```typescript
// research-v2-fe, research-v2b-fe, research-v2h-fe
// 可能需要共享研究状态和进度信息
```

### 6.4 针对当前项目的修正方案

#### 方案A：应用层协调（推荐）

在 Next.js 应用层统一协调插件交互：

```typescript
// ✅ 修正方案 - 在应用层协调
// apps/web-app/src/app/workbench-v1a/page.tsx
'use client';

import { useState } from 'react';
import { WorkbenchV1APage } from '@yai-investor-insight/workbench-v1a-fe';
import { AITaskModal } from '@yai-investor-insight/idea-feature-v1a-fe';
import { useSession } from '@yai-investor-insight/shared-fe-core';

export default function WorkbenchRoute() {
  const [showAIModal, setShowAIModal] = useState(false);
  const [modalData, setModalData] = useState(null);
  const { user } = useSession();

  const handleNewIdea = () => {
    setShowAIModal(true);
  };

  const handleAITaskSubmit = async (taskData) => {
    // 在应用层处理跨插件的数据流
    console.log('AI task submitted:', taskData);
    
    // 可以调用其他插件的功能
    // 比如跳转到 research 页面
    window.location.href = `/research-v2?task=${taskData.id}`;
    
    setShowAIModal(false);
  };

  return (
    <>
      {/* Workbench 插件变为纯组件，通过 props 传递交互回调 */}
      <WorkbenchV1APage 
        user={user}
        onNewIdea={handleNewIdea}
        onNavigate={(route) => window.location.href = route}
      />
      
      {/* AI 模态框由应用层管理 */}
      <AITaskModal
        open={showAIModal}
        onClose={() => setShowAIModal(false)}
        onSubmit={handleAITaskSubmit}
        data={modalData}
      />
    </>
  );
}
```

### 6.6 应用层协调中的组件包含关系处理

当组件之间存在包含关系时，我们需要特别处理以下几种场景：

#### 场景1：TopBar 包含来自不同插件的按钮

**问题**：TopBar 需要显示来自不同插件的按钮，但不能直接依赖这些插件。

**解决方案：Render Props + 组件组合**

```typescript
// libs/workbench-v1a-fe/src/lib/components/TopBar.tsx
// ✅ 重构后：接收子组件作为 props
interface TopBarProps {
  searchQuery?: string;
  onSearch?: (query: string) => void;
  
  // 通过 render props 接收来自不同插件的按钮
  actionButtons?: React.ReactNode;
  
  // 或者通过函数式 render props
  renderActions?: () => React.ReactNode;
  
  // 或者通过组件数组
  rightSlot?: React.ComponentType[];
}

export function TopBar({ 
  searchQuery, 
  onSearch, 
  actionButtons,
  renderActions,
  rightSlot 
}: TopBarProps) {
  return (
    <div className="top-bar">
      <div className="search-section">
        <SearchInput value={searchQuery} onChange={onSearch} />
      </div>
      
      <div className="actions-section">
        {/* 方式1：直接渲染子组件 */}
        {actionButtons}
        
        {/* 方式2：函数式 render props */}
        {renderActions?.()}
        
        {/* 方式3：动态组件数组 */}
        {rightSlot?.map((Component, index) => (
          <Component key={index} />
        ))}
      </div>
    </div>
  );
}
```

```typescript
// apps/web-app/src/app/workbench-v1a/page.tsx
// 应用层组装不同插件的组件
import { TopBar } from '@yai-investor-insight/workbench-v1a-fe';
import { Button } from '@yai-investor-insight/shared-fe-kit';
import { UserProfileButton } from '@yai-investor-insight/user-account-fe';

export default function WorkbenchRoute() {
  const [showAIModal, setShowAIModal] = useState(false);
  
  // 组装来自不同插件的按钮
  const actionButtons = (
    <>
      <Button onClick={() => setShowAIModal(true)}>
        新 Idea
      </Button>
      <UserProfileButton onProfileClick={handleProfileClick} />
      <NotificationButton onNotificationClick={handleNotificationClick} />
    </>
  );
  
  return (
    <div className="workbench-layout">
      <TopBar 
        searchQuery={searchQuery}
        onSearch={setSearchQuery}
        actionButtons={actionButtons}
      />
      
      {/* 其他内容... */}
    </div>
  );
}
```

#### 场景2：研究页面嵌入不同插件的组件

**问题**：ResearchContainer 中的某些 Stage 需要嵌入来自其他插件的组件。

**解决方案：Slot 模式 + Higher-Order Component**

```typescript
// libs/research-v2-fe/src/lib/components/stages/FactVerificationStage.tsx
// ✅ 重构后：支持插槽嵌入
interface FactVerificationStageProps {
  // 原有的 props
  data: FactVerificationData;
  onStageComplete: () => void;
  
  // 新增：插槽支持
  slots?: {
    chartSection?: React.ReactNode;
    analysisSection?: React.ReactNode;
    actionSection?: React.ReactNode;
  };
  
  // 或者支持更灵活的插槽
  renderChart?: (data: any) => React.ReactNode;
  renderAnalysis?: (data: any) => React.ReactNode;
}

export function FactVerificationStage({
  data,
  onStageComplete,
  slots,
  renderChart,
  renderAnalysis
}: FactVerificationStageProps) {
  return (
    <div className="fact-verification-stage">
      <div className="stage-header">
        <h2>事实核实阶段</h2>
      </div>
      
      <div className="stage-content">
        {/* 默认内容 */}
        <div className="verification-summary">
          {data.summary}
        </div>
        
        {/* 图表区域 - 可嵌入外部组件 */}
        <div className="chart-section">
          {slots?.chartSection || renderChart?.(data) || (
            <DefaultChartPlaceholder />
          )}
        </div>
        
        {/* 分析区域 - 可嵌入外部组件 */}
        <div className="analysis-section">
          {slots?.analysisSection || renderAnalysis?.(data) || (
            <DefaultAnalysisView data={data} />
          )}
        </div>
        
        {/* 操作区域 */}
        <div className="action-section">
          {slots?.actionSection || (
            <Button onClick={onStageComplete}>
              继续下一阶段
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
```

```typescript
// apps/web-app/src/app/research-v2/page.tsx
// 应用层组装不同插件在研究页面中
import { ResearchContainer, FactVerificationStage } from '@yai-investor-insight/research-v2-fe';
import { ChartComponent } from '@yai-investor-insight/chart-plugin-fe';
import { AIAnalysisComponent } from '@yai-investor-insight/ai-analysis-fe';

export default function ResearchV2Route() {
  const [currentStage, setCurrentStage] = useState('fact-verification');
  const [researchData, setResearchData] = useState(null);
  
  const renderFactVerificationStage = () => {
    return (
      <FactVerificationStage
        data={researchData.factVerification}
        onStageComplete={() => setCurrentStage('impact-simulation')}
        slots={{
          chartSection: (
            <ChartComponent 
              data={researchData.chartData}
              type="line"
              interactive={true}
            />
          ),
          analysisSection: (
            <AIAnalysisComponent
              prompt="分析这些事实的可信度"
              data={researchData.facts}
              onAnalysisComplete={handleAnalysisComplete}
            />
          )
        }}
      />
    );
  };
  
  return (
    <ResearchContainer>
      {currentStage === 'fact-verification' && renderFactVerificationStage()}
      {/* 其他阶段... */}
    </ResearchContainer>
  );
}
```

#### 场景3：模态框内部组件组合

**问题**：AITaskModal 需要包含来自不同插件的组件（文件上传、图表选择等）。

**解决方案：Compound Component 模式**

```typescript
// libs/idea-feature-v1a-fe/src/lib/components/AITaskModal.tsx
// ✅ 重构后：支持组合式组件
interface AITaskModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: AITaskInput) => void;
  
  // 支持子组件组合
  children?: React.ReactNode;
  
  // 或者支持具名插槽
  slots?: {
    inputSection?: React.ReactNode;
    attachmentSection?: React.ReactNode;
    optionsSection?: React.ReactNode;
    actionsSection?: React.ReactNode;
  };
}

export function AITaskModal({ 
  open, 
  onClose, 
  onSubmit, 
  children,
  slots 
}: AITaskModalProps) {
  const [prompt, setPrompt] = useState('');
  
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>From event to edge in one search</DialogTitle>
        </DialogHeader>
        
        <div className="modal-body">
          {/* 输入区域 */}
          <div className="input-section">
            {slots?.inputSection || (
              <AIPromptInput
                value={prompt}
                onChange={setPrompt}
                placeholder="输入您想要分析的市场事件..."
              />
            )}
          </div>
          
          {/* 附件区域 */}
          <div className="attachment-section">
            {slots?.attachmentSection || (
              <DefaultAttachmentUploader />
            )}
          </div>
          
          {/* 选项区域 */}
          <div className="options-section">
            {slots?.optionsSection || (
              <DefaultOptionsPanel />
            )}
          </div>
          
          {/* 自定义内容 */}
          {children}
        </div>
        
        <DialogFooter>
          {slots?.actionsSection || (
            <>
              <Button variant="outline" onClick={onClose}>
                取消
              </Button>
              <Button onClick={() => onSubmit({ prompt })}>
                开始分析
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
```

```typescript
// apps/web-app/src/app/workbench-v1a/page.tsx
// 使用组合式模态框
import { AITaskModal } from '@yai-investor-insight/idea-feature-v1a-fe';
import { FileUploader } from '@yai-investor-insight/file-manager-fe';
import { ChartSelector } from '@yai-investor-insight/chart-plugin-fe';

export default function WorkbenchRoute() {
  const [showAIModal, setShowAIModal] = useState(false);
  const [attachments, setAttachments] = useState([]);
  
  return (
    <>
      {/* 其他内容... */}
      
      <AITaskModal
        open={showAIModal}
        onClose={() => setShowAIModal(false)}
        onSubmit={handleAITaskSubmit}
        slots={{
          attachmentSection: (
            <FileUploader
              multiple
              accept=".pdf,.doc,.csv"
              onFilesSelected={setAttachments}
              maxSize={10 * 1024 * 1024} // 10MB
            />
          ),
          optionsSection: (
            <div className="options-grid">
              <ChartSelector 
                onChartTypeSelected={handleChartTypeSelect}
              />
              <AnalysisOptionsPanel 
                onOptionsChange={handleOptionsChange}
              />
            </div>
          )
        }}
      >
        {/* 额外的自定义内容 */}
        <CustomAnalysisPreview data={previewData} />
      </AITaskModal>
    </>
  );
}
```

#### 场景4：动态组件加载和懒加载

**问题**：包含关系中的子组件可能来自大型插件，需要懒加载以优化性能。

**解决方案：React.lazy + Suspense + 错误边界**

```typescript
// apps/web-app/src/components/LazyPluginLoader.tsx
import { lazy, Suspense } from 'react';
import { ErrorBoundary } from 'react-error-boundary';

// 懒加载插件组件
const LazyChartComponent = lazy(() => 
  import('@yai-investor-insight/chart-plugin-fe').then(module => ({
    default: module.ChartComponent
  }))
);

const LazyFileUploader = lazy(() =>
  import('@yai-investor-insight/file-manager-fe').then(module => ({
    default: module.FileUploader
  }))
);

// 通用的懒加载包装器
interface LazyPluginWrapperProps {
  pluginName: string;
  componentName: string;
  fallback?: React.ReactNode;
  errorFallback?: React.ComponentType<{error: Error}>;
  children: React.ReactNode;
}

export function LazyPluginWrapper({ 
  pluginName, 
  componentName, 
  fallback, 
  errorFallback,
  children 
}: LazyPluginWrapperProps) {
  const DefaultErrorFallback = ({ error }: { error: Error }) => (
    <div className="plugin-error">
      <p>插件 {pluginName}.{componentName} 加载失败</p>
      <details>
        <summary>错误详情</summary>
        <pre>{error.message}</pre>
      </details>
    </div>
  );
  
  return (
    <ErrorBoundary 
      FallbackComponent={errorFallback || DefaultErrorFallback}
      onError={(error) => {
        console.error(`Plugin ${pluginName}.${componentName} failed:`, error);
        // 可以发送错误报告
      }}
    >
      <Suspense fallback={fallback || <PluginLoadingSkeleton />}>
        {children}
      </Suspense>
    </ErrorBoundary>
  );
}

// 使用示例
export function EnhancedResearchStage() {
  return (
    <div className="research-stage">
      <LazyPluginWrapper 
        pluginName="chart-plugin" 
        componentName="ChartComponent"
        fallback={<ChartLoadingSkeleton />}
      >
        <LazyChartComponent data={chartData} />
      </LazyPluginWrapper>
      
      <LazyPluginWrapper 
        pluginName="file-manager" 
        componentName="FileUploader"
        fallback={<div>加载文件上传组件...</div>}
      >
        <LazyFileUploader onUpload={handleFileUpload} />
      </LazyPluginWrapper>
    </div>
  );
}
```

#### 场景5：类型安全的包含关系

**问题**：如何在 TypeScript 中保证包含关系的类型安全。

**解决方案：泛型约束 + 接口定义**

```typescript
// libs/shared-fe-plugin-system/src/types/composition.ts
// 定义组件包含关系的类型系统

// 插槽类型定义
interface SlotDefinition<T = any> {
  name: string;
  required?: boolean;
  defaultComponent?: React.ComponentType<T>;
  props?: T;
}

// 容器组件类型
interface ContainerComponent<TSlots extends Record<string, SlotDefinition>> {
  slots: TSlots;
  renderSlot: <K extends keyof TSlots>(
    slotName: K, 
    props?: TSlots[K]['props']
  ) => React.ReactNode;
}

// TopBar 的插槽定义
interface TopBarSlots {
  searchInput: SlotDefinition<{ query: string; onChange: (q: string) => void }>;
  actionButtons: SlotDefinition<{ onAction: (action: string) => void }>;
  userSection: SlotDefinition<{ user: User | null }>;
}

// 类型安全的 TopBar 组件
export function TypeSafeTopBar<T extends TopBarSlots>(
  props: {
    slots?: Partial<{
      [K in keyof T]: React.ComponentType<T[K]['props']>
    }>;
    onSlotAction?: (slotName: keyof T, action: any) => void;
  }
) {
  const { slots, onSlotAction } = props;
  
  return (
    <div className="top-bar">
      <div className="search-section">
        {slots?.searchInput ? (
          <slots.searchInput 
            query="" 
            onChange={(q) => onSlotAction?.('searchInput', { query: q })}
          />
        ) : (
          <DefaultSearchInput />
        )}
      </div>
      
      <div className="actions-section">
        {slots?.actionButtons && (
          <slots.actionButtons 
            onAction={(action) => onSlotAction?.('actionButtons', action)}
          />
        )}
      </div>
      
      <div className="user-section">
        {slots?.userSection && (
          <slots.userSection user={null} />
        )}
      </div>
    </div>
  );
}
```

#### 方案B：事件总线协调

创建统一的事件系统：

```typescript
// libs/shared-fe-event-bus/src/events.ts
export interface PluginEventMap {
  // 工作台事件
  'workbench:new-idea-request': { source: string };
  'workbench:navigate': { route: string; params?: any };
  
  // AI 功能事件
  'idea:modal-show': { data?: any };
  'idea:modal-hide': void;
  'idea:task-created': { taskId: string; data: any };
  
  // 研究功能事件
  'research:start': { taskId: string };
  'research:stage-change': { stage: string; progress: number };
  
  // 用户账户事件
  'user:login': { user: User };
  'user:logout': void;
  'user:profile-update': { user: User };
}

// 使用示例
// workbench 插件中
eventBus.emit('idea:modal-show', { source: 'workbench' });

// idea-feature 插件中
eventBus.on('idea:modal-show', (data) => {
  setModalVisible(true);
});
```

#### 方案C：共享状态管理

基于 Zustand 创建插件状态管理：

```typescript
// libs/shared-fe-state/src/plugin-store.ts
interface PluginState {
  // UI 状态
  modals: {
    aiTask: { visible: boolean; data?: any };
    userProfile: { visible: boolean };
  };
  
  // 应用状态
  currentUser: User | null;
  currentRoute: string;
  
  // 插件状态
  workbench: {
    selectedTab: string;
    searchQuery: string;
  };
  
  research: {
    currentStage: string;
    progress: number;
  };
}

export const usePluginStore = create<PluginState>((set, get) => ({
  // 初始状态
  modals: {
    aiTask: { visible: false },
    userProfile: { visible: false },
  },
  
  // 操作方法
  showModal: (modalName: string, data?: any) => {
    set(state => ({
      modals: {
        ...state.modals,
        [modalName]: { visible: true, data }
      }
    }));
  },
  
  hideModal: (modalName: string) => {
    set(state => ({
      modals: {
        ...state.modals,
        [modalName]: { visible: false }
      }
    }));
  },
}));
```

### 6.5 方案对比

| 方面 | 直接依赖（当前）| 应用层协调 | 事件总线 | 共享状态 |
|------|----------------|------------|----------|----------|
| 耦合程度 | ❌ 强耦合 | ✅ 零耦合 | ✅ 松耦合 | ⚠️ 中等耦合 |
| 独立测试 | ❌ 困难 | ✅ 简单 | ✅ 简单 | ⚠️ 需要 mock |
| 动态加载 | ❌ 不支持 | ✅ 支持 | ✅ 支持 | ✅ 支持 |
| 类型安全 | ✅ 编译时检查 | ✅ 接口约束 | ⚠️ 运行时检查 | ✅ TypeScript 支持 |
| 开发体验 | ⚠️ 循环依赖风险 | ✅ 清晰架构 | ⚠️ 调试困难 | ✅ 直观状态 |
| 性能 | ✅ 最优 | ✅ 良好 | ⚠️ 事件开销 | ✅ 良好 |
| 复杂度 | ✅ 简单 | ⚠️ 中等 | ⚠️ 中等 | ⚠️ 较高 |

---

## 7. 针对当前项目的具体建议

### 7.1 立即行动（优先级：高）

#### 1. 重构 workbench-v1a-fe
```typescript
// 移除直接依赖，改为 props 回调
export function TopBar({ 
  onNewIdea,
  onSearch, 
  searchQuery 
}: {
  onNewIdea?: () => void;
  onSearch?: (query: string) => void;
  searchQuery?: string;
}) {
  return (
    <div className="top-bar">
      <SearchInput value={searchQuery} onChange={onSearch} />
      <Button onClick={onNewIdea}>新 Idea</Button>
    </div>
  );
}
```

#### 2. 统一设计系统
```typescript
// 整合 shared-fe-kit，避免 UI 组件重复
// 将各插件的 ui/ 目录组件迁移到 shared-fe-kit
import { Button, Dialog, Card } from '@yai-investor-insight/shared-fe-kit';
```

#### 3. 创建应用协调层
```typescript
// apps/web-app/src/components/PluginCoordinator.tsx
export function PluginCoordinator() {
  // 统一管理所有插件的交互
}
```

### 7.2 中期优化（优先级：中）

#### 1. 建立事件系统
```typescript
// 为复杂的数据流场景引入事件总线
// 特别是研究流程的状态同步
```

#### 2. 类型定义统一
```typescript
// 将重复的类型定义移到 shared-types
// 建立插件接口标准
```

### 7.3 长期规划（优先级：低）

#### 1. 插件注册系统
```typescript
// 支持动态插件加载
// 插件依赖管理
```

#### 2. 微前端演进
```typescript
// 如果项目规模继续扩大
// 考虑微前端架构
```

## 8. 总结

根据当前项目的规模和需求，建议采用**应用层协调为主，事件总线为辅**的混合方案：

1. **组件嵌入和模态框**：使用应用层协调
2. **数据流和生命周期**：使用事件总线
3. **保持插件零依赖**：所有插件都是纯组件
4. **逐步演进**：根据项目发展调整架构复杂度

这样既能保证代码的清晰度和可维护性，又能为未来的扩展留下空间。

### 8.1 实施路线图

| 阶段 | 时间 | 主要任务 | 预期效果 |
|------|------|----------|----------|
| **第1周** | 立即 | 重构直接依赖，创建应用协调层 | 解除插件耦合 |
| **第2-3周** | 短期 | 统一设计系统，建立事件总线 | 提升开发效率 |
| **第4-8周** | 中期 | 完善插件接口，优化性能 | 架构稳定 |
| **第9周+** | 长期 | 插件注册系统，微前端探索 | 支持大规模扩展 |

### 8.2 包含关系的最佳实践

1. **选择合适的模式**：
   - 简单包含：使用 Render Props
   - 复杂组合：使用 Slot 模式
   - 动态加载：使用 React.lazy + Suspense

2. **类型安全**：
   - 定义清晰的接口约束
   - 使用 TypeScript 泛型约束
   - 提供默认实现

3. **性能优化**：
   - 懒加载非关键组件
   - 使用 React.memo 避免不必要的重渲染
   - 合理设置加载边界

4. **错误处理**：
   - 为每个包含的组件设置错误边界
   - 提供降级方案
   - 记录和监控错误

### 8.3 下一步行动

1. **立即重构**：修正当前的直接依赖问题
2. **建立规范**：制定插件开发标准和接口约定
3. **实现包含关系处理**：按照上述方案重构包含关系
4. **逐步演进**：根据功能需求逐步引入更复杂的交互方案
5. **持续优化**：监控性能和开发体验，适时调整架构

### 8.3 成功指标

- ✅ 插件间零依赖
- ✅ 单元测试覆盖率 > 80%
- ✅ 构建时间 < 30s
- ✅ 热重载时间 < 3s
- ✅ 插件开发文档完善
- ✅ 新插件接入时间 < 1天