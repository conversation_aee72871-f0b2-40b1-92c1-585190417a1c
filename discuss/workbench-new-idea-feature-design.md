# Workbench V1A「新 Idea」AI 任务入口设计方案

## 1. 功能概述

「新 Idea」按钮是 Workbench V1A 的核心 AI 任务入口，类似 Cursor、Claude、Lovable 等 AI 工具。用户通过自然语言描述投资想法或市场事件，AI 自动生成完整的投资分析报告。

## 2. 产品定位

### 2.1 核心理念
**"From event to edge in one search"** - 从事件到投资优势，一次搜索完成

基于 AI 驱动的事件分析，快速验证事实、预测影响、构建投资逻辑

### 2.2 典型用户流程
1. 用户看到市场事件或产生投资想法
2. 点击「新 Idea」按钮，打开 AI 任务界面
3. 用自然语言描述想法："分析苹果发布 Vision Pro 对供应链的影响"
4. AI 自动分析并生成完整的投资研究报告
5. 用户可以继续对话，深入探讨细节

### 2.3 用户需求
- **自然交互**：用自然语言描述想法，无需填表
- **智能分析**：AI 自动理解意图并生成专业分析
- **实时对话**：支持多轮对话，深入探讨
- **即时成果**：快速获得可操作的投资洞察

## 3. AI 任务界面设计

### 3.1 界面布局（模态框方案）

```
┌─────────────────────────────────────────────────────────────┐
│  From event to edge in one search                           │
│  基于AI驱动的事件分析，快速验证事实、预测影响、构建投资逻辑    │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ 🔍 输入您想要分析的市场事件或投资信息...              │   │
│  │                                                     │   │
│  │                                                     │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  💡 深度核实    📎 附件                    [开始分析]        │
│                                                             │
│  ─────────────────────────────────────────────────────────  │
│                                                             │
│  💡 示例提示：                                              │
│  • "分析苹果发布 Vision Pro 对供应链公司的影响"             │
│  • "英伟达财报超预期，相关概念股投资机会分析"               │
│  • "美联储加息对房地产板块的影响及投资策略"                 │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 交互流程
```
点击「新 Idea」→ 弹出 AI 任务界面 → 输入自然语言描述 →
AI 开始分析 → 实时显示分析过程 → 生成完整报告 →
保存为新 Idea → 支持继续对话深入
```

### 3.3 核心功能
- **自然语言输入**：大文本框，支持多行输入
- **智能提示**：提供示例 prompts 帮助用户开始
- **附件上传**：支持上传相关文档、图片等
- **深度核实**：AI 自动验证事实和数据
- **实时分析**：显示 AI 思考和分析过程

## 4. AI 任务数据结构

### 4.1 AI 任务输入
```typescript
interface AITaskInput {
  prompt: string;                    // 用户输入的自然语言描述
  attachments?: File[];              // 上传的附件
  enableFactCheck?: boolean;         // 是否启用深度核实
  context?: {                        // 上下文信息
    currentIdeas?: Idea[];           // 当前已有的 Ideas
    userPreferences?: UserProfile;   // 用户偏好
  };
}
```

### 4.2 AI 分析结果
```typescript
interface AIAnalysisResult {
  id: string;
  title: string;                     // AI 生成的标题
  summary: string;                   // 执行摘要
  analysis: {
    eventDescription: string;        // 事件描述
    marketImpact: string;           // 市场影响分析
    investmentThesis: string;       // 投资逻辑
    riskAssessment: string;         // 风险评估
    actionableInsights: string[];   // 可操作洞察
  };
  relatedStocks: StockInfo[];       // 相关股票
  confidence: number;               // AI 置信度 (0-1)
  sources: string[];                // 数据来源
  createdAt: string;
  conversationId: string;           // 对话 ID，支持继续对话
}
```

### 4.3 生成的 Idea 对象
```typescript
interface GeneratedIdea {
  id: number;
  title: string;                    // 来自 AI 分析结果
  description: string;              // AI 生成的描述
  aiAnalysis: AIAnalysisResult;     // 完整的 AI 分析
  status: 'ai_generated' | 'user_modified' | 'completed';
  unreadEvents: number;
  lastUpdated: string;
  conversationId: string;           // 支持继续 AI 对话
}
```

## 5. 技术实现方案

### 5.1 前端组件结构
```
AITaskModal/
├── AITaskModal.tsx           // 主模态框组件
├── AIPromptInput.tsx         // 自然语言输入组件
├── AIAnalysisStream.tsx      // 实时分析流显示
├── AttachmentUpload.tsx      // 附件上传组件
├── ExamplePrompts.tsx        // 示例提示组件
├── AIConversation.tsx        // AI 对话组件
└── AnalysisResult.tsx        // 分析结果展示
```

### 5.2 状态管理
- 使用 Zustand 管理 AI 任务状态和对话历史
- 使用 React Query 管理 AI API 调用和缓存
- 使用 WebSocket 或 SSE 实现实时流式响应

### 5.3 AI 核心功能
- **自然语言理解**：解析用户意图和关键信息
- **事件分析引擎**：分析市场事件的影响和机会
- **投资逻辑构建**：自动生成投资论证框架
- **风险评估**：识别和量化投资风险
- **实时数据集成**：获取最新市场数据和新闻
- **多轮对话**：支持深入探讨和细化分析

## 6. 用户体验设计

### 6.1 交互体验原则
- **即时响应**：输入时立即显示 AI 思考状态
- **流式体验**：分析结果逐步流式显示，不等待完整结果
- **智能提示**：根据输入内容智能推荐相关问题
- **上下文保持**：记住对话历史，支持连续深入

### 6.2 视觉设计
- **简洁界面**：突出输入框，减少干扰元素
- **进度指示**：清晰显示 AI 分析进度和状态
- **结果层次**：分析结果按重要性分层展示
- **品牌一致**：与 workbench 整体设计风格保持一致

### 6.3 性能优化
- 模态框懒加载，点击时才初始化 AI 服务
- 使用流式响应，减少用户等待时间
- 智能缓存常见分析结果
- 离线模式支持基础功能

## 7. 实现优先级

### Phase 1：基础 AI 任务界面（MVP）
- [ ] AI 任务模态框基础结构
- [ ] 自然语言输入组件
- [ ] 示例提示展示
- [ ] 基础 AI API 集成
- [ ] 简单分析结果展示

### Phase 2：流式体验
- [ ] 实时流式响应显示
- [ ] AI 分析进度指示
- [ ] 结果逐步构建展示
- [ ] 错误处理和重试机制

### Phase 3：高级 AI 功能
- [ ] 深度事实核实
- [ ] 附件上传和分析
- [ ] 多轮对话支持
- [ ] 智能追问建议

### Phase 4：企业级功能
- [ ] 分析结果导出
- [ ] 协作和分享
- [ ] 自定义 AI 模型
- [ ] 批量分析任务

## 8. 技术风险和挑战

### 8.1 主要风险
- **AI 服务稳定性**：依赖外部 AI 服务，需要考虑服务中断
- **响应时间**：复杂分析可能需要较长时间，影响用户体验
- **成本控制**：AI API 调用成本需要合理控制
- **数据安全**：用户输入的敏感信息需要保护

### 8.2 解决方案
- 多 AI 服务商备份，实现服务降级
- 流式响应 + 进度指示，改善等待体验
- 智能缓存 + 用量监控，控制成本
- 数据加密 + 隐私保护机制

## 9. 示例用户场景

### 场景 1：突发事件分析
**用户输入**：
> "刚看到新闻说特斯拉在中国建新工厂，这对相关供应链公司有什么影响？"

**AI 分析输出**：
- 事件核实和背景分析
- 供应链影响评估
- 相关受益公司识别
- 投资时机和风险分析

### 场景 2：财报季机会挖掘
**用户输入**：
> "英伟达财报超预期，除了英伟达本身，还有哪些相关投资机会？"

**AI 分析输出**：
- 财报关键数据解读
- 产业链上下游影响分析
- 概念股投资机会梳理
- 风险收益比评估

## 10. 下一步行动

1. **确认技术方案**：选择 AI 任务模态框方案
2. **设计 UI 原型**：基于线框图设计具体界面
3. **搭建基础组件**：实现 AITaskModal 基础结构
4. **集成 AI 服务**：连接后端 AI 分析能力
5. **用户测试**：验证交互流程和分析质量

## 11. 关键讨论点

1. **AI 模型选择**：使用哪个 AI 模型？GPT-4、Claude 还是自训练模型？
2. **分析深度**：初版应该提供多深入的分析？
3. **实时数据**：是否需要集成实时股价、新闻等数据？
4. **多语言支持**：是否需要支持英文输入和分析？
5. **移动端适配**：模态框在移动端的交互体验如何优化？

这个方案将「新 Idea」转变为一个强大的 AI 投资分析入口，用户只需要用自然语言描述想法，就能获得专业的投资分析报告。
