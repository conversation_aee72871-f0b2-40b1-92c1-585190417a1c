// 主要组件导出
export { UserAccountFeature } from './lib/user-account-fe';
export { UserProfile, UserProfileSimple } from './lib/components/user-profile';
export { LoginForm } from './lib/components/login-form';
export { RegisterForm } from './lib/components/register-form';
export { AccountSettings } from './lib/components/account-settings';
export { SimpleLoginModal } from './lib/components/simple-login-modal';
export { CoagentLoginExample } from './lib/components/coagent-login-example';
export type { CoagentLoginExampleProps } from './lib/components/coagent-login-example';

// Server Actions 导出
export * from './lib/actions';
export {
  loginWithMobile,
  sendOtpCode,
  getUserProfile,
  getCurrentUser,
  logout,
  isAuthenticated
} from './lib/actions/userAccountActions';

// Hooks 导出 - 客户端Session状态管理
export * from './lib/hooks';

// 重新导出 Session 相关类型
export type { SessionData, SessionResult } from '@yai-investor-insight/shared-fe-core';

// 适配器导出
export type { AuthAdapter, AuthResult } from './lib/adapters/authAdapter';

// Pages 导出
export { UserAccountPage } from './lib/pages/user-account-page';
export { LoginPage } from './lib/pages/login-page';
export { RegisterPage } from './lib/pages/register-page';

// 类型导出
export type { UserProfileProps } from './lib/components/user-profile';
export type { 
  UserData, 
  SendOtpCodeRequest, 
  MobileLoginRequest, 
  LoginResult, 
  LoginCredentials, 
  RegisterData, 
  UpdateProfileData, 
  ChangePasswordData 
} from './lib/types/userAccountTypes';

// API Client 导出 - 注意：这些只应该在服务端使用
// 如果需要在服务端使用，请直接从 './lib/api-client' 导入
// export { authWithMobile, type AuthMobileArgVO, type AuthResVO } from './lib/api-client';
// export * from './lib/api-client/types';

// 样式导入
import './lib/user-account-fe.module.css';