import { createCoagent } from '@yai-investor-insight/shared-fe-core/src/lib/coagent';
import { axios } from '@yai-investor-insight/shared-fe-core';

// 定义公共接口的 URL 列表
const PUBLIC_URLS = [
  '/account/login/authWithMobile',
  '/account/login/sendOtpCode',
  // 其他需要公开访问的 URL
];

// coagent 运行在服务端，可以直接访问 K8s 内部服务
const ACCOUNT_SERVICE_URL = 'http://yai-account-service-java:8080';

// 创建 axios 实例
const axiosInstance = axios.create({
  baseURL: ACCOUNT_SERVICE_URL,
  timeout: 30000,
});

// 创建 Coagent 实例
const coagent = createCoagent(axiosInstance);

/**
 * 判断是否为公开接口
 */
function isPublicUrl(url: string): boolean {
  return PUBLIC_URLS.some(publicUrl => url.includes(publicUrl));
}

export const customInstance = async <T>(
  url: string,
  options: RequestInit = {},
): Promise<T> => {
  const method = (options.method || 'GET') as 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  const isPublic = isPublicUrl(url);

  try {
    // 解析请求数据
    let data: any = undefined;
    if (options.body) {
      try {
        data = JSON.parse(options.body as string);
      } catch {
        data = options.body;
      }
    }

    // 使用新的简化 API
    const response = await coagent.request({
      method,
      url,
      data,
      isPublic,
    });

    // 检查是否有错误
    if (response.error) {
      throw new Error(response.error);
    }

    // 构造符合 TypeScript 类型定义的响应对象
    return {
      data: response.data,
      status: response.status || 200,
      headers: new Headers()
    } as T;

  } catch (error) {
    console.error(`Coagent request failed for URL: ${url}`, error);
    throw error;
  }
};

// 创建自定义实例的工厂函数
export function createCustomInstance(config: { baseURL: string }) {
  // 为这个配置创建专用的 axios 实例
  const customAxiosInstance = axios.create({
    baseURL: config.baseURL,
    timeout: 30000,
  });

  // 创建专用的 Coagent 实例
  const customCoagent = createCoagent(customAxiosInstance);

  return {
    get: async <T>(url: string, options?: RequestInit): Promise<{ data: T }> => {
      console.log('createCustomInstance.get called with:', { url, config });

      const fullUrl = `${config.baseURL}${url}`;
      const response = await customCoagent.request<T>({
        method: 'GET',
        url,
        isPublic: isPublicUrl(fullUrl),
      });

      if (response.error) {
        throw new Error(response.error);
      }

      return { data: response.data };
    },

    post: async <T>(url: string, data?: any, options?: RequestInit): Promise<{ data: T }> => {
      const fullUrl = `${config.baseURL}${url}`;
      const response = await customCoagent.request<T>({
        method: 'POST',
        url,
        data,
        isPublic: isPublicUrl(fullUrl),
      });

      if (response.error) {
        throw new Error(response.error);
      }

      return { data: response.data };
    },

    put: async <T>(url: string, data?: any, options?: RequestInit): Promise<{ data: T }> => {
      const fullUrl = `${config.baseURL}${url}`;
      const response = await customCoagent.request<T>({
        method: 'PUT',
        url,
        data,
        isPublic: isPublicUrl(fullUrl),
      });

      if (response.error) {
        throw new Error(response.error);
      }

      return { data: response.data };
    },

    delete: async <T>(url: string, options?: RequestInit): Promise<{ data: T }> => {
      const fullUrl = `${config.baseURL}${url}`;
      const response = await customCoagent.request<T>({
        method: 'DELETE',
        url,
        isPublic: isPublicUrl(fullUrl),
      });

      if (response.error) {
        throw new Error(response.error);
      }

      return { data: response.data };
    }
  };
}

export default customInstance;