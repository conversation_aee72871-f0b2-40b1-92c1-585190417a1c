'use client';

import React, { useEffect, useState } from 'react';
import { getUserProfile, UserData } from '../actions/userAccountActions';

export interface UserProfileProps {
  variant?: 'default' | 'simple' | 'detailed';
  className?: string;
  onLogout?: () => void;
}

export const UserProfile: React.FC<UserProfileProps> = ({
  variant = 'default',
  className = '',
  onLogout,
}) => {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setLoading(true);
        const profile = await getUserProfile();
        setUserData(profile);
      } catch (err) {
        setError('Error fetching user data');
        console.error('Error fetching user data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  if (error || !userData?.user_info_vo) {
    return (
      <div className={`text-red-500 ${className}`}>
        <p>{error || 'No user data available'}</p>
      </div>
    );
  }

  const userInfo = userData.user_info_vo;
  const userCredit = userData.user_credit;

  const renderSimpleVariant = () => (
    <div className={`flex items-center space-x-3 ${className}`}>
      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
        {userInfo.username?.charAt(0).toUpperCase() || 'U'}
      </div>
      <div>
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium text-gray-900">{userInfo.username || 'Unknown User'}</p>
          {userCredit?.credit !== undefined && (
            <span className="text-xs text-blue-600">({userCredit.credit} 积分)</span>
          )}
        </div>
        <p className="text-xs text-gray-500">{userInfo.email || userInfo.mobile}</p>
      </div>
    </div>
  );

  const renderDetailedVariant = () => (
    <div className={`bg-white rounded-lg shadow-sm border p-6 ${className}`}>
      {/* 头像和用户信息 */}
      <div className="flex flex-col items-center mb-6">
        <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-2xl font-bold shadow-lg mb-4">
          {userInfo.username?.charAt(0).toUpperCase() || 'U'}
        </div>
        <div className="text-center">
          <div className="flex items-center justify-center space-x-3 mb-2">
            <h3 className="text-xl font-semibold text-gray-900">{userInfo.username || 'Unknown User'}</h3>
            
            {/* 积分信息显示在用户名后面 */}
            {userCredit && (
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                <span className="text-base font-medium text-gray-700">
                  {userCredit.credit !== undefined ? userCredit.credit : 0} 积分
                </span>
                {userCredit.expired_at ? (
                  <span className="text-sm text-gray-500">
                    (过期: {new Date(userCredit.expired_at).toLocaleDateString()})
                  </span>
                ) : (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    永久
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* 基本信息 */}
      <div className="mb-6">
        <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          基本信息
        </h4>
        <div className="grid grid-cols-1 gap-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-3 mb-3">
              <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span className="text-sm font-medium text-gray-700">姓名</span>
            </div>
            <p className="text-base text-gray-900 ml-8">{userInfo.username || '未设置'}</p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-3 mb-3">
              <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              <span className="text-sm font-medium text-gray-700">手机号</span>
            </div>
            <p className="text-base text-gray-900 ml-8">{userInfo.mobile || '未设置'}</p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-3 mb-3">
              <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <span className="text-sm font-medium text-gray-700">邮箱</span>
            </div>
            <p className="text-base text-gray-900 ml-8">{userInfo.email || '未设置'}</p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-3 mb-3">
              <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              <span className="text-sm font-medium text-gray-700">性别</span>
            </div>
            <p className="text-base text-gray-900 ml-8">{userInfo.gender_key || '未知'}</p>
          </div>
        </div>
      </div>
      
      {onLogout && (
        <div className="mt-6 pt-4 border-t">
          <button
            onClick={onLogout}
            className="w-full bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700 font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            <span>退出登录</span>
          </button>
        </div>
      )}
    </div>
  );

  const renderDefaultVariant = () => (
    <div className={`bg-gray-50 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
            {userInfo.username?.charAt(0).toUpperCase() || 'U'}
          </div>
          <div>
            <div className="flex items-center space-x-2">
              <p className="font-medium text-gray-900">{userInfo.username || 'Unknown User'}</p>
              {userCredit?.credit !== undefined && (
                <span className="text-xs text-blue-600">({userCredit.credit} 积分)</span>
              )}
            </div>
            <p className="text-sm text-gray-500">{userInfo.email || userInfo.mobile}</p>
          </div>
        </div>
        {onLogout && (
          <button
            onClick={onLogout}
            className="ml-4 text-sm text-gray-600 hover:text-gray-800 transition-colors"
          >
            退出
          </button>
        )}
      </div>
    </div>
  );

  if (variant === 'simple') {
    return renderSimpleVariant();
  }

  if (variant === 'detailed') {
    return renderDetailedVariant();
  }

  return renderDefaultVariant();
};

/**
 * 简化版用户信息组件 - 仅显示用户名和登出按钮
 * @deprecated 使用 UserProfile 组件的 variant="simple" 属性替代
 */
export function UserProfileSimple(props: Omit<UserProfileProps, 'variant'>) {
  return <UserProfile {...props} variant="simple" />;
}