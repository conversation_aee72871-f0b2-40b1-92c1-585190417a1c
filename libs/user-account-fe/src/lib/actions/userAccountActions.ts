"use server";

// 用户账户相关的 Server Actions
import { serverSession } from '@yai-investor-insight/shared-fe-core/src/lib/session/server';
import { logger } from '@yai-investor-insight/shared-fe-core/src/lib/log/server';
import type { SendOtpCodeArgVO, AuthMobileArgVO, AuthResVO, getUserInfoResponseComposite } from '../api-client';
import { sendOtpCode as sendOtpCodeApi, authWithMobile, getUserInfo } from '../api-client'
import type {
  UserData,
  SendOtpCodeRequest,
  MobileLoginRequest,
  LoginResult,
  LoginCredentials,
  RegisterData,
  UpdateProfileData,
  ChangePasswordData
} from '../types/userAccountTypes';

export type { 
  UserData, 
  SendOtpCodeRequest, 
  MobileLoginRequest, 
  LoginResult, 
  LoginCredentials, 
  RegisterData, 
  UpdateProfileData, 
  ChangePasswordData 
} from '../types/userAccountTypes';

// ==================== 常量定义 ====================

/** 产品线标识 */
const PRODUCT_LINE = 'investor_insight' as const;

// ==================== 会话管理接口 ====================
// 注意：Session相关类型和功能现在统一从 shared-fe-core 导入



// ==================== 工具函数 ====================

/**
 * 处理API错误，返回用户友好的错误信息
 */
function handleApiError(error: any, defaultMessage: string): never {
  logger.error('用户账户API错误', {
    component: 'userAccountActions',
    error: error.message || 'Unknown error',
    stack: error.stack,
    status: error.response?.status,
    defaultMessage
  });
  
  if (error.response?.status === 404) {
    throw new Error('服务端点不存在，请检查服务配置');
  }
  
  if (error.response?.status === 401) {
    throw new Error('验证码错误或已过期，请重新获取');
  }
  
  if (error.response?.status === 403) {
    throw new Error('服务访问被拒绝，请稍后重试');
  }
  
  if (error.code === 'ECONNREFUSED') {
    throw new Error('无法连接到服务，请检查网络连接');
  }
  
  if (error.response?.data?.message) {
    throw new Error(error.response.data.message);
  }
  
  if (error.message) {
    throw new Error(error.message);
  }
  
  throw new Error(defaultMessage);
}

/**
 * 转换API用户数据为统一格式
 */
function transformUserData(authData: AuthResVO, mobile?: string): UserData {
  return {
    user_info_vo: {
      id: authData.user?.id,
      username: authData.user?.name || mobile || '未知用户',
      mobile: mobile || authData.user?.mobile || '',
      email: undefined,
      school: undefined,
      exam_type: undefined,
      sort: 0,
      gender_key: undefined,
      exam_no: undefined,
      province_key: '',
      exam_subjects: '',
      total_score: 0,
      chinese_score: 0,
      math_score: 0,
      english_score: 0,
      created_at: authData.user?.created_at,
      updated_at: authData.user?.updated_at
    },
    init_data: 0,
    user_credit: undefined
  };
}

/**
 * 转换用户信息数据为 UserData 格式
 */
function transformUserInfoData(userInfoData: any): UserData {
  return {
    user_info_vo: userInfoData.user_info_vo,
    init_data: userInfoData.init_data,
    user_credit: userInfoData.user_credit,
  };
}

/**
 * 将 getUserInfoResponseComposite 转换为 UserData
 * 处理成功响应(200)和错误响应(500)两种情况
 * 
 * @param response - API响应，可能是成功(200)或错误(500)状态
 * @returns UserData - 统一格式的用户数据
 * 
 * @example
 * ```typescript
 * import { getUserInfo } from '../api-client';
 * import { transformGetUserInfoResponse } from './userAccountActions';
 * 
 * const response = await getUserInfo();
 * const userData = await transformGetUserInfoResponse(response);
 * 
 * // 根据用户等级，response可能包含不同的数据结构
 * // 函数会自动处理并返回统一的UserData格式
 * ```
 */
export async function transformGetUserInfoResponse(response: getUserInfoResponseComposite): Promise<UserData> {
  // 检查响应状态和数据是否存在
  if (response.status === 200 && response.data && response.data.data) {
    // 成功响应，包含完整的用户信息
    const userInfoResVO = response.data.data;
    if (userInfoResVO) {
      return {
        user_info_vo: userInfoResVO.user_info_vo,
        init_data: userInfoResVO.init_data,
        user_credit: userInfoResVO.user_credit,
      };
    }
  }
  
  // 错误响应或数据为空时，返回默认的空用户数据
  return {
    user_info_vo: undefined,
    init_data: 0,
    user_credit: undefined,
  };
}

// ==================== 主要功能函数 ====================

/**
 * 发送手机验证码
 * @param mobile 手机号
 */
export async function sendOtpCode(mobile: string): Promise<void> {
  try {
    await logger.info('发送手机验证码', {
      component: 'userAccountActions',
      action: 'sendOtpCode',
      mobile: mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') // 脱敏处理
    });
    
    const payload: SendOtpCodeArgVO = { mobile };
    await sendOtpCodeApi(payload);
    
    await logger.info('手机验证码发送成功', {
      component: 'userAccountActions',
      action: 'sendOtpCode',
      mobile: mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'),
      status: 'success'
    });
  } catch (error: any) {
    await logger.error('发送手机验证码失败', {
      component: 'userAccountActions',
      action: 'sendOtpCode',
      mobile: mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'),
      error: error.message || 'Unknown error',
      status: 'failed'
    });
    handleApiError(error, '发送验证码失败，请稍后重试');
  }
}

/**
 * 手机号验证码登录 - 使用shared-fe-core的核心session功能
 * @param mobile 手机号
 * @param code 验证码
 * @returns 登录结果包含用户数据和token
 */
export async function loginWithMobile(mobile: string, code: string): Promise<LoginResult> {
  try {
    await logger.info('开始手机号验证码登录', {
      component: 'userAccountActions',
      action: 'loginWithMobile',
      mobile: mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'),
      productLine: PRODUCT_LINE
    });

    const payload: AuthMobileArgVO = {
      mobile,
      code,
      productLine: PRODUCT_LINE
    };

    const response = await authWithMobile(payload);

    await logger.info('登录API响应', {
      component: 'userAccountActions',
      action: 'loginWithMobile',
      responseStatus: response.status,
      businessCode: response.data.code,
      mobile: mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    });

    // 检查API响应，判断业务状态码
    if (response.data.code === '0') {
      const authData = response.data.data as AuthResVO;

      if (!authData.token) {
        throw new Error('登录失败，未获取到token');
      }

      const userData = transformUserData(authData, mobile);

      await serverSession.saveLogin({
        userId: authData.user?.id?.toString() || `user_${Date.now()}`,
        phone: mobile,
        userName: authData.user?.name || '',
        token: authData.token || ''
      });

      await logger.info('手机号验证码登录成功', {
        component: 'userAccountActions',
        action: 'loginWithMobile',
        userId: authData.user?.id?.toString(),
        userName: authData.user?.name,
        mobile: mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
      });

      return {
        userData,
        token: authData.token
      };
    } else {
      await logger.warn('手机号验证码登录失败', {
        component: 'userAccountActions',
        action: 'loginWithMobile',
        businessCode: response.data.code,
        message: response.data?.message,
        mobile: mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
      });
      throw new Error(response.data?.message || '登录失败，请检查手机号和验证码');
    }
  } catch (error: any) {
    await logger.error('手机号验证码登录异常', {
      component: 'userAccountActions',
      action: 'loginWithMobile',
      mobile: mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'),
      error: error.message || 'Unknown error',
      stack: error.stack
    });

    // 处理不同类型的错误并返回适当的错误信息
    let errorMessage = '登录失败，请检查手机号和验证码';

    if (error.response?.status === 404) {
      errorMessage = '服务端点不存在，请检查服务配置';
    } else if (error.response?.status === 401) {
      errorMessage = '验证码错误或已过期，请重新获取';
    } else if (error.response?.status === 403) {
      errorMessage = '服务访问被拒绝，请稍后重试';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = '无法连接到服务，请检查网络连接';
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    throw new Error(errorMessage);
  }
}

/**
 * 获取当前用户信息 - 使用shared-fe-core的核心session功能
 */
export async function getCurrentUser() {
  try {
    await logger.debug('获取当前用户信息', {
      component: 'userAccountActions',
      action: 'getCurrentUser'
    });
    
    const result = await serverSession.getCurrentUser();
    
    await logger.debug('当前用户信息获取结果', {
      component: 'userAccountActions',
      action: 'getCurrentUser',
      isLoggedIn: result.isLoggedIn,
      userId: result.isLoggedIn ? result.userId : null
    });
    
    return result;
  } catch (error: any) {
    await logger.error('获取当前用户信息失败', {
      component: 'userAccountActions',
      action: 'getCurrentUser',
      error: error.message || 'Unknown error'
    });
    return { isLoggedIn: false };
  }
}

/**
 * 获取用户资料
 * @returns 完整的用户信息数据
 * 支持处理不同用户等级的响应数据
 */
export async function getUserProfile(): Promise<UserData> {
  try {
    await logger.info('获取用户详细信息', {
      component: 'userAccountActions',
      action: 'getUserProfile'
    });
    
    // 先检查会话状态
    const currentSession = await getCurrentUser();
    if (!currentSession.isLoggedIn) {
      throw new Error('用户未登录，请先登录');
    }
    
    const response = await getUserInfo();

    // 使用新的转换函数处理响应
    const userData = await transformGetUserInfoResponse(response);
    
    if (response.status === 200) {
      console.log('✅ User profile fetched successfully for:', userData.user_info_vo?.username);
    } else {
      console.warn('⚠️ 获取用户信息返回错误状态:', response.status, response.data.message);
    }
    
    return userData;
  } catch (error: any) {
    console.error('Get user profile error:', error);
    handleApiError(error, '获取用户信息失败');
  }
}

/**
 * 用户登出 - 使用shared-fe-core的核心session功能
 */
export async function logout(): Promise<LoginResult> {
  try {
    await serverSession.clearLogin();
    return {
      userData: { 
        user_info_vo: {
          id: undefined,
          username: '',
          mobile: '',
          email: undefined,
          school: undefined,
          exam_type: undefined,
          sort: 0,
          gender_key: undefined,
          exam_no: undefined,
          province_key: '',
          exam_subjects: '',
          total_score: 0,
          chinese_score: 0,
          math_score: 0,
          english_score: 0,
          created_at: undefined,
          updated_at: undefined
        },
        init_data: 0,
        user_credit: undefined
      },
      token: ''
    };
  } catch (error) {
    console.error('Logout error:', error);
    handleApiError(error, '退出登录失败');
  }
}

/**
 * 检查用户是否已登录 - 使用shared-fe-core的核心session功能
 */
export async function isAuthenticated(): Promise<boolean> {
  try {
    return await serverSession.isLoggedIn();
  } catch (error) {
    console.error('Failed to check authentication:', error);
    return false;
  }
}

/**
 * 更新用户资料
 * @param data 更新的用户资料数据
 * @returns 更新后的用户数据
 */
export async function updateUserProfile(data: UpdateProfileData): Promise<UserData> {
  try {
    console.log('🔄 Updating user profile...');
    
    // 先检查会话状态
    const currentSession = await getCurrentUser();
    if (!currentSession.isLoggedIn) {
      throw new Error('用户未登录，请先登录');
    }
    
    // TODO: 实现用户信息更新功能
    throw new Error('用户信息更新功能暂未实现');
  } catch (error: any) {
    console.error('Update user profile error:', error);
    handleApiError(error, '更新用户信息失败');
  }
}

// ==================== 暂未实现的功能 ====================

/**
 * 用户登录（邮箱密码方式 - 暂未支持）
 */
export async function loginUser(credentials: LoginCredentials): Promise<UserData> {
  throw new Error('请使用手机号验证码登录');
}

/**
 * 用户注册（暂未开放）
 */
export async function registerUser(data: RegisterData): Promise<UserData> {
  throw new Error('注册功能暂未开放，请联系管理员');
}

/**
 * 修改密码（暂不支持）
 */
export async function changePassword(data: ChangePasswordData): Promise<void> {
  throw new Error('当前登录方式不支持密码修改');
}