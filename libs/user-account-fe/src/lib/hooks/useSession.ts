/**
 * 客户端Session管理Hook
 * 统一的session状态管理，适用于所有前端应用
 */

'use client';

import { useState, useEffect } from 'react';
import { getCurrentUser, logout as logoutAction } from '../actions/userAccountActions';
import { SessionResult } from '@yai-investor-insight/shared-fe-core';

export interface UseSessionResult {
  session: SessionResult | null;
  isLoading: boolean;
  isLoggedIn: boolean;
  refetch: () => Promise<void>;
  logout: () => Promise<void>;
}

/**
 * 客户端Session状态管理Hook
 * 提供session状态、loading状态和操作方法
 *
 * @example
 * ```tsx
 * function MyComponent() {
 *   const { session, isLoading, isLoggedIn, logout } = useSession();
 *
 *   if (isLoading) return <div>Loading...</div>;
 *
 *   return (
 *     <div>
 *       {isLoggedIn ? (
 *         <div>
 *           <p>Welcome, {session?.userName}</p>
 *           <button onClick={logout}>Logout</button>
 *         </div>
 *       ) : (
 *         <p>Please login</p>
 *       )}
 *     </div>
 *   );
 * }
 * ```
 */
export function useSession(): UseSessionResult {
  const [session, setSession] = useState<SessionResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchSession = async () => {
    try {
      setIsLoading(true);
      const sessionData = await getCurrentUser();
      setSession(sessionData);
    } catch (error) {
      console.error('Failed to fetch session:', error);
      setSession({ isLoggedIn: false });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      await logoutAction();
      setSession({ isLoggedIn: false });
    } catch (error) {
      console.error('Failed to logout:', error);
      // 即使logout失败，也要清除客户端状态
      setSession({ isLoggedIn: false });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSession();
  }, []);

  return {
    session,
    isLoading,
    isLoggedIn: session?.isLoggedIn ?? false,
    refetch: fetchSession,
    logout: handleLogout,
  };
}