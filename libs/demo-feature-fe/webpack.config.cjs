const path = require('path');

module.exports = {
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  
  entry: path.resolve(__dirname, 'src/index.ts'),
  
  output: {
    path: path.resolve(__dirname, '../../dist/libs/demo-feature-fe'),
    filename: 'index.js',
    library: {
      type: 'module'
    },
    clean: true
  },
  
  experiments: {
    outputModule: true
  },
  
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: [
          {
            loader: 'ts-loader',
            options: {
              configFile: path.resolve(__dirname, 'tsconfig.lib.json'),
              transpileOnly: true
            }
          }
        ],
        exclude: /node_modules/
      },
      {
        test: /\.css$/,
        use: [
          'style-loader',
          'css-loader'
        ]
      }
    ]
  },
  
  resolve: {
    extensions: ['.tsx', '.ts', '.js', '.jsx'],
    fallback: {
      "path": false,
      "http": false,
      "https": false,
      "fs": false,
      "os": false,
      "crypto": false,
      "stream": false,
      "util": false,
      "buffer": false,
      "process": false
    }
  },
  
  externals: {
    'react': 'react',
    'react-dom': 'react-dom'
  }
};