// Pages
export { DemoPage } from './lib/pages/demo-page';
export { LoggingDemoPage } from './lib/pages/logging-demo-page';
export { SseDemoPage } from './lib/pages/sse-demo-page';

// Demo Components
export { MarketOverview, SseDemo } from './lib/components/demo';

// Test Coagent Components
export {
  TestCoagentPage,
  SendOtpTestCard,
  MobileLoginTestCard,
  UserDetailTestCard,
  CoagentTestCard,
  TechnicalDescription
} from './lib/components/test-coagent';

export type {
  TestCoagentPageProps,
  SendOtpTestCardProps,
  MobileLoginTestCardProps,
  UserDetailTestCardProps,
  CoagentTestCardProps
} from './lib/components/test-coagent';

// Shared Components
export {
  UserStatusCard,
  TestScenarioCard,
  TestResult
} from './lib/components/shared';

export type {
  UserStatusCardProps,
  UserSession,
  TestScenarioCardProps,
  TestResultProps
} from './lib/components/shared';

// Hooks
export { useTestScenarios } from './lib/hooks/useTestScenarios';

// 配置 - 已移除Coagent相关配置，专注于核心功能

// Actions
export { getMarketDataAction, getMarketDataBySymbolAction, testCoagentRequest, testApiHealth, testDemoFeatureApi } from './lib/actions';

// Types
export type { MarketData } from './lib/types';
