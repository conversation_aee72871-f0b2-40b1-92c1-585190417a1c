'use client';

import { MarketOverview } from '../components/demo/market-overview';
import { useState } from 'react';

interface DemoPageProps {
  className?: string;
}

export function DemoPage({ className }: DemoPageProps) {
  const [refreshKey, setRefreshKey] = useState(0);

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className={`container mx-auto px-4 py-8 ${className || ''}`}>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          演示功能页面
        </h1>
        <p className="text-gray-600">
          这是一个演示插件式架构的试点功能模块，展示了前后端分离和组件复用的能力。
        </p>
      </div>

      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">实时市场数据</h2>
        <button 
          onClick={handleRefresh}
          className="px-4 py-2 text-sm border rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          刷新数据
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <MarketOverview key={refreshKey} />
        
        <div className="space-y-4">
          <div className="p-6 bg-purple-50 rounded-lg">
            <h3 className="text-lg font-medium text-purple-900 mb-4">
              功能演示
            </h3>
            <div className="space-y-3">
              <a
                href="/demo-feature/test-coagent"
                className="block p-3 bg-white rounded-md border border-purple-200 hover:border-purple-300 hover:shadow-sm transition-all"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-purple-900">🧪 Coagent 测试</h4>
                    <p className="text-sm text-purple-700">测试用户认证和 API 调用功能</p>
                  </div>
                  <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </a>
            </div>
          </div>

          <div className="p-6 bg-blue-50 rounded-lg">
            <h3 className="text-lg font-medium text-blue-900 mb-2">
              架构特点
            </h3>
            <ul className="text-blue-800 space-y-1 text-sm">
              <li>• 使用共享 UI 组件库 (shared-fe-kit)</li>
              <li>• 异步数据获取与状态管理</li>
              <li>• 响应式布局设计</li>
              <li>• TypeScript 类型安全</li>
              <li>• Next.js 原生编译，无需预构建</li>
            </ul>
          </div>

          <div className="p-6 bg-green-50 rounded-lg">
            <h3 className="text-lg font-medium text-green-900 mb-2">
              模块化优势
            </h3>
            <ul className="text-green-800 space-y-1 text-sm">
              <li>• 独立开发和测试</li>
              <li>• 清晰的依赖边界</li>
              <li>• 可复用的组件设计</li>
              <li>• 易于维护和扩展</li>
              <li>• 支持热重载开发</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};