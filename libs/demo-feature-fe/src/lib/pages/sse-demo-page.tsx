/**
 * SSE Demo 页面 - 完整的 Server-Sent Events 演示
 */
'use client';

import React from 'react';
import { SseDemo } from '../components/demo/sse-demo';

export function SseDemoPage() {
  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Coagent SSE 完整演示
          </h1>
          <p className="text-lg text-gray-600">
            演示 coagent 在 Server Action 中的使用，以及与 demo-feature-bs 后端的 SSE 通信
          </p>
        </div>
        
        <SseDemo />
        
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">技术架构</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">Coagent 功能</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• 服务端专用的统一 HTTP 请求代理</li>
                <li>• 自动身份认证和请求头处理</li>
                <li>• 完整的 SSE 流管理和生命周期回调</li>
                <li>• 链路追踪和结构化日志记录</li>
                <li>• 与 demo-feature-bs 后端无缝集成</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-3">Demo 架构</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• <strong>后端</strong>: demo-feature-bs 提供 SSE 流</li>
                <li>• <strong>中间层</strong>: Server Action 使用 coagent</li>
                <li>• <strong>前端</strong>: EventSource 直连展示数据</li>
                <li>• <strong>数据流</strong>: 实时市场数据、AI分析、系统状态</li>
                <li>• <strong>类型安全</strong>: 全栈 TypeScript 支持</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}