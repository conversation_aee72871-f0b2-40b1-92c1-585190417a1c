'use client';

import React from 'react';

/**
 * 测试结果组件属性
 */
export interface TestResultProps {
  result: string;
  title?: string;
}

/**
 * 测试结果显示组件
 */
export function TestResult({ result, title = '测试结果' }: TestResultProps) {
  if (!result) return null;

  return (
    <div className="mt-4 p-4 bg-gray-100 rounded-md">
      <h3 className="font-medium mb-2">{title}:</h3>
      <pre className="text-sm whitespace-pre-wrap text-gray-700">
        {result}
      </pre>
    </div>
  );
}
