'use client';

import React from 'react';

/**
 * 测试场景卡片组件属性
 */
export interface TestScenarioCardProps {
  title: string;
  description: string;
  endpoint?: string;
  isPublic?: boolean;
  color: 'blue' | 'green' | 'purple';
  children: React.ReactNode;
}

/**
 * 测试场景卡片组件
 */
export function TestScenarioCard({ 
  title, 
  description, 
  endpoint, 
  isPublic = false,
  color, 
  children 
}: TestScenarioCardProps) {
  const colorClasses = {
    blue: 'text-blue-600',
    green: 'text-green-600',
    purple: 'text-purple-600'
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className={`text-xl font-semibold mb-4 ${colorClasses[color]}`}>
        {title}
      </h2>
      
      <div className="space-y-4">
        <div className="text-sm text-gray-600">
          <p>{description}</p>
          {endpoint && (
            <p className="text-xs mt-1">
              接口: {endpoint} ({isPublic ? '公开接口' : '需要身份验证'})
            </p>
          )}
        </div>
        
        {children}
      </div>
    </div>
  );
}
