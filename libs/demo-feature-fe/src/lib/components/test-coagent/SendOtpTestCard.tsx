'use client';

import React from 'react';
import { TestScenarioCard } from '../shared/TestScenarioCard';
import { TestResult } from '../shared/TestResult';

/**
 * 发送验证码测试卡片组件属性
 */
export interface SendOtpTestCardProps {
  mobile: string;
  setMobile: (mobile: string) => void;
  onTest: () => void;
  isLoading: boolean;
  result: string;
}

/**
 * 发送验证码测试卡片组件
 */
export function SendOtpTestCard({ 
  mobile, 
  setMobile, 
  onTest, 
  isLoading, 
  result 
}: SendOtpTestCardProps) {
  return (
    <TestScenarioCard
      title="📱 测试场景1: 发送验证码"
      description="测试发送短信验证码功能"
      endpoint="/account/login/sendOtpCode"
      isPublic={true}
      color="blue"
    >
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          手机号:
        </label>
        <input
          type="tel"
          value={mobile}
          onChange={(e) => setMobile(e.target.value)}
          placeholder="请输入手机号"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>
      
      <button
        onClick={onTest}
        disabled={isLoading || !mobile}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isLoading ? '发送中...' : '发送验证码'}
      </button>
      
      <TestResult result={result} />
    </TestScenarioCard>
  );
}
