'use client';

import React from 'react';

/**
 * 技术说明组件
 */
export function TechnicalDescription() {
  return (
    <div className="mt-8 bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold mb-4">技术实现说明</h2>
      <div className="prose text-gray-600">
        <h3 className="text-lg font-medium text-gray-900">Coagent 认证流程:</h3>
        <ol className="list-decimal list-inside space-y-2 mt-2">
          <li><strong>Cookie 解析</strong>: getUserIdFromCookie 函数从 iron-session 加密的 cookie 中提取用户ID</li>
          <li><strong>请求头注入</strong>: createHeaders 函数将用户ID注入到 lucas-uniq-userId 请求头中</li>
          <li><strong>自动认证</strong>: coagent 在每个非公开API请求中自动执行上述流程</li>
          <li><strong>安全保障</strong>: 整个过程在服务端进行，确保用户身份信息的安全性</li>
        </ol>
        
        <h3 className="text-lg font-medium text-gray-900 mt-6">接口分类:</h3>
        <div className="mt-2">
          <h4 className="font-medium text-gray-800">公开接口 (不需要身份验证):</h4>
          <ul className="list-disc list-inside space-y-1 mt-1 ml-4">
            <li>/account/login/authWithMobile (登录接口)</li>
            <li>/account/login/sendSms (发送验证码)</li>
            <li>/account/login/sendOtpCode (发送OTP验证码)</li>
          </ul>
          
          <h4 className="font-medium text-gray-800 mt-4">私有接口 (需要身份验证):</h4>
          <ul className="list-disc list-inside space-y-1 mt-1 ml-4">
            <li>POST /account/user/detail (获取个人详细信息)</li>
            <li>GET /account/user/profile (获取用户档案)</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
