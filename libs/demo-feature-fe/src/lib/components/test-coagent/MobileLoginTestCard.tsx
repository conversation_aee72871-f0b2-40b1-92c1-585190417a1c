'use client';

import React from 'react';
import { TestScenarioCard } from '../shared/TestScenarioCard';
import { TestResult } from '../shared/TestResult';

/**
 * 手机号登录测试卡片组件属性
 */
export interface MobileLoginTestCardProps {
  mobile: string;
  setMobile: (mobile: string) => void;
  code: string;
  setCode: (code: string) => void;
  onTest: () => void;
  isLoading: boolean;
  result: string;
}

/**
 * 手机号登录测试卡片组件
 */
export function MobileLoginTestCard({ 
  mobile, 
  setMobile, 
  code, 
  setCode, 
  onTest, 
  isLoading, 
  result 
}: MobileLoginTestCardProps) {
  return (
    <TestScenarioCard
      title="🔐 测试场景2: 手机号验证码登录"
      description="测试手机号验证码登录功能"
      endpoint="/account/login/authWithMobile"
      isPublic={true}
      color="green"
    >
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          手机号:
        </label>
        <input
          type="tel"
          value={mobile}
          onChange={(e) => setMobile(e.target.value)}
          placeholder="请输入手机号"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          验证码:
        </label>
        <input
          type="text"
          value={code}
          onChange={(e) => setCode(e.target.value)}
          placeholder="请输入验证码"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
        />
      </div>
      
      <button
        onClick={onTest}
        disabled={isLoading || !mobile || !code}
        className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
      >
        {isLoading ? '登录中...' : '验证码登录'}
      </button>
      
      <TestResult result={result} />
    </TestScenarioCard>
  );
}
