import { Card, CardHeader, Card<PERSON>itle, CardContent } from '@yai-investor-insight/shared-fe-kit';
import { getMarketDataAction } from '../../actions/marketDataActions';

interface MarketOverviewProps {
  className?: string;
}

export async function MarketOverview({ className }: MarketOverviewProps) {
  const marketData = await getMarketDataAction();

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>市场概览</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {marketData?.map((stock) => (
            <div
              key={stock.symbol}
              className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
            >
              <div className="flex-1">
                <div className="font-semibold text-gray-900">{stock.symbol}</div>
                <div className="text-sm text-gray-500">{stock.name}</div>
              </div>
              <div className="text-right">
                <div className="font-medium">${stock.price.toFixed(2)}</div>
                <div
                  className={`text-sm ${
                    stock.change >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {stock.change >= 0 ? '+' : ''}
                  {stock.change.toFixed(2)} ({stock.changePercent.toFixed(2)}%)
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
