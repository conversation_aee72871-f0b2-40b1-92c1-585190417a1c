'use client';

import React, { useState, useEffect, useRef } from 'react';
import { testApiHealth, testDemoFeatureApi } from '../../actions';

interface SseMessage {
  id: string;
  timestamp: string;
  data: any;
  type?: string;
}

export function SseDemo() {
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState<SseMessage[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [error, setError] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);
  const eventSourceRef = useRef<EventSource | null>(null);

  // 确保只在客户端运行
  useEffect(() => {
    setIsClient(true);
  }, []);

  const connectToSse = async () => {
    try {
      setConnectionStatus('connecting');
      setError(null);
      setMessages([]);

      // 检查是否在客户端环境且 EventSource 可用
      if (!isClient || typeof window === 'undefined') {
        throw new Error('This function can only be called on the client side');
      }

      if (typeof EventSource === 'undefined') {
        throw new Error('EventSource is not supported in this browser');
      }

      // 直接使用 EventSource 连接到后端 (前端展示)
      // 注意：不再通过 Server Action 使用 coagent.stream，因为 EventSource 是浏览器专用 API
      const baseUrl = 'http://localhost:8000';
      const params = new URLSearchParams({
        user_id: 'demo-user-123',
        format: 'json'
      });
      const sseUrl = `${baseUrl}/api/plugins/demo-feature/stream/demo?${params.toString()}`;

      // 创建 EventSource (仅在客户端)
      const eventSource = new window.EventSource(sseUrl);
      eventSourceRef.current = eventSource;

      // 处理连接打开
      eventSource.onopen = () => {
        console.log('SSE connection opened');
        setIsConnected(true);
        setConnectionStatus('connected');
        
        // 添加连接成功消息
        const message: SseMessage = {
          id: Date.now().toString(),
          timestamp: new Date().toISOString(),
          data: { 
            type: 'connection', 
            message: 'SSE connection established successfully (direct EventSource)',
            method: 'Direct EventSource API'
          },
          type: 'system'
        };
        setMessages(prev => [...prev, message]);
      };

      // 处理消息
      eventSource.onmessage = (event) => {
        console.log('SSE message received:', event.data);
        try {
          const data = JSON.parse(event.data);
          const message: SseMessage = {
            id: Date.now().toString(),
            timestamp: new Date().toISOString(),
            data: data,
            type: typeof data === 'object' ? data.type : 'message'
          };
          setMessages(prev => [...prev, message]);
        } catch (error) {
          console.warn('Failed to parse SSE message:', event.data);
          const message: SseMessage = {
            id: Date.now().toString(),
            timestamp: new Date().toISOString(),
            data: event.data,
            type: 'raw'
          };
          setMessages(prev => [...prev, message]);
        }
      };

      // 处理错误
      eventSource.onerror = (error) => {
        console.error('SSE connection error:', error);
        setError('SSE connection error');
        setConnectionStatus('error');
        setIsConnected(false);
        eventSource.close();
        eventSourceRef.current = null;
      };

    } catch (error) {
      console.error('Failed to create SSE connection:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
      setConnectionStatus('error');
      setIsConnected(false);
    }
  };

  const disconnectFromSse = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
      setIsConnected(false);
      setConnectionStatus('disconnected');
      console.log('SSE connection closed');
    }
  };

  const clearMessages = () => {
    setMessages([]);
  };

  const testRegularRequest = async () => {
    try {
      // 使用 Server Action 调用后端 API - 演示 coagent.request 功能
      const result = await testApiHealth();
      
      const message: SseMessage = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        data: { 
          type: 'server_action_test', 
          server_action_result: result,
          method: 'coagent.request via Server Action'
        },
        type: 'test'
      };
      setMessages(prev => [...prev, message]);
    } catch (error) {
      console.error('Server Action request failed:', error);
      setError(error instanceof Error ? error.message : 'Server Action failed');
    }
  };

  const testDemoFeatureRequest = async () => {
    try {
      // 测试 demo-feature 后端 API
      const result = await testDemoFeatureApi();
      
      const message: SseMessage = {
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        data: { 
          type: 'demo_feature_test', 
          server_action_result: result,
          method: 'coagent.request demo-feature API'
        },
        type: 'test'
      };
      setMessages(prev => [...prev, message]);
    } catch (error) {
      console.error('Demo feature request failed:', error);
      setError(error instanceof Error ? error.message : 'Demo feature request failed');
    }
  };

  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  const getStatusBadgeColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'connecting': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return 'Connected';
      case 'connecting': return 'Connecting...';
      case 'error': return 'Error';
      default: return 'Disconnected';
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">SSE Demo</h2>
          <p className="text-sm text-gray-600 mt-1">
            测试 Server-Sent Events (SSE) 功能的完整示例
          </p>
        </div>

        <div className="p-6">
          {/* 连接状态 */}
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700">连接状态:</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeColor()}`}>
                  {getStatusText()}
                </span>
              </div>
              <div className="text-sm text-gray-500">
                消息数量: {messages.length}
              </div>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
              <div className="flex">
                <div className="text-sm text-red-700">
                  <strong>错误:</strong> {error}
                </div>
              </div>
            </div>
          )}

          {/* 控制按钮 */}
          <div className="mb-6 flex flex-wrap gap-3">
            <button
              onClick={connectToSse}
              disabled={!isClient || isConnected || connectionStatus === 'connecting'}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {connectionStatus === 'connecting' ? '连接中...' : '连接 SSE'}
            </button>
            
            <button
              onClick={disconnectFromSse}
              disabled={!isConnected}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              断开连接
            </button>

            <button
              onClick={testRegularRequest}
              disabled={!isClient}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              测试健康检查 API
            </button>

            <button
              onClick={testDemoFeatureRequest}
              disabled={!isClient}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              测试 Demo Feature API
            </button>

            <button
              onClick={clearMessages}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              清空消息
            </button>
          </div>

          {/* 消息列表 */}
          <div className="bg-gray-50 rounded-lg">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">消息日志</h3>
            </div>
            <div className="p-4 max-h-96 overflow-y-auto">
              {messages.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  暂无消息
                </div>
              ) : (
                <div className="space-y-3">
                  {messages.map((message, index) => (
                    <div
                      key={message.id}
                      className="bg-white p-3 rounded border border-gray-200"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <span className="text-sm font-medium text-gray-700">
                          #{index + 1}
                        </span>
                        <div className="flex items-center space-x-2">
                          {message.type && (
                            <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                              {message.type}
                            </span>
                          )}
                          <span className="text-xs text-gray-500">
                            {new Date(message.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                      </div>
                      <pre className="text-sm text-gray-800 whitespace-pre-wrap bg-gray-50 p-2 rounded overflow-x-auto">
                        {JSON.stringify(message.data, null, 2)}
                      </pre>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* 使用说明 */}
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h4 className="text-sm font-medium text-blue-900 mb-2">使用说明:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• <strong>"连接 SSE"</strong>: 使用原生 EventSource API 直接连接到 demo-feature-bs 后端</li>
              <li>• <strong>"测试健康检查 API"</strong>: 通过 Server Action + coagent.request 调用健康检查端点</li>
              <li>• <strong>"测试 Demo Feature API"</strong>: 通过 Server Action + coagent.request 调用 demo-feature 市场数据端点</li>
              <li>• <strong>架构说明</strong>: SSE 使用客户端直连，HTTP 请求使用服务端 coagent 代理</li>
              <li>• 所有消息会显示在日志中，包含完整的请求/响应数据和方法信息</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}