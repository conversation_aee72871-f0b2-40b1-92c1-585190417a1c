'use server';

import { createCoagent } from '@yai-investor-insight/shared-fe-core/server';
import { axios } from '@yai-investor-insight/shared-fe-core';

const axiosInstance = axios.create({
  baseURL: 'http://localhost:8000',
  timeout: 30000,
});

const coagent = createCoagent(axiosInstance);

// 注意：coagent.stream() 在服务端不可用，因为 EventSource 是浏览器 API
// 这里只演示常规的 HTTP 请求功能
export async function testCoagentRequest(endpoint: string = '/api/v1/health') {
  try {
    const response = await coagent.request({
      method: 'GET',
      url: endpoint,
    });

    return {
      success: true,
      data: response.data,
      status: response.status,
      message: 'Coagent request successful'
    };
  } catch (error) {
    console.error('[Server Action] Coagent request failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Coagent request failed'
    };
  }
}

export async function testApiHealth() {
  return testCoagentRequest('/api/v1/health');
}

export async function testDemoFeatureApi() {
  return testCoagentRequest('/api/plugins/demo-feature/market-data');
}