"""API路由适配器"""

import logging
import asyncio
import json
from datetime import datetime
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from typing import List, Dict, Any
from ..application.use_cases import GetMarketDataUseCase, GetMarketDataBySymbolUseCase
from ..infrastructure.mock_repository import MockMarketDataRepository

# Get logger instance
logger = logging.getLogger(__name__)
# 创建路由器
router = APIRouter(prefix="/api/plugins/demo-feature", tags=["demo-feature"])

# 创建依赖
repository = MockMarketDataRepository()
get_market_data_use_case = GetMarketDataUseCase(repository)
get_market_data_by_symbol_use_case = GetMarketDataBySymbolUseCase(repository)


@router.get("/market-data", response_model=List[Dict[str, Any]])
async def get_market_data():
    """获取市场数据"""
    try:
        market_data = await get_market_data_use_case.execute()
        return [data.dict() for data in market_data]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/market-data/{symbol}", response_model=Dict[str, Any])
async def get_market_data_by_symbol(symbol: str):
    """根据股票代码获取市场数据"""
    try:
        market_data = await get_market_data_by_symbol_use_case.execute(symbol)
        return market_data.dict()
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stream/demo")
async def stream_demo_data(user_id: str = "anonymous", format: str = "json"):
    """SSE 流式数据演示端点"""
    
    async def generate_stream():
        """生成模拟的实时数据流"""
        try:
            counter = 0
            while True:
                counter += 1
                
                # 模拟不同类型的数据
                if counter % 5 == 0:
                    # 市场数据更新
                    data = {
                        "type": "market_update",
                        "timestamp": datetime.now().isoformat(),
                        "data": {
                            "symbol": "AAPL",
                            "price": 150.25 + (counter % 10) * 0.5,
                            "change": (counter % 10) * 0.1 - 0.5,
                            "volume": 1000000 + counter * 1000
                        },
                        "user_id": user_id,
                        "counter": counter
                    }
                elif counter % 3 == 0:
                    # AI 分析结果
                    data = {
                        "type": "ai_analysis",
                        "timestamp": datetime.now().isoformat(),
                        "data": {
                            "analysis": f"市场分析报告 #{counter}",
                            "sentiment": "positive" if counter % 2 == 0 else "negative",
                            "confidence": min(0.95, 0.6 + (counter % 5) * 0.1),
                            "recommendations": ["买入", "持有", "卖出"][counter % 3]
                        },
                        "user_id": user_id,
                        "counter": counter
                    }
                else:
                    # 系统状态
                    data = {
                        "type": "system_status",
                        "timestamp": datetime.now().isoformat(),
                        "data": {
                            "status": "active",
                            "cpu_usage": min(95, 20 + (counter % 10) * 5),
                            "memory_usage": min(90, 30 + (counter % 8) * 6),
                            "active_connections": 150 + counter % 50
                        },
                        "user_id": user_id,
                        "counter": counter
                    }
                
                # 格式化输出
                if format.lower() == "json":
                    yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                else:
                    yield f"data: {data}\n\n"
                
                # 等待2秒再发送下一条消息
                await asyncio.sleep(2)
                
                # 模拟连接中断 (每30条消息)
                if counter >= 30:
                    logger.info(f"Stream completed for user {user_id} after {counter} messages")
                    break
                    
        except Exception as e:
            logger.error(f"Error in stream generation: {e}")
            error_data = {
                "type": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "user_id": user_id
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
    
    logger.info(f"Starting SSE stream for user: {user_id}, format: {format}")
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )