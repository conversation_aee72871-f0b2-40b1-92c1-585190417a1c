# Session 服务端管理

专注于Next.js服务端环境的session管理，提供简化的API接口。

**注意**: 客户端session管理(React hooks, 客户端状态等)请使用 `user-account-fe` 模块。

## 推荐使用的新API

### 1. 简化的ServerSession API (推荐)

```typescript
import { serverSession } from '@yai-investor-insight/shared-fe-core';

// 在Server Action中使用
export async function handleLogin(phone: string, code: string) {
  // 调用认证API
  const authResponse = await authWithMobile({ mobile: phone, code });
  
  if (authResponse.success) {
    // 保存用户信息到cookies - 无需手动传递cookies实例
    await serverSession.saveLogin({
      userId: authResponse.data.user.id,
      phone: authResponse.data.user.phone,
      userName: authResponse.data.user.name,
      token: authResponse.data.token
    });
    
    return { success: true };
  }
  
  return { success: false, error: '登录失败' };
}
```

### 2. 获取当前用户信息

```typescript
import { serverSession } from '@yai-investor-insight/shared-fe-core';

// 在Server Action中使用
export async function getCurrentUser() {
  return await serverSession.getCurrentUser();
}

// 在页面组件中使用
export default async function ProfilePage() {
  const user = await serverSession.getCurrentUser();
  
  if (!user.isLoggedIn) {
    redirect('/login');
  }
  
  return (
    <div>
      <h1>欢迎, {user.userName}</h1>
      <p>用户ID: {user.userId}</p>
      <p>手机号: {user.phone}</p>
    </div>
  );
}
```

### 3. 检查登录状态

```typescript
import { serverSession } from '@yai-investor-insight/shared-fe-core';

// 在middleware中使用
export async function middleware(request: NextRequest) {
  const isLoggedIn = await serverSession.isLoggedIn();
  
  if (!isLoggedIn && request.nextUrl.pathname.startsWith('/dashboard')) {
    return NextResponse.redirect(new URL('/login', request.url));
  }
}
```

### 4. 获取用户ID

```typescript
import { serverSession } from '@yai-investor-insight/shared-fe-core';

// 在API路由中使用
export async function GET() {
  const userId = await serverSession.getUserId();
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // 使用userId调用其他API
  const data = await fetchUserData(userId);
  return NextResponse.json(data);
}
```

### 5. 退出登录

```typescript
import { serverSession } from '@yai-investor-insight/shared-fe-core';

// 在Server Action中使用
export async function handleLogout() {
  await serverSession.clearLogin();
  return { success: true };
}
```

### 6. 更新用户信息

```typescript
import { serverSession } from '@yai-investor-insight/shared-fe-core';

export async function updateUserInfo(userName: string) {
  await serverSession.updateUser({ userName });
  return { success: true };
}
```

## 高级用法 - ServerSession类

如果需要自定义配置，可以使用ServerSession类：

```typescript
import { ServerSession, getDefaultSessionOptions } from '@yai-investor-insight/shared-fe-core';

// 自定义配置
const customOptions = {
  ...getDefaultSessionOptions(),
  cookieOptions: {
    ...getDefaultSessionOptions().cookieOptions,
    maxAge: 1000 * 60 * 60 * 24 * 30, // 30天有效期
  }
};

const customSession = new ServerSession(customOptions);

export async function customLogin(userData: any) {
  await customSession.saveLogin(userData);
}
```

## 兼容性API (不推荐新项目使用)

旧的API仍然可用，但需要手动传递cookies实例：

```typescript
import { saveLoginSession, getCurrentUserFromCookies } from '@yai-investor-insight/shared-fe-core';
import { cookies } from 'next/headers';

// 旧方式 - 需要手动传递cookies
export async function oldWayLogin(userData: any) {
  const cookiesInstance = await cookies();
  await saveLoginSession(cookiesInstance, userData);
}
```

## 类型定义

```typescript
interface SessionData {
  userId: string;
  phone: string;
  isLoggedIn: boolean;
  userName?: string;
  token?: string;
}

interface SessionResult {
  isLoggedIn: boolean;
  userId?: string;
  phone?: string;
  userName?: string;
  token?: string;
}
```

## 环境变量

```env
SECRET_COOKIE_PASSWORD=your-secret-key-at-least-32-characters-long
```

## 注意事项

1. **服务端专用**: 所有API只能在服务端环境使用(Server Actions、API Routes、middleware等)
2. **自动cookies处理**: 新API自动处理cookies获取，无需手动传递
3. **默认配置**: 生产环境自动启用secure模式，默认7天有效期
4. **客户端session**: 客户端状态管理请使用 `user-account-fe` 模块

## API对比

| 场景 | 新API (推荐) | 旧API |
|------|-------------|--------|
| 保存登录 | `serverSession.saveLogin(userData)` | `saveLoginSession(cookies, userData)` |
| 获取用户 | `serverSession.getCurrentUser()` | `getCurrentUserFromCookies(cookies)` |
| 检查登录 | `serverSession.isLoggedIn()` | `isUserLoggedIn(cookies)` |
| 获取用户ID | `serverSession.getUserId()` | `getUserIdFromCookies(cookies)` |
| 退出登录 | `serverSession.clearLogin()` | `clearLoginSession(cookies)` |
