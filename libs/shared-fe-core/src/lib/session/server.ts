/**
 * Next.js Server-side Session Utilities
 * 用于 Server Actions、API Routes、middleware 等服务端环境
 */

import { cookies } from 'next/headers';
import { getIronSession } from 'iron-session';
import { SessionData, SessionResult, SessionOptions, getDefaultSessionOptions } from './types';

/**
 * Server端Session操作类 - 自动处理cookies获取
 * 简化服务端使用，无需手动传递cookies实例
 */
export class ServerSession {
  private options: SessionOptions;

  constructor(options?: SessionOptions) {
    this.options = options || getDefaultSessionOptions();
  }

  /**
   * 保存用户登录信息
   */
  async saveLogin(userData: {
    userId: string;
    phone: string;
    userName?: string;
    token?: string;
  }): Promise<void> {
    const cookiesInstance = await cookies();
    const session = await getIronSession<SessionData>(cookiesInstance, this.options);
    
    session.isLoggedIn = true;
    session.userId = userData.userId;
    session.phone = userData.phone;
    session.userName = userData.userName;
    session.token = userData.token;
    
    await session.save();
  }

  /**
   * 获取当前登录用户信息
   */
  async getCurrentUser(): Promise<SessionResult> {
    try {
      const cookiesInstance = await cookies();
      const session = await getIronSession<SessionData>(cookiesInstance, this.options);
      
      if (session.isLoggedIn && session.userId) {
        return {
          isLoggedIn: true,
          userId: session.userId,
          phone: session.phone,
          userName: session.userName,
          token: session.token
        };
      }
      
      return { isLoggedIn: false };
    } catch {
      return { isLoggedIn: false };
    }
  }

  /**
   * 检查用户是否已登录
   */
  async isLoggedIn(): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user.isLoggedIn;
  }

  /**
   * 获取用户ID（常用于API调用）
   */
  async getUserId(): Promise<string | null> {
    const user = await this.getCurrentUser();
    return user.isLoggedIn && user.userId ? user.userId : null;
  }

  /**
   * 清除登录信息（退出登录）
   */
  async clearLogin(): Promise<void> {
    const cookiesInstance = await cookies();
    const session = await getIronSession<SessionData>(cookiesInstance, this.options);
    
    session.isLoggedIn = false;
    session.userId = '';
    session.phone = '';
    session.userName = undefined;
    session.token = undefined;
    
    await session.save();
  }

  /**
   * 更新用户信息
   */
  async updateUser(updates: {
    userName?: string;
    token?: string;
    phone?: string;
  }): Promise<void> {
    const cookiesInstance = await cookies();
    const session = await getIronSession<SessionData>(cookiesInstance, this.options);
    
    if (!session.isLoggedIn) {
      throw new Error('No active session to update');
    }

    if (updates.userName !== undefined) session.userName = updates.userName;
    if (updates.token !== undefined) session.token = updates.token;
    if (updates.phone !== undefined) session.phone = updates.phone;
    
    await session.save();
  }
}

/**
 * 默认Server Session实例 - 使用默认配置
 * 大多数场景下直接使用这个实例即可
 */
export const session = new ServerSession();

/**
 * 便捷函数 - 直接使用默认配置的简化接口
 */
export const serverSession = {
  /** 保存登录信息 */
  saveLogin: (userData: Parameters<ServerSession['saveLogin']>[0]) => session.saveLogin(userData),
  
  /** 获取当前用户 */
  getCurrentUser: () => session.getCurrentUser(),
  
  /** 检查是否已登录 */
  isLoggedIn: () => session.isLoggedIn(),
  
  /** 获取用户ID */
  getUserId: () => session.getUserId(),
  
  /** 清除登录信息 */
  clearLogin: () => session.clearLogin(),
  
  /** 更新用户信息 */
  updateUser: (updates: Parameters<ServerSession['updateUser']>[0]) => session.updateUser(updates)
};