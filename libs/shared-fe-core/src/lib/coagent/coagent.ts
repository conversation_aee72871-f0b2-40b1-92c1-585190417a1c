import { AxiosInstance, AxiosRequestConfig } from 'axios';
import { serverSession } from '../session/server';

/**
 * Session 提供者接口 - 用于获取用户身份信息
 */
export interface SessionProvider {
  /** 获取当前用户ID */
  getUserId(): Promise<string | null>;
}

/**
 * 请求选项接口
 */
export interface CoagentRequestOptions {
  /** HTTP方法 */
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  /** 请求URL */
  url: string;
  /** 请求数据 */
  data?: any;
  /** 查询参数 */
  params?: any;
  /** 自定义请求头 */
  headers?: Record<string, string>;
  /** 是否为公开接口（不需要认证） */
  isPublic?: boolean;
  /** 其他axios配置 */
  config?: Omit<AxiosRequestConfig, 'method' | 'url' | 'data' | 'params' | 'headers'>;
}

/**
 * SSE流选项接口
 */
export interface CoagentStreamOptions {
  /** 请求URL */
  url: string;
  /** 请求数据 */
  data?: any;
  /** 查询参数 */
  params?: any;
  /** 自定义请求头 */
  headers?: Record<string, string>;
  /** 是否为公开接口（不需要认证） */
  isPublic?: boolean;
  /** 事件回调 */
  onMessage?: (data: any) => void;
  /** 错误回调 */
  onError?: (error: Error) => void;
  /** 连接关闭回调 */
  onClose?: () => void;
  /** 连接打开回调 */
  onOpen?: () => void;
}

/**
 * 响应接口
 */
export interface CoagentResponse<T = any> {
  data: T;
  error?: string;
  status?: number;
  statusText?: string;
}

/**
 * Coagent实例接口
 */
export interface Coagent {
  /** 发送请求 */
  request: <T = any>(options: CoagentRequestOptions) => Promise<CoagentResponse<T>>;
  /** 创建SSE流连接 */
  stream: (options: CoagentStreamOptions) => Promise<() => void>;
}

/**
 * 判断是否为公开接口
 */
function isPublicEndpoint(url: string): boolean {
  const publicPaths = [
    '/health',
    '/ping',
    '/public',
    '/login',
    '/register',
    '/forgot-password'
  ];
  
  return publicPaths.some(path => url.includes(path));
}

/**
 * 生成追踪ID
 */
function generateTraceId(): string {
  return `trace_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * 获取适合的日志器
 */
async function getLogger() {
  try {
    // 尝试使用 server 日志器
    const { logger } = await import('../log/server');
    return logger;
  } catch (error) {
    // 回退到控制台日志
    return {
      info: async (message: string, data?: any) => console.info(message, data),
      warn: async (message: string, data?: any) => console.warn(message, data),
      error: async (message: string, data?: any) => console.error(message, data),
      debug: async (message: string, data?: any) => console.debug(message, data),
    };
  }
}

/**
 * 记录 API 请求日志
 */
async function logApiRequest(
  url: string,
  method: string,
  traceId: string,
  requestData?: any,
  headers?: Record<string, string>
): Promise<void> {
  const logger = await getLogger();
  await logger.info('Coagent API Request', {
    url,
    method,
    traceId,
    requestData: requestData ? JSON.stringify(requestData).substring(0, 1000) : undefined, // 限制长度
    headers: headers ? Object.keys(headers).join(', ') : undefined,
    type: 'coagent_request',
    timestamp: new Date().toISOString()
  });
}

/**
 * 记录 API 响应日志
 */
async function logApiResponse(
  url: string,
  method: string,
  traceId: string,
  status: number,
  statusText: string,
  duration: number,
  responseData?: any,
  error?: string
): Promise<void> {
  const logger = await getLogger();
  const level = status >= 400 ? 'error' : 'info';

  await logger[level]('Coagent API Response', {
    url,
    method,
    traceId,
    status,
    statusText,
    duration: `${duration}ms`,
    responseData: responseData ? JSON.stringify(responseData).substring(0, 1000) : undefined, // 限制长度
    error,
    type: 'coagent_response',
    timestamp: new Date().toISOString()
  });
}

/**
 * 创建请求头
 */
async function createRequestHeaders(
  headers: Record<string, string> = {},
  isPublic: boolean = false
): Promise<Record<string, string>> {
  let finalHeaders = { ...headers };

  // 如果不是公开接口，则从 serverSession 中获取 userId 并注入用户身份信息
  if (!isPublic) {
    try {
      const userId = await serverSession.getUserId();

      if (userId) {
        finalHeaders['lucas-uniq-userId'] = userId;
      }
    } catch (error) {
      console.warn('Failed to get user ID from server session:', error);
    }
  }

  // 确保Content-Type存在
  if (!finalHeaders['Content-Type'] && !finalHeaders['content-type']) {
    finalHeaders['Content-Type'] = 'application/json';
  }

  return finalHeaders;
}

/**
 * 构建完整URL
 */
function buildFullUrl(baseURL: string | undefined, url: string): string {
  if (!baseURL) return url;
  const cleanBaseURL = baseURL.replace(/\/$/, '');
  const cleanUrl = url.startsWith('/') ? url : '/' + url;
  return `${cleanBaseURL}${cleanUrl}`;
}

/**
 * 创建Coagent实例
 *
 * @param axiosInstance - axios实例
 * @returns Coagent实例
 */
export function createCoagent(axiosInstance: AxiosInstance): Coagent {
  return {
    request: async <T = any>(options: CoagentRequestOptions): Promise<CoagentResponse<T>> => {
      const traceId = generateTraceId();
      const startTime = Date.now();

      try {
        const {
          method = 'GET',
          url,
          data,
          params,
          headers = {},
          isPublic,
          config = {}
        } = options;

        // 自动判断是否为公开接口
        const finalIsPublic = isPublic !== undefined ? isPublic : isPublicEndpoint(url);
        
        // 构建完整URL
        const fullUrl = buildFullUrl(axiosInstance.defaults.baseURL, url);

        // 创建请求头
        const finalHeaders = await createRequestHeaders(headers, finalIsPublic);

        // 记录请求日志
        await logApiRequest(fullUrl, method, traceId, data, finalHeaders);
        
        // 发送请求
        let response;
        const requestConfig = { ...config };
        
        switch (method.toUpperCase()) {
          case 'POST':
            response = await axiosInstance.post(url, data, { headers: finalHeaders, ...requestConfig });
            break;
          case 'PUT':
            response = await axiosInstance.put(url, data, { headers: finalHeaders, ...requestConfig });
            break;
          case 'DELETE':
            response = await axiosInstance.delete(url, { headers: finalHeaders, ...requestConfig });
            break;
          case 'PATCH':
            response = await axiosInstance.patch(url, data, { headers: finalHeaders, ...requestConfig });
            break;
          case 'GET':
          default:
            response = await axiosInstance.get(url, { 
              headers: finalHeaders,
              ...requestConfig, 
              params 
            });
            break;
        }
        
        // 记录成功响应日志
        const duration = Date.now() - startTime;
        await logApiResponse(
          fullUrl,
          method,
          traceId,
          response.status,
          response.statusText || 'OK',
          duration,
          response.data
        );

        return {
          data: response.data,
          error: undefined,
          status: response.status,
          statusText: response.statusText
        };
      } catch (error: any) {
        // 记录错误响应日志
        const duration = Date.now() - startTime;
        const status = error.response?.status || 500;
        const statusText = error.response?.statusText || 'Error';
        const fullUrl = buildFullUrl(axiosInstance.defaults.baseURL, options.url);

        await logApiResponse(
          fullUrl,
          options.method || 'GET',
          traceId,
          status,
          statusText,
          duration,
          error.response?.data,
          error.message || 'Unknown error occurred'
        );

        return {
          data: null as T,
          error: error.message || 'Unknown error occurred',
          status,
          statusText
        };
      }
    },

    stream: async (options: CoagentStreamOptions): Promise<() => void> => {
      const traceId = generateTraceId();
      const {
        url,
        data,
        params,
        headers = {},
        isPublic,
        onMessage,
        onError,
        onClose,
        onOpen
      } = options;

      // 自动判断是否为公开接口
      const finalIsPublic = isPublic !== undefined ? isPublic : isPublicEndpoint(url);
      
      // 构建完整URL
      const fullUrl = buildFullUrl(axiosInstance.defaults.baseURL, url);

      // 创建请求头
      const finalHeaders = await createRequestHeaders(headers, finalIsPublic);
      
      // 记录流请求日志
      await logApiRequest(fullUrl, 'STREAM', traceId, data, finalHeaders);

      // 构建最终URL（包含查询参数）
      let streamUrl = fullUrl;
      if (params) {
        const searchParams = new URLSearchParams();
        Object.keys(params).forEach(key => {
          if (params[key] !== undefined && params[key] !== null) {
            searchParams.append(key, String(params[key]));
          }
        });
        const queryString = searchParams.toString();
        if (queryString) {
          streamUrl += (streamUrl.includes('?') ? '&' : '?') + queryString;
        }
      }

      return new Promise((resolve, reject) => {
        try {
          // 创建EventSource
          const eventSource = new EventSource(streamUrl);

          // 处理连接打开
          eventSource.onopen = () => {
            console.log(`[Coagent SSE] Connection opened: ${streamUrl}`);
            onOpen?.();
          };

          // 处理消息
          eventSource.onmessage = (event) => {
            try {
              const data = JSON.parse(event.data);
              onMessage?.(data);
            } catch (error) {
              console.warn('[Coagent SSE] Failed to parse message:', event.data);
              onMessage?.(event.data);
            }
          };

          // 处理错误
          eventSource.onerror = (error) => {
            console.error('[Coagent SSE] Connection error:', error);
            const errorObj = new Error('SSE connection error');
            onError?.(errorObj);
          };

          // 返回关闭函数
          const closeConnection = () => {
            eventSource.close();
            console.log(`[Coagent SSE] Connection closed: ${streamUrl}`);
            onClose?.();
          };

          resolve(closeConnection);
        } catch (error) {
          console.error('[Coagent SSE] Failed to create EventSource:', error);
          reject(error);
        }
      });
    }
  };
}

// ============================================================================
// Server-side only - No React hooks
// ============================================================================

// Coagent 是服务端专用的 HTTP 请求代理
// 客户端应该使用 Server Actions 或标准的 fetch/axios 调用 API Routes


