# Coagent - 简化的HTTP请求代理

Coagent是一个简化的HTTP请求代理系统，核心功能是通过cookies中解析出的userId，自动添加认证header头，代理请求到下游的HTTP服务。

## 核心特性

- 🔐 **自动认证**：从cookies中解析userId，自动添加`lucas-uniq-userId`请求头
- 🚀 **极简设计**：只有一个创建函数和一个调用方式
- 📝 **请求日志**：自动记录API请求和响应日志
- 🎯 **智能判断**：自动识别公开接口，无需手动指定
- ⚡ **React集成**：提供React Query集成的hooks
- 🎨 **统一接口**：Server Actions和客户端使用相同的API

## 简化的API设计

### 核心理念
- **一个创建函数**：`createCoagent(axiosInstance)`
- **一个调用方式**：`coagent.request(options)`
- **自动化处理**：认证、日志、错误处理全部自动化

### 工作流程

```mermaid
graph TD
    A[coagent.request] --> B{自动判断接口类型}
    B -->|公开接口| C[直接发送请求]
    B -->|私有接口| D[自动获取userId]
    D --> E[注入认证头]
    E --> F[发送认证请求]
    C --> G[记录日志]
    F --> G
    G --> H[返回统一响应格式]
```

## 快速开始

### 1. 创建 Coagent 实例

```typescript
import axios from 'axios';
import { createCoagent } from '@yai-investor-insight/shared-fe-core';

// 创建 axios 实例
const axiosInstance = axios.create({
  baseURL: 'https://api.example.com',
  timeout: 10000,
});

// 创建 Coagent 实例
const coagent = createCoagent(axiosInstance);
```

### 2. 在 Server Actions 中使用

```typescript
'use server';

import { coagent } from './lib/coagent';

export async function getUserProfile() {
  const response = await coagent.request({
    method: 'GET',
    url: '/api/user/profile'
    // 自动识别为需要认证的接口，会添加 lucas-uniq-userId 头
  });

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}
```

### 3. 在 React 组件中使用

```typescript
'use client';

import { setGlobalCoagent, useCoagentQuery, useCoagentMutation } from '@yai-investor-insight/shared-fe-core';
import { useEffect } from 'react';

// 在应用初始化时设置全局实例
function App() {
  useEffect(() => {
    setGlobalCoagent(coagent);
  }, []);

  return <UserProfile />;
}

// 在组件中使用
function UserProfile() {
  // 查询数据
  const { data, isLoading, error } = useCoagentQuery({
    queryKey: ['user', 'profile'],
    url: '/api/user/profile',
    method: 'GET'
  });

  // 更新数据
  const updateProfile = useCoagentMutation({
    url: '/api/user/profile',
    method: 'PUT',
    onSuccess: () => console.log('Updated!')
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <h1>{data?.data?.name}</h1>
      <button onClick={() => updateProfile.mutate({ name: 'New Name' })}>
        Update
      </button>
    </div>
  );
}
```

## API 参考

### createCoagent(axiosInstance)

创建 Coagent 实例的唯一函数。

**参数：**
- `axiosInstance: AxiosInstance` - 配置好的 axios 实例

**返回：**
- `Coagent` - Coagent 实例

### coagent.request(options)

发送 HTTP 请求的统一方法。

**参数：**
- `options: CoagentRequestOptions`
  - `method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'` - HTTP 方法，默认 'GET'
  - `url: string` - 请求 URL
  - `data?: any` - 请求数据
  - `params?: any` - 查询参数
  - `headers?: Record<string, string>` - 自定义请求头
  - `isPublic?: boolean` - 是否为公开接口，默认自动判断
  - `config?: AxiosRequestConfig` - 其他 axios 配置

**返回：**
- `Promise<CoagentResponse<T>>` - 统一的响应格式
  - `data: T` - 响应数据
  - `error?: string` - 错误信息（如果有）

### React Hooks

#### useCoagentQuery(options)

用于查询数据的 React Query hook。

**参数：**
- `options: UseCoagentQueryOptions<T>`
  - `queryKey: unknown[]` - React Query 的查询键
  - `url: string` - 请求 URL
  - 其他参数同 `coagent.request()`

#### useCoagentMutation(options)

用于数据变更的 React Query hook。

**参数：**
- `options: UseCoagentMutationOptions<T, TVariables>`
  - `url: string` - 请求 URL
  - `method?: 'POST' | 'PUT' | 'DELETE' | 'PATCH'` - HTTP 方法，默认 'POST'
  - 其他参数同 `coagent.request()`

### 全局配置

#### setGlobalCoagent(coagent)

设置全局 Coagent 实例，供 React hooks 使用。

**参数：**
- `coagent: Coagent` - Coagent 实例

## 使用示例

### 基本请求

```typescript
// GET 请求
const userResponse = await coagent.request({
  method: 'GET',
  url: '/api/user/profile'
});

// POST 请求
const createResponse = await coagent.request({
  method: 'POST',
  url: '/api/user',
  data: { name: 'John', email: '<EMAIL>' }
});

// 公开接口请求
const publicResponse = await coagent.request({
  method: 'GET',
  url: '/api/public/data',
  isPublic: true
});
```

### 错误处理

```typescript
const response = await coagent.request({
  method: 'POST',
  url: '/api/user/update',
  data: userData
});

if (response.error) {
  console.error('Request failed:', response.error);
} else {
  console.log('Success:', response.data);
}
```

### 自定义请求头

```typescript
const response = await coagent.request({
  method: 'POST',
  url: '/api/upload',
  data: formData,
  headers: {
    'Content-Type': 'multipart/form-data',
    'X-Custom-Header': 'custom-value'
  }
});
```

## 认证机制

### 自动认证
- 对于非公开接口，自动从 cookies 中获取 userId
- 自动添加 `lucas-uniq-userId` 请求头
- 自动添加 `Content-Type: application/json` 请求头

### 公开接口识别
以下路径会被自动识别为公开接口（不添加认证头）：
- `/health`、`/ping`、`/public`
- `/login`、`/register`、`/forgot-password`

### 手动指定接口类型
```typescript
// 强制指定为公开接口
await coagent.request({
  method: 'GET',
  url: '/api/some/endpoint',
  isPublic: true
});

// 强制指定为私有接口
await coagent.request({
  method: 'POST',
  url: '/api/private/data',
  isPublic: false,
  data: { key: 'value' }
});
```

## 特性

### 自动日志记录
- 自动记录所有 API 请求和响应
- 包含 traceId，便于追踪和调试
- 记录请求耗时和状态码

### 统一错误处理
- 统一的响应格式：`{ data, error }`
- 自动捕获和处理网络错误
- 优雅的认证失败降级

### TypeScript 支持
- 完整的 TypeScript 类型定义
- 泛型支持，提供类型安全的响应数据
- 智能的代码提示和错误检查

## 迁移指南

如果你之前使用的是复杂的 Coagent API，可以按以下方式迁移：

### 旧的方式
```typescript
// 旧的复杂方式
const server = createSmartCoagentServer(axiosInstance, () => cookies());
const response = await server.dispatch({
  type: 'POST',
  payload: {
    url: '/api/user',
    data: userData
  }
});
```

### 新的简化方式
```typescript
// 新的简化方式
const coagent = createCoagent(axiosInstance);
const response = await coagent.request({
  method: 'POST',
  url: '/api/user',
  data: userData
});
```

### React Hooks 迁移
```typescript
// 旧的方式
const { data } = useCoagentQuery({
  queryKey: ['user'],
  endpoint: '/api/user',
  method: 'GET'
});

// 新的方式
const { data } = useCoagentQuery({
  queryKey: ['user'],
  url: '/api/user',
  method: 'GET'
});
```
