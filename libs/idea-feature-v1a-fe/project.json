{"name": "idea-feature-v1a-fe", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/idea-feature-v1a-fe/src", "projectType": "library", "tags": ["scope:idea", "type:feature", "platform:web"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/idea-feature-v1a-fe", "tsConfig": "libs/idea-feature-v1a-fe/tsconfig.lib.json", "packageJson": "libs/idea-feature-v1a-fe/package.json", "main": "libs/idea-feature-v1a-fe/src/index.ts"}}, "lint": {"executor": "@nx/eslint:lint"}, "type-check": {"executor": "nx:run-commands", "options": {"command": "tsc --noEmit -p libs/idea-feature-v1a-fe/tsconfig.lib.json"}}}}