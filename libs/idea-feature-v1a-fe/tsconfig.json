{"extends": "../../tsconfig.base.json", "compilerOptions": {"module": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}]}, "include": ["src", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "jest.config.ts", "**/*.spec.ts", "**/*.test.ts"]}