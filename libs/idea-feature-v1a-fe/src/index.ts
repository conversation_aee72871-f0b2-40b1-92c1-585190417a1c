/**
 * AI Idea Feature Frontend Plugin 导出入口
 *
 * 专注 AI 投资分析功能的组件和类型导出
 */

// Idea 创建模态框组件
export { IdeaCreationModal } from './lib/components/IdeaCreationModal';
export { AIPromptInput } from './lib/components/AIPromptInput';
export { ExamplePrompts } from './lib/components/ExamplePrompts';

// Idea 列表和卡片组件
export { IdeaCardStream } from './lib/components/IdeaCardStream';
export { IdeaCard } from './lib/components/IdeaCard';
export { TagFilter } from './lib/components/TagFilter';

// Idea 详情页组件
export { IdeaDetailPage } from './lib/components/IdeaDetailPage';
export { IdeaDetailEmbedded } from './lib/components/IdeaDetailEmbedded';
export { AiThinkingFlow } from './lib/components/AiThinkingFlow';
export { PersonalIdeaFlow } from './lib/components/PersonalIdeaFlow';
export { StockRecommendations } from './lib/components/StockRecommendations';
export { FactCheckingFlow } from './lib/components/FactCheckingFlow';

// 导航组件
export { IdeaNavigationSection } from './lib/components/IdeaNavigationSection';

// Server Actions
export { createIdea, getIdeaList, getIdeaById } from './lib/actions';

// 类型定义
export type {
  IdeaInput,
  Idea,
  IdeaCreationModalProps,
  AIPromptInputProps,
  ExamplePromptsProps
} from './lib/types';

export type {
  IdeaItem,
  IdeaListResponse,
  IdeaDetail
} from './lib/actions';