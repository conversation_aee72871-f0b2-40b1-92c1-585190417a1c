import type { IdeaItem } from '../actions/getIdeaList'
import type { IdeaDetail } from '../actions/getIdeaById'

/**
 * 统一的 Idea Mock 数据
 * 包含完整的 idea 信息，供 list 和 detail 接口使用
 */

// 我的 Ideas 数据
export const myIdeasData: IdeaItem[] = [
  {
    type: 'idea',
    id: 1,
    title: 'NVIDIA H200超级芯片量产，AI训练效率革命性提升',
    description: 'H200芯片相比H100性能提升90%，内存容量翻倍达到141GB，各大云服务商争相采购。HBM3e内存技术首次商用，带宽达到4.8TB/s，大模型训练成本降低30%。',
    stocks: ['NVDA', 'TSM', 'AMZN', 'GOOGL', 'MSFT'],
    allStocks: ['NVDA', 'TSM', 'AMZN', 'GOOGL', 'MSFT', 'META', 'CRM', 'ORCL'],
    unreadEvents: 2,
    lastUpdated: '2小时前',
    tags: ['AI芯片', '云计算'],
    category: 'AI芯片',
    status: 'completed',
    aiInsight: '受益：AI芯片设计商(NVDA +15%)、代工厂(TSM +8%)、云计算厂商(AMZN/GOOGL/MSFT +5-7%)；受损：传统数据中心设备商(-3-5%)；最新订单数据显示Q1需求超预期40%，确认强劲投资逻辑。',
    newsUpdates: [
      { content: '微软Azure率先部署H200集群，与OpenAI深度合作训练GPT-5', timestamp: '1小时前' },
      { content: 'AWS宣布明年Q1开始提供H200实例服务，预订量已超80%', timestamp: '3小时前' },
      { content: '谷歌云预订500万美元H200算力，加速Gemini模型训练', timestamp: '5小时前' }
    ]
  },
  {
    type: 'idea',
    id: 2,
    title: '微软Copilot全面集成Office365，企业AI办公时代到来',
    description: 'Copilot在Word、Excel、PPT中实现深度集成，企业订阅用户数突破千万，生产力革命正在发生。智能文档生成、数据分析自动化、PPT设计AI化，办公效率提升平均达到45%。',
    stocks: ['MSFT', 'CRM', 'ORCL', 'SNOW', 'WDAY'],
    allStocks: ['MSFT', 'CRM', 'ORCL', 'SNOW', 'WDAY', 'ADBE', 'NOW', 'TEAM'],
    unreadEvents: 1,
    lastUpdated: '5小时前',
    tags: ['企业AI', 'SaaS'],
    category: '企业软件',
    status: 'completed',
    aiInsight: '受益：领先SaaS厂商(MSFT +12%, CRM +8%)、AI工具开发商、企业服务平台；受损：传统办公软件公司(-5-8%)、人工文档服务商；用户增长率150%验证了市场需求爆发，续费率达到95%。',
    newsUpdates: [
      { content: 'Copilot Pro企业版用户增长率达到150%，月活跃用户突破1200万', timestamp: '2小时前' },
      { content: '财富500强中已有78%企业部署Copilot，生产力提升45%', timestamp: '4小时前' },
      { content: '微软宣布Copilot for Teams功能，智能会议纪要准确率99%', timestamp: '6小时前' }
    ]
  },
  {
    type: 'idea',
    id: 3,
    title: '特斯拉FSD V13实现L4级自动驾驶，商业化应用在即',
    description: 'FSD V13在城市复杂路况测试中表现完美，无人工接管里程突破50000英里，自动驾驶出租车服务启动。神经网络参数增至1200亿，端到端学习能力大幅提升，安全性超越人类驾驶员。',
    stocks: ['TSLA', 'NVDA', 'AMD', 'INTC', 'QCOM'],
    allStocks: ['TSLA', 'NVDA', 'AMD', 'INTC', 'QCOM', 'MOBILEYE', 'GM', 'F'],
    unreadEvents: 3,
    lastUpdated: '1天前',
    tags: ['自动驾驶', '智能汽车'],
    category: '智能汽车',
    status: 'executing',
    aiInsight: '受益：自动驾驶技术公司(TSLA +18%)、AI芯片制造商(NVDA +10%)、激光雷达厂商；受损：传统出租车行业(-15%)、人工驾驶培训(-20%)、保险公司调整定价；奥斯汀试点成功，加速全球商业化进程。',
    newsUpdates: [
      { content: '马斯克宣布Robotaxi服务将在德州奥斯汀率先上线，投入1000辆车队', timestamp: '12小时前' },
      { content: 'FSD V13测试数据显示安全性提升500%，事故率降至人类驾驶员1/10', timestamp: '18小时前' },
      { content: '特斯拉与Uber达成合作，Robotaxi将接入Uber平台', timestamp: '1天前' }
    ]
  },
  {
    type: 'idea',
    id: 4,
    title: '苹果Vision Pro二代曝光，空间计算生态加速成型',
    description: 'Vision Pro 2将配备M4芯片和更轻薄设计，重量降至400g以下，续航提升至8小时。新增眼球追踪优化和手势识别增强，空间应用开发者生态快速扩张，企业级应用场景逐步落地。',
    stocks: ['AAPL', 'NVDA', 'QCOM', 'AMD', 'SONY'],
    allStocks: ['AAPL', 'NVDA', 'QCOM', 'AMD', 'SONY', 'META', 'MSFT', 'GOOGL'],
    unreadEvents: 1,
    lastUpdated: '3小时前',
    tags: ['AR/VR', '苹果', '空间计算'],
    category: '消费电子',
    status: 'draft',
    aiInsight: '受益：AR/VR硬件厂商(AAPL +8%)、芯片供应商(NVDA/QCOM +6%)、光学器件厂商；受损：传统PC厂商(-3%)、智能手机配件商(-2%)；开发者生态建设是关键，需关注应用落地速度。',
    newsUpdates: [
      { content: '苹果确认Vision Pro 2将于2025年Q2发布，价格下调至2499美元', timestamp: '1小时前' },
      { content: 'Vision Pro应用商店应用数量突破5000个，月活用户增长120%', timestamp: '4小时前' },
      { content: '迪士尼宣布为Vision Pro开发沉浸式主题乐园体验应用', timestamp: '6小时前' }
    ]
  },
  {
    type: 'idea',
    id: 5,
    title: 'Meta发布Llama 3.5开源模型，开源AI生态迎来新突破',
    description: 'Llama 3.5参数规模达到7000亿，在多项基准测试中超越GPT-4，完全开源免费商用。Meta同时发布Code Llama 3.5专门用于代码生成，开发者社区响应热烈，预计将重塑AI应用开发格局。',
    stocks: ['META', 'NVDA', 'AMD', 'MSFT', 'GOOGL'],
    allStocks: ['META', 'NVDA', 'AMD', 'MSFT', 'GOOGL', 'ORCL', 'CRM', 'AMZN'],
    unreadEvents: 2,
    lastUpdated: '4小时前',
    tags: ['开源AI', 'Meta', '大语言模型'],
    category: 'AI模型',
    status: 'executing',
    aiInsight: '受益：开源AI生态受益者(META +10%)、AI基础设施提供商(NVDA +8%)、云服务商；受损：闭源AI模型厂商(-5%)、AI API服务商(-8%)；开源策略或将重塑行业格局，关注商业模式变化。',
    newsUpdates: [
      { content: 'Llama 3.5发布24小时内下载量突破100万次，创开源模型记录', timestamp: '2小时前' },
      { content: '微软Azure宣布原生支持Llama 3.5部署，提供一键部署服务', timestamp: '5小时前' },
      { content: 'Hugging Face CEO称Llama 3.5是"开源AI的iPhone时刻"', timestamp: '7小时前' }
    ]
  }
]

// 推荐 Ideas 数据
export const recommendedIdeasData: IdeaItem[] = [
  {
    type: 'idea',
    id: 101,
    title: 'Grok-4即将发布，AI基础模型能力进一步提升',
    description: 'xAI即将发布Grok-4模型，预期在推理能力和多模态理解方面实现重大突破，将推动AI应用场景拓展。参数规模达到3万亿，多模态能力覆盖文本、图像、视频、音频，推理速度提升5倍。',
    stocks: ['NVDA', 'MSFT', 'GOOGL', 'META', 'TSLA'],
    allStocks: ['NVDA', 'MSFT', 'GOOGL', 'META', 'TSLA', 'AMZN', 'CRM', 'ORCL'],
    unreadEvents: 0,
    lastUpdated: '30分钟前',
    tags: ['AI模型', '大语言模型'],
    category: 'AI模型',
    status: 'executing',
    aiInsight: '受益：高性能芯片供应商(NVDA +12%)、云计算平台(MSFT +8%)、模型训练服务商；受损：传统AI应用厂商(-5%)、低端AI服务提供商；Grok-4测试数据显示超越GPT-4o达25%，强化马斯克AI战略布局。',
    newsUpdates: [
      { content: 'Grok-4测试版本性能指标曝光，推理能力超越GPT-4o达25%', timestamp: '15分钟前' },
      { content: 'xAI获得60亿美元新一轮融资，估值突破500亿美元', timestamp: '1小时前' },
      { content: '特斯拉宣布Grok-4将集成到车载系统，提供智能助手服务', timestamp: '2小时前' }
    ],
    isRecommended: true
  },
  {
    type: 'idea',
    id: 102,
    title: 'OpenAI发布Sora视频生成模型商业版本',
    description: 'Sora商业版即将上线，视频生成质量达到专业级别，内容创作行业面临变革。支持4K分辨率、60帧率，单次可生成60秒高质量视频，创作成本降低90%。',
    stocks: ['MSFT', 'ADBE', 'NVDA', 'NFLX', 'DIS'],
    allStocks: ['MSFT', 'ADBE', 'NVDA', 'NFLX', 'DIS', 'META', 'GOOGL', 'CRM'],
    unreadEvents: 0,
    lastUpdated: '1小时前',
    tags: ['AI视频', '内容创作'],
    category: 'AI视频',
    status: 'executing',
    aiInsight: '受益：AI视频技术公司、创意平台运营商(ADBE +10%)、视频内容平台(NFLX +6%)；受损：传统视频制作工作室(-15%)、后期制作公司(-12%)；月费$200定价偏高但企业接受度良好，预计年内降价推广。',
    newsUpdates: [
      { content: 'Sora Pro版本定价公布，月费$200瞄准专业市场', timestamp: '45分钟前' },
      { content: 'Adobe宣布与OpenAI合作，Sora将集成到Premiere Pro', timestamp: '3小时前' },
      { content: 'Netflix试用Sora生成预告片，效果获得业界认可', timestamp: '5小时前' }
    ],
    isRecommended: true
  }
]

// Idea 详情数据 (包含更详细信息的映射)
export const ideaDetailsData: Record<string, IdeaDetail> = {
  '1': {
    id: '1',
    title: 'NVIDIA H200超级芯片量产，AI训练效率革命性提升',
    description: 'H200芯片相比H100性能提升90%，内存容量翻倍达到141GB，各大云服务商争相采购。HBM3e内存技术首次商用，带宽达到4.8TB/s，大模型训练成本降低30%。',
    createdAt: '2024-01-15',
    tags: ['AI芯片', '英伟达', '产业链分析'],
    status: 'completed',
    content: {
      summary: 'NVIDIA H200芯片的发布标志着AI训练进入新纪元，性能提升90%的同时成本降低30%，将加速AI应用的普及和发展。',
      keyPoints: [
        'H200相比H100性能提升90%，内存容量翻倍达到141GB',
        'HBM3e内存技术首次商用，带宽达到4.8TB/s',
        '大模型训练成本降低30%，提高AI应用商业化可行性',
        '云服务商争相采购，预计供不应求状态将持续至少12个月'
      ],
      riskAssessment: '主要风险包括地缘政治影响芯片供应、竞争对手技术追赶、以及估值过高的调整风险。',
      recommendation: '基于AI革命的持续推进和数据中心需求的强劲增长，建议长期持有NVDA。短期内可能面临估值过高的调整风险，建议分批建仓。'
    }
  },
  '2': {
    id: '2',
    title: '微软Copilot全面集成Office365，企业AI办公时代到来',
    description: 'Copilot在Word、Excel、PPT中实现深度集成，企业订阅用户数突破千万，生产力革命正在发生。智能文档生成、数据分析自动化、PPT设计AI化，办公效率提升平均达到45%。',
    createdAt: '2024-01-14',
    tags: ['企业AI', 'Microsoft', 'SaaS'],
    status: 'completed',
    content: {
      summary: '微软Copilot的全面集成正在重新定义企业办公方式，用户增长率150%验证了市场需求的爆发。',
      keyPoints: [
        'Copilot Pro企业版用户增长率达到150%，月活跃用户突破1200万',
        '财富500强中已有78%企业部署Copilot，生产力提升45%',
        '智能会议纪要准确率99%，大幅减少人工整理工作',
        '续费率达到95%，显示用户对产品价值的认可'
      ],
      riskAssessment: '面临云业务竞争激烈、监管政策风险以及经济周期影响的挑战。',
      recommendation: '云业务和AI产品线的强劲表现支撑长期增长，估值相对合理，适合稳健投资者长期配置。'
    }
  },
  '3': {
    id: '3',
    title: '特斯拉FSD V13实现L4级自动驾驶，商业化应用在即',
    description: 'FSD V13在城市复杂路况测试中表现完美，无人工接管里程突破50000英里，自动驾驶出租车服务启动。神经网络参数增至1200亿，端到端学习能力大幅提升，安全性超越人类驾驶员。',
    createdAt: '2024-01-13',
    tags: ['自动驾驶', '特斯拉', '智能汽车'],
    status: 'completed',
    content: {
      summary: '特斯拉FSD V13实现L4级自动驾驶突破，奥斯汀试点成功为全球商业化铺平道路。',
      keyPoints: [
        'FSD V13测试数据显示安全性提升500%，事故率降至人类驾驶员1/10',
        'Robotaxi服务将在德州奥斯汀率先上线，投入1000辆车队',
        '神经网络参数增至1200亿，端到端学习能力大幅提升',
        '与Uber达成合作，Robotaxi将接入Uber平台扩大服务范围'
      ],
      riskAssessment: '估值波动较大，竞争加剧，生产交付存在不确定性风险。',
      recommendation: '电动车领域领导地位稳固，但竞争加剧，估值波动较大，适合风险承受能力较强的投资者。'
    }
  },
  '4': {
    id: '4',
    title: '苹果Vision Pro二代曝光，空间计算生态加速成型',
    description: 'Vision Pro 2将配备M4芯片和更轻薄设计，重量降至400g以下，续航提升至8小时。新增眼球追踪优化和手势识别增强，空间应用开发者生态快速扩张，企业级应用场景逐步落地。',
    createdAt: '2024-01-16',
    tags: ['AR/VR', '苹果', '空间计算'],
    status: 'completed',
    content: {
      summary: 'Vision Pro 2的技术突破将推动空间计算从概念走向主流，企业级应用场景快速落地。',
      keyPoints: [
        'M4芯片加持下性能提升40%，功耗降低25%',
        '重量从650g降至400g以下，佩戴体验大幅改善',
        '开发者生态快速扩张，应用数量突破5000个',
        '企业级培训、设计、协作应用场景逐步成熟'
      ],
      riskAssessment: '消费者接受度仍需验证，高价格门槛限制普及速度，需关注竞争对手产品。',
      recommendation: '空间计算代表未来趋势，苹果生态优势明显，建议关注硬件成本下降和应用生态发展。'
    }
  },
  '5': {
    id: '5',
    title: 'Meta发布Llama 3.5开源模型，开源AI生态迎来新突破',
    description: 'Llama 3.5参数规模达到7000亿，在多项基准测试中超越GPT-4，完全开源免费商用。Meta同时发布Code Llama 3.5专门用于代码生成，开发者社区响应热烈，预计将重塑AI应用开发格局。',
    createdAt: '2024-01-16',
    tags: ['开源AI', 'Meta', '大语言模型'],
    status: 'completed',
    content: {
      summary: 'Llama 3.5的发布标志着开源AI达到新高度，有望重塑AI应用开发和商业化格局。',
      keyPoints: [
        '7000亿参数规模，在多项基准测试中超越GPT-4',
        '完全开源免费商用，降低AI应用开发门槛',
        'Code Llama 3.5专攻代码生成，开发效率提升显著',
        '24小时内下载量突破100万次，创开源模型记录'
      ],
      riskAssessment: '开源策略可能冲击现有商业模式，Meta需要新的变现路径，监管风险依然存在。',
      recommendation: 'Meta的开源战略长远看有利于构建AI生态护城河，短期内关注其元宇宙和广告业务表现。'
    }
  },
  '101': {
    id: '101',
    title: 'Grok-4即将发布，AI基础模型能力进一步提升',
    description: 'xAI即将发布Grok-4模型，预期在推理能力和多模态理解方面实现重大突破，将推动AI应用场景拓展。参数规模达到3万亿，多模态能力覆盖文本、图像、视频、音频，推理速度提升5倍。',
    createdAt: '2024-01-16',
    tags: ['AI模型', '大语言模型'],
    status: 'executing'
  },
  '102': {
    id: '102',
    title: 'OpenAI发布Sora视频生成模型商业版本',
    description: 'Sora商业版即将上线，视频生成质量达到专业级别，内容创作行业面临变革。支持4K分辨率、60帧率，单次可生成60秒高质量视频，创作成本降低90%。',
    createdAt: '2024-01-16',
    tags: ['AI视频', '内容创作'],
    status: 'executing'
  }
}

// 辅助函数：根据 ID 获取所有 ideas (包括我的和推荐的)
export function getAllIdeas(): IdeaItem[] {
  return [...myIdeasData, ...recommendedIdeasData]
}

// 辅助函数：根据 ID 查找 idea
export function findIdeaById(id: string | number): IdeaItem | undefined {
  const numId = typeof id === 'string' ? parseInt(id) : id
  return getAllIdeas().find(idea => idea.id === numId)
}