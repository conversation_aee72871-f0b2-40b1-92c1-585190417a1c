/**
 * AI Idea Feature 类型定义
 */

// Idea 输入数据结构
export interface IdeaInput {
  prompt: string;                    // 用户输入的自然语言描述
  attachments?: File[];              // 上传的附件
  enableFactCheck?: boolean;         // 是否启用深度核实
  context?: {                        // 上下文信息
    currentIdeas?: any[];            // 当前已有的 Ideas
    userPreferences?: any;           // 用户偏好
  };
}

// Idea 完整数据结构
export interface Idea {
  id: string;                        // Idea 唯一标识
  title: string;                     // Idea 标题
  prompt: string;                    // 原始用户输入
  status: 'draft' | 'executing' | 'completed' | 'error'; // 状态
  enableFactCheck: boolean;          // 是否启用深度核实
  content?: {                        // AI 分析生成的内容
    summary?: string;                // 分析摘要
    keyPoints?: string[];            // 关键点
    riskAssessment?: string;         // 风险评估
    recommendation?: string;         // 投资建议
  };
  attachments?: string[];            // 附件URL列表
  createdAt: string;                 // 创建时间
  updatedAt: string;                 // 更新时间
}

// Idea 创建模态框 Props
export interface IdeaCreationModalProps {
  open?: boolean;                    // 是否显示模态框
  onClose?: () => void;             // 关闭回调
  onSubmit?: (idea: Idea) => void;  // 提交回调 - 传递完整的 Idea 对象
  className?: string;               // 自定义样式
}

// AI 输入框 Props
export interface AIPromptInputProps {
  value?: string;                   // 输入值
  onChange?: (value: string) => void; // 输入变化回调
  placeholder?: string;             // 占位符
  className?: string;               // 自定义样式
  disabled?: boolean;               // 是否禁用
}

// 示例提示 Props
export interface ExamplePromptsProps {
  onSelect?: (prompt: string) => void; // 选择回调
  className?: string;               // 自定义样式
}

// 示例提示数据结构
export interface ExamplePrompt {
  id: string;
  title: string;
  description?: string;
}