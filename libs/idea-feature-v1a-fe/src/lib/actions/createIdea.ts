'use server'

import type { IdeaInput, Idea } from '../types'
import { myIdeasData, ideaDetailsData } from '../data/mockIdeas'
import type { IdeaItem } from './getIdeaList'
import type { IdeaDetail } from './getIdeaById'

/**
 * 创建新的 Idea
 * 这是插件内部的 server action，负责处理完整的 idea 创建流程
 */
export async function createIdea(input: IdeaInput): Promise<Idea> {
  try {
    // TODO: 集成实际的后端 API
    // 这里需要调用后端服务来创建 idea 并进行 AI 分析
    
    // 生成唯一的数字 ID（与现有 mock 数据格式一致）
    const ideaId = Date.now()
    
    // 根据 prompt 生成标题（简化版本，实际应该用 AI 生成）
    const title = input.prompt.length > 50 
      ? input.prompt.substring(0, 50) + '...' 
      : input.prompt
    
    // 模拟 API 调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 创建 IdeaItem 对象（添加到列表中）
    const newIdeaItem: IdeaItem = {
      type: 'idea',
      id: ideaId,
      title,
      description: input.prompt,
      stocks: [],
      allStocks: [],
      unreadEvents: 0,
      lastUpdated: '刚刚',
      tags: [],
      category: '新建',
      status: 'executing',
      aiInsight: '正在分析中，请稍候...',
      newsUpdates: []
    }
    
    // 创建 IdeaDetail 对象（用于详情页）
    const newIdeaDetail: IdeaDetail = {
      id: String(ideaId),
      title,
      description: input.prompt,
      createdAt: new Date().toISOString().split('T')[0],
      tags: [],
      status: 'executing', // 统一使用 executing 状态
      content: {
        summary: '正在分析中，请稍候...',
        keyPoints: [],
        riskAssessment: '',
        recommendation: ''
      }
    }
    
    // 创建返回的 Idea 对象
    const newIdea: Idea = {
      id: String(ideaId),
      title,
      prompt: input.prompt,
      status: 'executing', // 初始状态为执行中
      enableFactCheck: input.enableFactCheck || false,
      content: {
        summary: '正在分析中，请稍候...',
        keyPoints: [],
        riskAssessment: '',
        recommendation: ''
      },
      attachments: input.attachments?.map(file => file.name) || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    // 添加到 mock 数据中
    myIdeasData.unshift(newIdeaItem) // 添加到列表顶部
    ideaDetailsData[String(ideaId)] = newIdeaDetail // 添加到详情数据
    
    // TODO: 在实际实现中，这里应该：
    // 1. 调用后端 API 创建 idea 记录
    // 2. 启动 AI 分析流程
    // 3. 如果启用了 fact check，启动核实流程
    // 4. 返回真实的 idea 对象
    
    console.log('Created idea:', newIdea)
    console.log('Added to myIdeasData, new length:', myIdeasData.length)
    console.log('Added to ideaDetailsData with key:', String(ideaId))
    
    return newIdea
    
  } catch (error) {
    console.error('Failed to create idea:', error)
    throw new Error('创建 Idea 失败，请稍后重试')
  }
}