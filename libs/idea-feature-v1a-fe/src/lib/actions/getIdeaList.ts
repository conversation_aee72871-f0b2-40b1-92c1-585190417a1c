'use server'

import { myIdeasData, recommendedIdeasData } from '../data/mockIdeas'

// 定义 Idea 项目的类型接口
export interface IdeaItem {
  type: 'idea'
  id: number
  title: string
  description: string
  stocks: string[]
  allStocks: string[]
  unreadEvents: number
  lastUpdated: string
  tags: string[]
  category: string
  aiInsight: string
  newsUpdates: Array<{
    content: string
    timestamp: string
  }>
  status: 'draft' | 'executing' | 'completed'
  isRecommended?: boolean
}

export interface IdeaListResponse {
  myIdeas: IdeaItem[]
  recommendedIdeas: IdeaItem[]
}

/**
 * 获取 Idea 列表数据
 * 包括我的 Ideas 和推荐 Ideas
 */
export async function getIdeaList(): Promise<IdeaListResponse> {
  try {
    // TODO: 集成实际的后端 API
    // 这里需要调用后端服务来获取真实的 idea 数据

    // 模拟 API 调用延迟
    await new Promise(resolve => setTimeout(resolve, 100))

    const response: IdeaListResponse = {
      myIdeas: myIdeasData,
      recommendedIdeas: recommendedIdeasData
    }

    console.log('获取 Idea 列表:', { myIdeas: myIdeasData.length, recommendedIdeas: recommendedIdeasData.length })
    
    return response
    
  } catch (error) {
    console.error('获取 Idea 列表失败:', error)
    throw new Error('获取 Idea 列表失败，请稍后重试')
  }
}