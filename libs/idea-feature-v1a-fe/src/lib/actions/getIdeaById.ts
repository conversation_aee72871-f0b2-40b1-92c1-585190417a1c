'use server'

import { ideaDetailsData } from '../data/mockIdeas'

export interface IdeaDetail {
  id: string
  title: string
  description: string
  createdAt: string
  tags: string[]
  status: 'draft' | 'executing' | 'completed' | 'error'
  content?: {
    summary?: string
    keyPoints?: string[]
    riskAssessment?: string
    recommendation?: string
  }
}

/**
 * 根据 ideaId 获取单个 Idea 的详细信息
 */
export async function getIdeaById(ideaId: string): Promise<IdeaDetail | null> {
  try {
    // TODO: 集成实际的后端 API
    // 这里需要调用后端服务来获取真实的 idea 详情数据

    // 模拟 API 调用延迟
    await new Promise(resolve => setTimeout(resolve, 100))

    const ideaDetail = ideaDetailsData[ideaId]

    if (!ideaDetail) {
      console.log('未找到指定的 Idea:', ideaId)
      return null
    }

    console.log('获取 Idea 详情:', { id: ideaId, title: ideaDetail.title, status: ideaDetail.status })
    
    return ideaDetail
    
  } catch (error) {
    console.error('获取 Idea 详情失败:', error)
    throw new Error(`获取 Idea ${ideaId} 详情失败，请稍后重试`)
  }
}