'use client'

import * as React from "react"
import { useState, useEffect } from "react"
import { ArrowLeft, Calendar, Tag } from "lucide-react"
import { cn } from "@yai-investor-insight/shared-fe-kit"

import { AiThinkingFlow } from "./AiThinkingFlow"
import { PersonalIdeaFlow } from "./PersonalIdeaFlow"
import { StockRecommendations } from "./StockRecommendations"
import { FactCheckingFlow } from "./FactCheckingFlow"
import { getIdeaById, type IdeaDetail } from "../actions"

interface IdeaDetailEmbeddedProps {
  ideaId: string
  onBack?: () => void
  className?: string
}

export function IdeaDetailEmbedded({
  ideaId,
  onBack,
  className
}: IdeaDetailEmbeddedProps) {
  const [ideaData, setIdeaData] = useState<IdeaDetail | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'analysis' | 'thoughts' | 'stocks'>('analysis')

  useEffect(() => {
    const loadIdeaDetail = async () => {
      try {
        setIsLoading(true)
        setError(null)
        const data = await getIdeaById(ideaId)
        if (data) {
          setIdeaData(data)
        } else {
          setError('未找到指定的 Idea')
        }
      } catch (err) {
        console.error('加载 Idea 详情失败:', err)
        setError(err instanceof Error ? err.message : '加载失败')
      } finally {
        setIsLoading(false)
      }
    }

    loadIdeaDetail()
  }, [ideaId])

  if (isLoading) {
    return (
      <div className={cn("w-full", className)}>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">正在加载 Idea 详情...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={cn("w-full", className)}>
        <div className="flex items-center justify-center h-64">
          <div className="text-red-500">{error}</div>
        </div>
      </div>
    )
  }

  if (!ideaData) {
    return (
      <div className={cn("w-full", className)}>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">未找到 Idea 数据</div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("w-full", className)}>
      {/* Header - 简化版本，不包含外层背景 */}
      <header className="border-b bg-white">
        <div className="px-6 py-4">
          <div className="flex items-center gap-4">
            <button
              onClick={onBack}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {ideaData.title}
              </h1>
              
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{ideaData.createdAt}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Tag className="h-4 w-4" />
                  <div className="flex gap-2">
                    {ideaData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-blue-100 text-blue-700 rounded-md text-xs"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    ideaData.status === 'executing' ? 'bg-yellow-500 animate-pulse' :
                    ideaData.status === 'completed' ? 'bg-green-500' : 'bg-red-500'
                  )}></div>
                  <span className="text-xs">
                    {ideaData.status === 'executing' ? 'AI 分析中' :
                     ideaData.status === 'completed' ? '分析完成' : '分析失败'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white border-b">
        <div className="px-6">
          <div className="flex gap-8">
            {[
              { key: 'analysis', label: 'AI 推演分析' },
              { key: 'thoughts', label: '个人想法' },
              { key: 'stocks', label: '相关标的' }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={cn(
                  "py-4 px-2 border-b-2 font-medium text-sm transition-colors",
                  activeTab === tab.key
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                )}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content - 移除外层的 max-width 和背景色 */}
      <main className="px-6 py-8">
        <div className="grid grid-cols-1 gap-8">
          {/* Description */}
          <div className="bg-white rounded-lg border p-6">
            <h2 className="text-lg font-semibold mb-3">投资想法描述</h2>
            <p className="text-gray-700 leading-relaxed">
              {ideaData.description}
            </p>
          </div>

          {/* Fact Checking Research */}
          <FactCheckingFlow ideaId={ideaId} />

          {/* Tab Content */}
          <div className="min-h-[600px]">
            {activeTab === 'analysis' && <AiThinkingFlow ideaId={ideaId} />}
            {activeTab === 'thoughts' && <PersonalIdeaFlow ideaId={ideaId} />}
            {activeTab === 'stocks' && <StockRecommendations ideaId={ideaId} />}
          </div>
        </div>
      </main>
    </div>
  )
}