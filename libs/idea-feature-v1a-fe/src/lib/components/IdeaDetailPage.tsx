'use client'

import * as React from "react"
import { useState } from "react"
import { ArrowLeft, Calendar, Tag, User } from "lucide-react"
import { cn } from "@yai-investor-insight/shared-fe-kit"

import { AiThinkingFlow } from "./AiThinkingFlow"
import { PersonalIdeaFlow } from "./PersonalIdeaFlow"
import { StockRecommendations } from "./StockRecommendations"
import { FactCheckingFlow } from "./FactCheckingFlow"

interface IdeaDetailPageProps {
  ideaId: string
  onBack?: () => void
  className?: string
}

export function IdeaDetailPage({
  ideaId,
  onBack,
  className
}: IdeaDetailPageProps) {
  // TODO: 集成后端 API - 根据 ideaId 从 API 加载真实的 idea 数据
  // 包括 idea 的基本信息、AI 分析结果、用户添加的想法等
  const [ideaData] = useState({ // 临时使用模拟数据
    id: ideaId,
    title: "英伟达 H200 芯片发布对 AI 产业链的影响分析",
    description: "分析英伟达最新发布的 H200 芯片对整个 AI 产业链的影响，包括上游供应商、下游应用厂商以及相关投资机会。",
    createdAt: "2024-01-15",
    tags: ["AI芯片", "英伟达", "产业链分析"],
    status: "generating" as const // generating | completed | failed
  })

  const [activeTab, setActiveTab] = useState<'analysis' | 'thoughts' | 'stocks'>('analysis')

  return (
    <div className={cn("min-h-screen bg-gray-50", className)}>
      {/* Header */}
      <header className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center gap-4">
            <button
              onClick={onBack}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {ideaData.title}
              </h1>
              
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{ideaData.createdAt}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Tag className="h-4 w-4" />
                  <div className="flex gap-2">
                    {ideaData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-blue-100 text-blue-700 rounded-md text-xs"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    ideaData.status === 'generating' ? 'bg-yellow-500 animate-pulse' :
                    ideaData.status === 'completed' ? 'bg-green-500' : 'bg-red-500'
                  )}></div>
                  <span className="text-xs">
                    {ideaData.status === 'generating' ? 'AI 分析中' :
                     ideaData.status === 'completed' ? '分析完成' : '分析失败'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex gap-8">
            {[
              { key: 'analysis', label: 'AI 推演分析' },
              { key: 'thoughts', label: '个人想法' },
              { key: 'stocks', label: '相关标的' }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={cn(
                  "py-4 px-2 border-b-2 font-medium text-sm transition-colors",
                  activeTab === tab.key
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                )}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 gap-8">
          {/* Description */}
          <div className="bg-white rounded-lg border p-6">
            <h2 className="text-lg font-semibold mb-3">投资想法描述</h2>
            <p className="text-gray-700 leading-relaxed">
              {ideaData.description}
            </p>
          </div>

          {/* Fact Checking Research */}
          <FactCheckingFlow ideaId={ideaId} />

          {/* Tab Content */}
          <div className="min-h-[600px]">
            {activeTab === 'analysis' && <AiThinkingFlow ideaId={ideaId} />}
            {activeTab === 'thoughts' && <PersonalIdeaFlow ideaId={ideaId} />}
            {activeTab === 'stocks' && <StockRecommendations ideaId={ideaId} />}
          </div>
        </div>
      </main>
    </div>
  )
}