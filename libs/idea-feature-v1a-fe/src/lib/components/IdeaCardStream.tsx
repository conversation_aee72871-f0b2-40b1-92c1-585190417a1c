'use client'

import { useState, useMemo, useEffect } from "react";
import { useRouter } from "next/navigation";
import { IdeaCard } from './IdeaCard';
import { TagFilter } from './TagFilter';
import { getIdeaList, type IdeaItem, type IdeaListResponse } from '../actions';

interface IdeaCardStreamProps {
  onCardClick?: (item: { id: string | number }) => void;
  searchQuery?: string;
}

export function IdeaCardStream({ onCardClick }: IdeaCardStreamProps) {
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [ideaData, setIdeaData] = useState<IdeaListResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const loadIdeas = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await getIdeaList();
        setIdeaData(data);
      } catch (err) {
        console.error('加载 Ideas 失败:', err);
        setError(err instanceof Error ? err.message : '加载失败');
      } finally {
        setIsLoading(false);
      }
    };

    loadIdeas();
  }, []);

  const handleCardClick = (item: IdeaItem) => {
    if (onCardClick) {
      onCardClick(item);
    } else {
      // 默认导航行为
      if (item.type === 'idea') {
        router.push(`/workbench-v1a/ideas/${item.id}`);
      }
    }
  };

  // 提取所有推荐ideas的标签
  const allRecommendedTags = useMemo(() => {
    if (!ideaData) return [];
    const tags = new Set<string>();
    ideaData.recommendedIdeas.forEach(idea => {
      idea.tags.forEach(tag => tags.add(tag));
    });
    return Array.from(tags).sort();
  }, [ideaData]);

  // 根据选中的标签筛选推荐ideas
  const filteredRecommendedIdeas = useMemo(() => {
    if (!ideaData) return [];
    if (selectedTags.length === 0) {
      return ideaData.recommendedIdeas;
    }
    // 由于改为单选模式，只取第一个选中的标签
    const selectedTag = selectedTags[0];
    return ideaData.recommendedIdeas.filter(idea => 
      idea.tags.includes(selectedTag)
    );
  }, [selectedTags, ideaData]);

  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">正在加载 Ideas...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-red-500">{error}</div>
        </div>
      </div>
    );
  }

  if (!ideaData) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">暂无 Ideas 数据</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 我的 Ideas */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">我的 Ideas</h2>
          <div className="text-sm text-muted-foreground">
            共 {ideaData.myIdeas.length} 个想法追踪中
          </div>
        </div>
        
        <div className="grid gap-6 lg:grid-cols-2">
          {ideaData.myIdeas.map((item) => (
            <IdeaCard
              key={`my-idea-${item.id}`}
              idea={item}
              onClick={() => handleCardClick(item)}
            />
          ))}
        </div>
      </div>

      {/* 推荐 Ideas */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">市场推荐</h2>
          <div className="text-sm text-muted-foreground">
            发现更多投资机会 · 共 {filteredRecommendedIdeas.length} 个
          </div>
        </div>
        
        {/* 标签筛选 */}
        <div className="flex items-center gap-6 pb-4 border-b border-slate-200">
          <TagFilter
            allTags={allRecommendedTags}
            selectedTags={selectedTags}
            onTagChange={setSelectedTags}
          />
        </div>
        
        <div className="grid gap-6 lg:grid-cols-2">
          {filteredRecommendedIdeas.map((item) => (
            <IdeaCard
              key={`recommended-idea-${item.id}`}
              idea={item}
              onClick={() => handleCardClick(item)}
              isRecommended={true}
            />
          ))}
        </div>
        
        {filteredRecommendedIdeas.length === 0 && selectedTags.length > 0 && (
          <div className="text-center py-12 text-muted-foreground">
            <p>没有找到符合筛选条件的Ideas</p>
            <p className="text-sm mt-2">试试调整筛选标签或清除全部筛选</p>
          </div>
        )}
      </div>
    </div>
  );
}