import * as React from "react"
import { LightBulbIcon } from "@heroicons/react/24/outline"
import { cn } from "@yai-investor-insight/shared-fe-kit"
import type { ExamplePromptsProps, ExamplePrompt } from "../types"

const examplePrompts: ExamplePrompt[] = [
  {
    id: "1",
    title: "分析苹果发布 Vision Pro 对供应链公司的影响",
  },
  {
    id: "2", 
    title: "英伟达财报超预期，相关概念股投资机会分析",
  },
  {
    id: "3",
    title: "美联储加息对房地产板块的影响及投资策略",
  },
  {
    id: "4",
    title: "新能源汽车政策变化对产业链的影响",
  }
]

export function ExamplePrompts({
  onSelect,
  className
}: ExamplePromptsProps) {
  const handleSelect = (prompt: string) => {
    onSelect?.(prompt)
  }

  return (
    <div className={cn("space-y-3", className)}>
      <div className="flex items-center gap-2 text-sm font-medium text-[hsl(220_13%_46%)]">
        <LightBulbIcon className="h-4 w-4" />
        <span>快速开始：</span>
      </div>
      
      <div className="space-y-2">
        {examplePrompts.map((prompt) => (
          <button
            key={prompt.id}
            onClick={() => handleSelect(prompt.title)}
            className={cn(
              "w-full text-left p-3 text-sm rounded-lg border border-transparent",
              "text-[hsl(220_13%_46%)] hover:text-[hsl(220_20%_15%)]",
              "hover:bg-[hsl(220_14%_96%)] hover:border-[hsl(220_13%_91%)]",
              "workbench-transition-smooth",
              "hover:shadow-sm hover:transform hover:scale-[1.01]"
            )}
          >
            • {prompt.title}
          </button>
        ))}
      </div>
    </div>
  )
}