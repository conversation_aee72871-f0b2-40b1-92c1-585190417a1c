'use client'

import * as React from "react"
import { useState } from "react"
import { CheckCircleIcon, PaperClipIcon } from "@heroicons/react/24/outline"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "./ui/dialog"
import { Button } from "./ui/button"
import { AIPromptInput } from "./AIPromptInput"
import { cn } from "@yai-investor-insight/shared-fe-kit"
import type { IdeaCreationModalProps, IdeaInput } from "../types"
import { createIdea } from "../actions"

export function IdeaCreationModal({
  open = false,
  onClose,
  onSubmit,
  className
}: IdeaCreationModalProps) {
  const [prompt, setPrompt] = useState("")
  const [enableFactCheck, setEnableFactCheck] = useState(false)
  const [attachments, setAttachments] = useState<File[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)


  const handleSubmit = async () => {
    if (!prompt.trim() || isLoading) return

    try {
      setIsLoading(true)
      setError(null)

      const ideaInput: IdeaInput = {
        prompt: prompt.trim(),
        attachments: attachments.length > 0 ? attachments : undefined,
        enableFactCheck,
        context: {
          // 这里可以添加上下文信息
        }
      }

      // 调用插件内的 server action 创建 Idea
      const newIdea = await createIdea(ideaInput)
      
      // 传递完整的 Idea 对象给父组件
      onSubmit?.(newIdea)
      
      // 重置表单
      setPrompt("")
      setEnableFactCheck(false)
      setAttachments([])
      
    } catch (err) {
      console.error('Failed to create idea:', err)
      setError(err instanceof Error ? err.message : '创建失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    if (isLoading) return // 加载中不允许关闭
    
    // 重置表单
    setPrompt("")
    setEnableFactCheck(false) 
    setAttachments([])
    setError(null)
    onClose?.()
  }

  const isSubmitDisabled = !prompt.trim() || isLoading

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className={cn(
        "max-w-2xl max-h-[80vh] overflow-y-auto",
        "workbench-card workbench-transition-smooth",
        "bg-gradient-to-br from-white to-gray-50",
        "border-[hsl(220_13%_91%)] shadow-lg",
        className
      )}>
        <DialogHeader className="text-center space-y-4 pb-4">
          <DialogTitle className="text-2xl font-bold text-[hsl(220_20%_15%)]">
            From event to edge in one search
          </DialogTitle>
          
          <DialogDescription className="text-base text-[hsl(220_13%_46%)] leading-relaxed">
            基于AI驱动的事件分析，快速验证事实、预测影响、构建投资逻辑
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-2">
          {/* 错误提示 */}
          {error && (
            <div className="p-3 rounded-lg bg-red-50 border border-red-200 text-red-700 text-sm">
              {error}
            </div>
          )}
          
          {/* 主输入区域 */}
          <AIPromptInput
            value={prompt}
            onChange={setPrompt}
            placeholder="🔍 输入您想要分析的市场事件或投资信息...

例如：分析苹果发布 Vision Pro 对供应链公司的影响"
            className="w-full"
            disabled={isLoading}
          />

          {/* 选项区域 */}
          <div className="flex items-center gap-8">
            <button
              onClick={() => setEnableFactCheck(!enableFactCheck)}
              disabled={isLoading}
              className={cn(
                "flex items-center gap-2 text-sm workbench-transition-smooth px-3 py-2 rounded-lg border",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                enableFactCheck 
                  ? "text-[hsl(240_50%_20%)] font-medium bg-[hsl(240_100%_80%/0.1)] border-[hsl(240_100%_80%/0.3)] shadow-sm" 
                  : "text-[hsl(220_13%_46%)] hover:text-[hsl(220_20%_15%)] bg-[hsl(220_14%_98%)] hover:bg-[hsl(220_14%_96%)] border-[hsl(220_13%_91%)] hover:border-[hsl(220_13%_85%)]"
              )}
            >
              <CheckCircleIcon className={cn(
                "h-4 w-4 workbench-transition-smooth",
                enableFactCheck ? "text-[hsl(240_50%_20%)]" : "text-[hsl(220_13%_46%)]"
              )} />
              <span>深度核实</span>
            </button>

            <button
              className={cn(
                "flex items-center gap-2 text-sm text-[hsl(220_13%_46%)] hover:text-[hsl(220_20%_15%)] bg-[hsl(220_14%_98%)] hover:bg-[hsl(220_14%_96%)] px-3 py-2 rounded-lg border border-[hsl(220_13%_91%)] hover:border-[hsl(220_13%_85%)] workbench-transition-smooth hover:shadow-sm",
                "disabled:opacity-50 disabled:cursor-not-allowed"
              )}
              disabled={isLoading}
              onClick={() => {
                // TODO: 实现附件上传功能
                console.log('Upload attachment')
              }}
            >
              <PaperClipIcon className="h-4 w-4" />
              <span>附件 ({attachments.length})</span>
            </button>
          </div>

        </div>

        <DialogFooter className="flex justify-end gap-4 pt-6">
          <Button 
            variant="outline" 
            onClick={handleClose}
            disabled={isLoading}
            className="px-6 py-2.5 border-[hsl(220_13%_91%)] text-[hsl(220_20%_15%)] hover:bg-[hsl(220_14%_96%)] workbench-transition-smooth disabled:opacity-50 disabled:cursor-not-allowed"
          >
            取消
          </Button>
          
          <Button 
            onClick={handleSubmit}
            disabled={isSubmitDisabled}
            className={cn(
              "px-8 py-2.5 workbench-button-primary workbench-transition-smooth",
              "bg-gradient-to-r from-[hsl(240_50%_20%)] to-[hsl(260_50%_25%)]",
              "text-white font-medium shadow-lg",
              "hover:shadow-xl hover:-translate-y-0.5",
              "disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-md"
            )}
          >
            {isLoading ? "创建中..." : "开始分析"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}