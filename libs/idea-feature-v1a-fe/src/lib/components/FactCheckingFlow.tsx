'use client'

import * as React from "react"
import { useState } from "react"
import { Button } from "./ui/button"
import { Badge } from "./ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card"
import { 
  RefreshCw, 
  Search, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  TrendingUp,
  TrendingDown,
  Minus,
  ExternalLink,
  Shield,
  Activity
} from "lucide-react"
import { cn } from "@yai-investor-insight/shared-fe-kit"

interface FactCheckItem {
  id: string
  claim: string
  status: "verified" | "questionable" | "false" | "pending"
  confidence: number
  source: string
  details: string
  lastUpdated: string
}

interface NewsItem {
  id: string
  title: string
  source: string
  url: string
  publishedAt: string
  sentiment: "positive" | "negative" | "neutral"
  relevance: number
  summary: string
}

interface KeyMetric {
  id: string
  name: string
  currentValue: string
  verified: boolean
  sources: string[]
  trend: "up" | "down" | "stable"
  accuracy: number
}

interface RiskFactor {
  id: string
  factor: string
  severity: "low" | "medium" | "high"
  description: string
  impact: string
  likelihood: number
}

interface FactCheckingFlowProps {
  ideaId: string
  isReadOnly?: boolean
}

export function FactCheckingFlow({
  ideaId,
  isReadOnly = false
}: FactCheckingFlowProps) {
  // TODO: 集成后端 API - 从 API 加载真实的事实核查数据
  const [factChecks, setFactChecks] = useState<FactCheckItem[]>([
    {
      id: "fact-1",
      claim: "英伟达 H200 采用 HBM3e 内存技术",
      status: "verified",
      confidence: 95,
      source: "英伟达官方发布",
      details: "已通过官方发布会和技术文档确认，H200 确实搭载 HBM3e 内存，提供 4.8TB/s 的内存带宽。",
      lastUpdated: "2小时前"
    },
    {
      id: "fact-2", 
      claim: "相比前代产品性能提升近2倍",
      status: "questionable",
      confidence: 70,
      source: "第三方评测机构",
      details: "基于初步基准测试，在特定工作负载下确实有显著提升，但'近2倍'的表述需要更多验证。",
      lastUpdated: "6小时前"
    },
    {
      id: "fact-3",
      claim: "云服务商已开始大规模采购",
      status: "verified",
      confidence: 85,
      source: "多家云服务商公告",
      details: "AWS、Google Cloud、Microsoft Azure 均已公开表示将在数据中心部署 H200。",
      lastUpdated: "1天前"
    }
  ])

  const [newsItems, setNewsItems] = useState<NewsItem[]>([
    {
      id: "news-1",
      title: "英伟达 H200 GPU 正式发布，AI 算力再次突破",
      source: "科技日报",
      url: "#",
      publishedAt: "2天前",
      sentiment: "positive",
      relevance: 95,
      summary: "详细介绍了 H200 的技术规格和市场预期，对英伟达股价产生积极影响。"
    },
    {
      id: "news-2",
      title: "美国考虑进一步限制 AI 芯片出口政策",
      source: "华尔街日报",
      url: "#",
      publishedAt: "1天前", 
      sentiment: "negative",
      relevance: 80,
      summary: "可能影响英伟达在中国市场的销售，存在一定的政策风险。"
    },
    {
      id: "news-3",
      title: "AI 大模型训练需求持续增长，芯片供不应求",
      source: "财经周刊",
      url: "#",
      publishedAt: "3天前",
      sentiment: "positive", 
      relevance: 90,
      summary: "市场对高性能 AI 芯片的需求持续旺盛，有利于英伟达产品销售。"
    }
  ])

  const [keyMetrics, setKeyMetrics] = useState<KeyMetric[]>([
    {
      id: "metric-1",
      name: "英伟达股价",
      currentValue: "$875.32",
      verified: true,
      sources: ["NASDAQ", "Yahoo Finance"],
      trend: "up",
      accuracy: 99
    },
    {
      id: "metric-2", 
      name: "数据中心收入占比",
      currentValue: "83%",
      verified: true,
      sources: ["英伟达财报", "SEC 文件"],
      trend: "up",
      accuracy: 95
    },
    {
      id: "metric-3",
      name: "AI 芯片市场份额",
      currentValue: "88%",
      verified: false,
      sources: ["第三方研究机构"],
      trend: "stable",
      accuracy: 75
    }
  ])

  const [riskFactors, setRiskFactors] = useState<RiskFactor[]>([
    {
      id: "risk-1",
      factor: "地缘政治风险",
      severity: "high",
      description: "中美科技竞争可能影响产品出口",
      impact: "收入下降 20-30%",
      likelihood: 60
    },
    {
      id: "risk-2",
      factor: "竞争对手追赶",
      severity: "medium", 
      description: "AMD、Intel 等竞争对手加速产品迭代",
      impact: "市场份额流失",
      likelihood: 70
    },
    {
      id: "risk-3",
      factor: "供应链瓶颈",
      severity: "medium",
      description: "先进制程产能限制可能影响生产",
      impact: "交付延期",
      likelihood: 45
    }
  ])

  const [isRefreshing, setIsRefreshing] = useState(false)

  const refreshFactCheck = () => {
    setIsRefreshing(true)
    // TODO: 调用后端 API 刷新事实核查数据
    setTimeout(() => {
      setIsRefreshing(false)
    }, 2000)
  }

  const getStatusIcon = (status: FactCheckItem['status']) => {
    switch (status) {
      case 'verified':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'questionable':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'false':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'pending':
        return <RefreshCw className="h-4 w-4 text-gray-400 animate-spin" />
      default:
        return <Minus className="h-4 w-4 text-gray-400" />
    }
  }

  const getSentimentIcon = (sentiment: NewsItem['sentiment']) => {
    switch (sentiment) {
      case 'positive':
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'negative':
        return <TrendingDown className="h-4 w-4 text-red-600" />
      case 'neutral':
        return <Minus className="h-4 w-4 text-gray-500" />
      default:
        return <Minus className="h-4 w-4 text-gray-400" />
    }
  }

  const getTrendIcon = (trend: KeyMetric['trend']) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" />
      case 'stable':
        return <Minus className="h-4 w-4 text-gray-500" />
      default:
        return <Minus className="h-4 w-4 text-gray-400" />
    }
  }

  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg flex items-center gap-2">
          <Shield className="h-5 w-5 text-blue-600" />
          事实核查调研
        </CardTitle>
        {!isReadOnly && (
          <div className="flex gap-2 mt-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={refreshFactCheck} 
              disabled={isRefreshing}
            >
              <RefreshCw className={cn("h-4 w-4 mr-1", isRefreshing && "animate-spin")} />
              重新核查
            </Button>
          </div>
        )}
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 核心事实验证 */}
        <div>
          <h3 className="font-medium text-sm mb-3 flex items-center gap-2">
            <Search className="h-4 w-4" />
            核心事实验证
          </h3>
          <div className="space-y-3">
            {factChecks.map((fact) => (
              <div key={fact.id} className="border rounded-lg p-3">
                <div className="flex items-start gap-3">
                  <div className="mt-0.5">
                    {getStatusIcon(fact.status)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <p className="text-sm font-medium">{fact.claim}</p>
                      <Badge variant="outline" className="text-xs">
                        {fact.confidence}% 确信度
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground mb-2">
                      {fact.details}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>来源: {fact.source}</span>
                      <span>更新: {fact.lastUpdated}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 相关新闻资讯 */}
        <div>
          <h3 className="font-medium text-sm mb-3 flex items-center gap-2">
            <Activity className="h-4 w-4" />
            相关新闻资讯
          </h3>
          <div className="space-y-2">
            {newsItems.map((news) => (
              <div key={news.id} className="border rounded-lg p-3">
                <div className="flex items-start gap-3">
                  <div className="mt-0.5">
                    {getSentimentIcon(news.sentiment)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <p className="text-sm font-medium">{news.title}</p>
                      <Badge variant="outline" className="text-xs">
                        {news.relevance}% 相关
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground mb-2">
                      {news.summary}
                    </p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>{news.source}</span>
                        <span>{news.publishedAt}</span>
                      </div>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="h-6 w-6 p-0"
                        onClick={() => window.open(news.url, '_blank')}
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 关键指标查证 */}
          <div>
            <h3 className="font-medium text-sm mb-3">关键指标查证</h3>
            <div className="space-y-2">
              {keyMetrics.map((metric) => (
                <div key={metric.id} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium">{metric.name}</span>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(metric.trend)}
                      <span className="text-sm font-mono">{metric.currentValue}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {metric.verified ? (
                      <CheckCircle className="h-3 w-3 text-green-600" />
                    ) : (
                      <AlertTriangle className="h-3 w-3 text-yellow-600" />
                    )}
                    <span className="text-xs text-muted-foreground">
                      {metric.accuracy}% 准确度
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 风险点识别 */}
          <div>
            <h3 className="font-medium text-sm mb-3">风险点识别</h3>
            <div className="space-y-2">
              {riskFactors.map((risk) => (
                <div key={risk.id} className="border rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge 
                      variant="outline" 
                      className={cn(
                        "text-xs",
                        risk.severity === 'high' ? 'border-red-300 text-red-700' :
                        risk.severity === 'medium' ? 'border-yellow-300 text-yellow-700' :
                        'border-green-300 text-green-700'
                      )}
                    >
                      {risk.severity === 'high' ? '高风险' :
                       risk.severity === 'medium' ? '中风险' : '低风险'}
                    </Badge>
                    <span className="text-sm font-medium">{risk.factor}</span>
                  </div>
                  <p className="text-xs text-muted-foreground mb-1">
                    {risk.description}
                  </p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>影响: {risk.impact}</span>
                    <span>概率: {risk.likelihood}%</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}