'use client'

import * as React from "react"
import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card"
import { Button } from "./ui/button"
import { Badge } from "./ui/badge"
import { TrendingUp, TrendingDown, RefreshCw, Star, BarChart3 } from "lucide-react"
import { cn } from "@yai-investor-insight/shared-fe-kit"

interface StockRecommendationsProps {
  ideaId: string
  isReadOnly?: boolean
}

interface StockRecommendation {
  symbol: string
  name: string
  type: "bullish" | "bearish"
  price: string
  change: string
  reasoning: string
  shortReasoning: string
  confidence: "高" | "中" | "低"
  isFollowing: boolean
  financials: {
    pe: string
    ps: string
    revenue: string
    growth: string
  }
}

export function StockRecommendations({ ideaId, isReadOnly = false }: StockRecommendationsProps) {
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [selectedStocks, setSelectedStocks] = useState<string[]>([])
  
  const [recommendations, setRecommendations] = useState<StockRecommendation[]>([
    {
      symbol: "NVDA",
      name: "英伟达",
      type: "bullish",
      price: "$722.48",
      change: "+2.3%",
      reasoning: "H200芯片的推出将显著提升英伟达在AI芯片市场的领导地位。预计H200的高内存容量和处理能力将吸引更多云服务商采购，推动营收增长。微软、OpenAI等重要客户的大额订单已经确认，为未来几个季度的业绩提供强力支撑。",
      shortReasoning: "H200芯片性能大幅提升，微软OpenAI等大客户确认订单。AI芯片领导地位进一步巩固，营收增长强劲。",
      confidence: "高",
      isFollowing: true,
      financials: {
        pe: "65.2",
        ps: "22.1",
        revenue: "$601B",
        growth: "+126%"
      }
    },
    {
      symbol: "TSM",
      name: "台积电",
      type: "bullish",
      price: "$97.85",
      change: "+1.8%",
      reasoning: "作为H200芯片的独家代工厂，台积电将直接受益于英伟达芯片需求的爆发式增长。先进制程技术的垄断地位确保了稳定的订单来源和高毛利率。随着AI芯片市场的扩张，台积电的产能利用率和定价能力都将得到提升。",
      shortReasoning: "作为H200独家代工厂，直接受益AI芯片需求爆发。先进制程垄断地位确保高毛利率，产能利用率大幅提升。",
      confidence: "高",
      isFollowing: false,
      financials: {
        pe: "18.5",
        ps: "7.2",
        revenue: "$75.9B",
        growth: "+10.9%"
      }
    },
    {
      symbol: "AMD",
      name: "超威半导体",
      type: "bearish",
      price: "$143.21",
      change: "-0.8%",
      reasoning: "虽然AMD的MI300X在某些场景下具有竞争力，但英伟达H200的发布进一步拉大了技术差距。AMD在AI芯片市场的份额可能面临进一步压缩，特别是在高端市场。CUDA生态系统的优势让英伟达在开发者中具有更强的粘性。",
      shortReasoning: "H200发布拉大技术差距，AMD在AI芯片高端市场份额可能被压缩。CUDA生态优势明显。",
      confidence: "中",
      isFollowing: false,
      financials: {
        pe: "192.3",
        ps: "8.9",
        revenue: "$23.1B",
        growth: "+11.2%"
      }
    }
  ])

  const toggleFollow = (symbol: string) => {
    setRecommendations(prev => prev.map(stock => 
      stock.symbol === symbol 
        ? { ...stock, isFollowing: !stock.isFollowing }
        : stock
    ))
  }

  const toggleSelect = (symbol: string) => {
    setSelectedStocks(prev => 
      prev.includes(symbol)
        ? prev.filter(s => s !== symbol)
        : [...prev, symbol]
    )
  }

  const refreshRecommendations = () => {
    setIsRefreshing(true)
    setTimeout(() => {
      setIsRefreshing(false)
    }, 2000)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-primary" />
              相关股票推荐
            </CardTitle>
            {!isReadOnly && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={refreshRecommendations}
                disabled={isRefreshing}
              >
                <RefreshCw className={cn("h-4 w-4 mr-1", isRefreshing && "animate-spin")} />
                刷新推荐
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {recommendations.map((stock) => (
            <Card key={stock.symbol} className="transition-all duration-200 hover:shadow-md">
              <CardContent className="p-4">
                <div className="space-y-4">
                  {/* 股票基本信息 */}
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className="text-center">
                        <div className="font-bold text-lg">{stock.symbol}</div>
                        <div className="text-sm text-muted-foreground">{stock.name}</div>
                      </div>
                      
                      <div className="text-right">
                        <div className="font-semibold">{stock.price}</div>
                        <div className={cn(
                          "text-sm flex items-center gap-1",
                          stock.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
                        )}>
                          {stock.type === 'bullish' ? (
                            <TrendingUp className="h-3 w-3" />
                          ) : (
                            <TrendingDown className="h-3 w-3" />
                          )}
                          {stock.change}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Badge 
                        variant={stock.type === 'bullish' ? 'default' : 'destructive'}
                        className="text-xs"
                      >
                        {stock.type === 'bullish' ? '看多' : '看空'}
                      </Badge>
                      
                      <Badge 
                        variant="secondary"
                        className={cn(
                          "text-xs",
                          stock.confidence === '高' ? 'bg-green-100 text-green-700' :
                          stock.confidence === '中' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-red-100 text-red-700'
                        )}
                      >
                        信心: {stock.confidence}
                      </Badge>

                      {!isReadOnly && (
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-6 w-6 p-0"
                          onClick={() => toggleFollow(stock.symbol)}
                        >
                          <Star className={cn(
                            "h-4 w-4",
                            stock.isFollowing ? 'fill-yellow-400 text-yellow-400' : 'text-muted-foreground'
                          )} />
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* 投资逻辑 */}
                  <div>
                    <h4 className="font-medium text-sm mb-2">投资逻辑</h4>
                    <p className="text-xs text-muted-foreground leading-relaxed">
                      {stock.shortReasoning}
                    </p>
                  </div>

                  {/* 财务指标 */}
                  <div className="grid grid-cols-4 gap-4 pt-3 border-t">
                    <div className="text-center">
                      <div className="text-xs text-muted-foreground">PE</div>
                      <div className="font-medium text-sm">{stock.financials.pe}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xs text-muted-foreground">PS</div>
                      <div className="font-medium text-sm">{stock.financials.ps}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xs text-muted-foreground">营收</div>
                      <div className="font-medium text-sm">{stock.financials.revenue}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xs text-muted-foreground">增长</div>
                      <div className={cn(
                        "font-medium text-sm",
                        stock.financials.growth.startsWith('+') ? 'text-green-600' : 'text-red-600'
                      )}>
                        {stock.financials.growth}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </CardContent>
      </Card>

      {/* 已选择的股票汇总 */}
      {selectedStocks.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">投资组合建议</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2 mb-4">
              {selectedStocks.map(symbol => {
                const stock = recommendations.find(s => s.symbol === symbol)
                return (
                  <Badge key={symbol} variant="outline" className="flex items-center gap-1">
                    {symbol}
                    <button 
                      onClick={() => toggleSelect(symbol)}
                      className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
                    >
                      ×
                    </button>
                  </Badge>
                )
              })}
            </div>
            <p className="text-sm text-muted-foreground">
              基于当前分析，建议关注以上标的的投资机会。请注意风险控制和分散投资。
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}