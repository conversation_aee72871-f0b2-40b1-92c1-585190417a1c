'use client'

import * as React from "react"
import { useState } from "react"
import { Button } from "./ui/button"
import { Badge } from "./ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card"
import { RefreshCw, Edit3, Heart, TrendingUp, ExternalLink, FileText } from "lucide-react"
import { cn } from "@yai-investor-insight/shared-fe-kit"

interface FutureScenario {
  id: string
  title: string
  content: string
  probability: number
  possibility: "高可能性" | "中等可能性" | "低可能性"
  timeframe: string
  liked: boolean
  references: Array<{
    id: string
    title: string
    source: string
    url: string
    time: string
  }>
}

interface AiThinkingFlowProps {
  ideaId: string
  isReadOnly?: boolean
}

export function AiThinkingFlow({
  ideaId,
  isReadOnly = false
}: AiThinkingFlowProps) {
  // TODO: 集成后端 API - 从 API 加载真实的 AI 推演数据
  // 可能调用 research-v2-bs 的某个端点来获取分析结果
  const [scenarios, setScenarios] = useState<FutureScenario[]>([
    {
      id: "scenario-1",
      title: "技术领先持续巩固",
      content: "英伟达H200采用先进的HBM3e内存技术，提供了比前代产品近2倍的内存带宽提升，这将显著改善大型模型训练和推理的效率。从技术角度看，H200的架构优化针对Transformer模型进行了深度定制，在处理注意力机制时的计算效率远超竞品。考虑到当前AI应用对算力的需求爆发式增长，特别是在自动驾驶、医疗影像分析和科学计算等领域，H200的性能提升来得正是时候。",
      probability: 85,
      possibility: "高可能性",
      timeframe: "3-6个月",
      liked: false,
      references: [
        {
          id: "ref-1-1",
          title: "英伟达H200 GPU正式发布，性能提升90%",
          source: "科技日报",
          url: "#",
          time: "2天前"
        },
        {
          id: "ref-1-2",
          title: "云服务商争相采购新一代AI芯片",
          source: "财经周刊",
          url: "#",
          time: "1周前"
        }
      ]
    },
    {
      id: "scenario-2",
      title: "市场竞争加剧",
      content: "AMD正加快MI300X系列的生产进度，预计在明年第二季度大规模出货，其在某些特定工作负载下的性价比可能超过H200。同时，英特尔的Gaudi系列芯片在推理任务上也展现出了竞争优势，特别是在成本控制方面。更值得关注的是，中国的寒武纪、海光等厂商正在快速追赶，虽然在绝对性能上仍有差距，但在特定应用场景下已经能够满足基本需求。",
      probability: 70,
      possibility: "中等可能性",
      timeframe: "6-12个月",
      liked: true,
      references: [
        {
          id: "ref-2-1",
          title: "AMD MI300X芯片性能评测报告",
          source: "芯片世界",
          url: "#",
          time: "3天前"
        },
        {
          id: "ref-2-2",
          title: "中国AI芯片厂商加速追赶",
          source: "经济观察报",
          url: "#",
          time: "5天前"
        }
      ]
    },
    {
      id: "scenario-3",
      title: "供应链风险持续",
      content: "地缘政治紧张局势可能对H200的生产和销售产生重大影响，特别是在中美科技竞争持续升级的背景下。美国政府可能进一步收紧对华高端芯片出口限制，这将直接影响英伟达在中国这一重要市场的收入。中国市场占英伟达数据中心业务收入的约20-25%，任何供应限制都将产生实质性影响。",
      probability: 60,
      possibility: "中等可能性",
      timeframe: "1-3个月",
      liked: false,
      references: [
        {
          id: "ref-3-1",
          title: "美国考虑进一步限制AI芯片出口",
          source: "华尔街日报",
          url: "#",
          time: "1天前"
        },
        {
          id: "ref-3-2",
          title: "台积电先进封装产能紧张",
          source: "电子工程专辑",
          url: "#",
          time: "4天前"
        }
      ]
    }
  ])

  const [isGenerating, setIsGenerating] = useState(false)
  const [regeneratingId, setRegeneratingId] = useState<string | null>(null)

  const toggleScenarioLike = (scenarioId: string) => {
    setScenarios(prev => prev.map(scenario => 
      scenario.id === scenarioId 
        ? { ...scenario, liked: !scenario.liked }
        : scenario
    ))
  }

  const replaceScenario = (scenarioId: string) => {
    setRegeneratingId(scenarioId)
    setTimeout(() => {
      setScenarios(prev => prev.map(scenario => 
        scenario.id === scenarioId 
          ? {
              ...scenario,
              title: "新生成的未来情况",
              content: "基于最新数据和分析生成的未来可能性推演...",
              probability: Math.floor(Math.random() * 40) + 40
            }
          : scenario
      ))
      setRegeneratingId(null)
    }, 1500)
  }

  const regenerateAllScenarios = () => {
    setIsGenerating(true)
    setTimeout(() => {
      setIsGenerating(false)
    }, 2000)
  }

  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-primary" />
          未来情况推演
        </CardTitle>
        {!isReadOnly && (
          <div className="flex gap-2 mt-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={regenerateAllScenarios} 
              disabled={isGenerating}
            >
              <RefreshCw className={cn("h-4 w-4 mr-1", isGenerating && "animate-spin")} />
              重新推演
            </Button>
          </div>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="space-y-4 max-h-[600px] overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-muted-foreground/20 hover:scrollbar-thumb-muted-foreground/40">
          {scenarios.map((scenario, index) => (
            <Card key={scenario.id} className="transition-all duration-200 hover:shadow-md">
              <CardContent className="p-4">
                <div className="flex gap-4">
                  {/* 左侧主要内容 */}
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          情况 {index + 1}
                        </Badge>
                        <Badge 
                          variant="secondary" 
                          className={cn(
                            "text-xs",
                            scenario.possibility === '高可能性' 
                              ? 'bg-green-100 text-green-700' 
                              : scenario.possibility === '中等可能性' 
                              ? 'bg-yellow-100 text-yellow-700' 
                              : 'bg-red-100 text-red-700'
                          )}
                        >
                          {scenario.possibility}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <Button 
                          size="sm" 
                          variant="ghost" 
                          className="h-6 w-6 p-0" 
                          onClick={() => toggleScenarioLike(scenario.id)}
                        >
                          <Heart className={cn(
                            "h-4 w-4",
                            scenario.liked 
                              ? 'fill-red-500 text-red-500' 
                              : 'text-muted-foreground'
                          )} />
                        </Button>
                        
                        {!isReadOnly && (
                          <>
                            <Button 
                              size="sm" 
                              variant="ghost" 
                              className="h-6 w-6 p-0"
                            >
                              <Edit3 className="h-3 w-3" />
                            </Button>
                            <Button 
                              size="sm" 
                              variant="ghost" 
                              className="h-6 w-6 p-0" 
                              onClick={() => replaceScenario(scenario.id)}
                              disabled={regeneratingId === scenario.id}
                            >
                              <RefreshCw className={cn(
                                "h-3 w-3",
                                regeneratingId === scenario.id && "animate-spin"
                              )} />
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                    
                    <h4 className="font-medium text-sm mb-2 leading-tight">
                      {scenario.title}
                    </h4>
                    
                    <p className="text-xs text-muted-foreground leading-relaxed mb-3">
                      {scenario.content}
                    </p>
                    
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>概率: {scenario.probability}%</span>
                      <span>时间: {scenario.timeframe}</span>
                    </div>
                  </div>

                  {/* 右侧参考新闻 */}
                  <div className="w-64 border-l pl-4">
                    <div className="flex items-center gap-1 mb-3">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span className="text-xs font-medium text-muted-foreground">参考资料</span>
                    </div>
                    
                    <div className="space-y-1">
                      {scenario.references.map(ref => (
                        <div 
                          key={ref.id} 
                          className="flex items-center justify-between gap-2 py-1 px-2 rounded hover:bg-muted/30 transition-colors"
                        >
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="text-xs font-medium truncate">{ref.title}</span>
                              <span className="text-xs text-muted-foreground whitespace-nowrap">·</span>
                              <span className="text-xs text-muted-foreground whitespace-nowrap">{ref.source}</span>
                              <span className="text-xs text-muted-foreground whitespace-nowrap">·</span>
                              <span className="text-xs text-muted-foreground whitespace-nowrap">{ref.time}</span>
                            </div>
                          </div>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-4 w-4 p-0 flex-shrink-0" 
                            onClick={() => window.open(ref.url, '_blank')}
                          >
                            <ExternalLink className="h-2.5 w-2.5" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}