'use client'

import { useRouter } from "next/navigation";
import { Brain, TrendingUp, Clock, MessageSquare, Sparkles, Zap, BarChart3, Mail, MessageCircle } from "lucide-react";

interface IdeaCardProps {
  idea: {
    id: number;
    title: string;
    description: string;
    stocks: string[];
    allStocks?: string[];
    unreadEvents: number;
    lastUpdated: string;
    tags: string[];
    latestChange?: string;
    category?: string;
    aiInsight?: string;
    newsUpdates?: Array<{
      content: string;
      timestamp: string;
    }>;
  };
  onClick: () => void;
  isRecommended?: boolean;
}

// 根据卡片标题和新闻生成AI分析
const generateAIInsights = (title: string, newsUpdates?: Array<{
  content: string;
  timestamp: string;
}>, category?: string) => {
  // 基于标题关键词的分析模板
  const analysisTemplates = [{
    keywords: ['芯片', 'H200', 'M4', 'GPU', 'CPU'],
    insights: ['【利好】芯片性能大幅提升，下游应用需求激增，产业链龙头受益明显', '【趋势】AI算力需求持续爆发，高端芯片供不应求，议价能力增强', '【风险】地缘政治影响供应链稳定，需关注国际贸易政策变化']
  }, {
    keywords: ['AI', '人工智能', 'Copilot', 'GPT', 'Claude'],
    insights: ['【利好】企业数字化转型加速，AI应用渗透率快速提升，商业化进程明确', '【趋势】AI Agent成为新增长点，B端市场爆发，订阅模式验证成功', '【风险】监管政策趋严，需关注AI安全和数据隐私相关法规影响']
  }, {
    keywords: ['自动驾驶', 'FSD', '智能汽车', 'Robotaxi'],
    insights: ['【利好】技术突破带来商业化机遇，出行服务市场空间巨大', '【趋势】L4级自动驾驶规模应用在即，产业链价值重构加速', '【风险】安全监管要求提高，需关注政策审批和责任认定问题']
  }];

  // 根据标题匹配相应的分析模板
  const matchedTemplate = analysisTemplates.find(template => 
    template.keywords.some(keyword => title.includes(keyword))
  );

  // 如果没有匹配到，使用通用模板
  const defaultInsights = ['【利好】市场需求持续增长，技术壁垒逐步建立，行业龙头受益', '【趋势】政策支持力度加大，行业景气度提升，发展前景明朗', '【风险】外部环境不确定性增加，需关注竞争格局和估值水平'];
  const insights = matchedTemplate ? matchedTemplate.insights : defaultInsights;

  // 生成时间戳（最近3小时内）
  const timestamps = ['2小时前', '1小时前', '30分钟前'];
  return insights.map((insight, index) => ({
    content: insight,
    timestamp: timestamps[index]
  }));
};

export function IdeaCard({
  idea,
  onClick,
  isRecommended = false
}: IdeaCardProps) {
  const router = useRouter();
  
  // 动态生成AI分析
  const aiInsights = generateAIInsights(idea.title, idea.newsUpdates, idea.category);

  return (
    <div className="cursor-pointer bg-white hover:shadow-lg transition-all duration-300 hover:scale-[1.02] group border rounded-lg" onClick={onClick}>
      {/* 左右分块布局 */}
      <div className="flex h-full">
        {/* 左侧区域 - Ideas信息 */}
        <div className="flex-1 p-6 flex flex-col gap-4">
          {/* 1. 标题+行业/事件分类 */}
          <div className="flex items-start gap-3">
            {isRecommended ? (
              <Sparkles className="h-5 w-5 text-blue-600 flex-shrink-0 mt-1" />
            ) : (
              <Brain className="h-5 w-5 text-blue-600 flex-shrink-0 mt-1" />
            )}
            <div className="flex-1 min-w-0">
              <h3 className="font-bold text-base leading-tight line-clamp-2 group-hover:text-blue-600 transition-colors mb-2">
                {idea.title}
              </h3>
              <div className="flex items-center justify-between gap-2">
                <div className="flex items-center gap-2">
                  {idea.category && (
                    <span className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded border">
                      {idea.category}
                    </span>
                  )}
                  {isRecommended && (
                    <span className="text-xs px-2 py-1 text-blue-600 border border-blue-200 rounded">
                      推荐
                    </span>
                  )}
                </div>
                
                {/* 右侧状态图标 */}
                <div className="flex items-center gap-1.5">
                  {/* 正在分析状态 */}
                  <div className="relative">
                    <BarChart3 className="h-3.5 w-3.5 text-blue-600 animate-pulse" />
                    <div className="absolute -top-1 -right-1 w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                  </div>
                  
                  {/* 邮件订阅状态 */}
                  <Mail className="h-3.5 w-3.5 text-gray-400 hover:text-blue-600 transition-colors cursor-pointer" />
                  
                  {/* 评论数量 */}
                  <div className="flex items-center gap-1 cursor-pointer hover:text-blue-600 transition-colors">
                    <MessageCircle className="h-3.5 w-3.5 text-gray-400" />
                    <span className="text-xs text-gray-400">
                      {Math.floor(Math.random() * 50) + 1}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 2. AI观点 */}
          <div className="p-3 bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200/50 rounded">
            <div className="flex items-start gap-2">
              <Sparkles className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <ul className="space-y-2 text-sm text-gray-800">
                  {aiInsights.map((insight, index) => (
                    <li key={index} className="flex items-start justify-between gap-2">
                      <div className="flex items-start gap-2">
                        <span className="w-1 h-1 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
                        <span className="line-clamp-1">{insight.content}</span>
                      </div>
                      <span className="text-xs text-gray-400 flex-shrink-0">{insight.timestamp}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* 3. 相关新闻动态 */}
          {idea.newsUpdates && idea.newsUpdates.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-start gap-2 px-[15px]">
                <MessageSquare className="h-4 w-4 text-gray-400 mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                  <ul className="space-y-2 text-sm text-gray-800">
                    {idea.newsUpdates.slice(0, 2).map((news, index) => (
                      <li key={index} className="flex items-start justify-between gap-2">
                        <div className="flex items-start gap-2">
                          <span className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0"></span>
                          <span className="line-clamp-1 flex-1">{news.content}</span>
                        </div>
                        <span className="text-xs text-gray-400 flex-shrink-0">
                          {news.timestamp}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 右侧区域 - 股票代码 */}
        <div className="w-32 border-l bg-gray-50 p-3 flex flex-col">
          <div className="text-xs text-gray-400 mb-2 font-medium">Following</div>
          <div className="space-y-1.5 flex-1">
            {(idea.allStocks || idea.stocks).slice(0, 5).map((stock, index) => {
              // Mock percentage changes and news indicators for demo
              const changes = ['+2.3%', '-1.2%', '+0.8%', '+4.1%', '-0.5%', '+1.8%', '-3.2%', '+5.1%'];
              const hasNews = [true, false, true, false, false, true, false, true];
              const change = changes[index % changes.length];
              const isPositive = change.startsWith('+');
              const hasNewChange = hasNews[index % hasNews.length];
              
              return (
                <button
                  key={stock}
                  onClick={(e) => {
                    e.stopPropagation();
                    router.push(`/workbench-v1a/stocks/${stock}?ideaId=${idea.id}`);
                  }}
                  className="group relative w-full p-2 bg-white border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 text-left"
                >
                  {/* 红点角标 */}
                  {hasNewChange && (
                    <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
                  )}
                  
                  <div className="flex justify-between items-center">
                    <div className="font-sans text-xs font-medium text-gray-800 group-hover:text-blue-600 transition-colors">
                      {stock}
                    </div>
                    <div className={`text-xs font-medium ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
                      {change}
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
          
          {/* More 按钮 */}
          {(idea.allStocks || idea.stocks).length > 5 && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onClick(); // 进入卡片详情页面
              }}
              className="mt-2 w-full py-1.5 text-xs text-gray-400 hover:text-blue-600 transition-colors border-t border-gray-200 pt-2"
            >
              more
            </button>
          )}
        </div>
      </div>
    </div>
  );
}