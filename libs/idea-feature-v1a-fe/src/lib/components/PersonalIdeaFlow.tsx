'use client'

import * as React from "react"
import { useState } from "react"
import { Button } from "./ui/button"
import { Card, CardContent } from "./ui/card"
import { Textarea } from "./ui/textarea"
import { Badge } from "./ui/badge"
import { User, Plus, Edit3, Check, X, Trash2 } from "lucide-react"
import { cn } from "@yai-investor-insight/shared-fe-kit"

interface PersonalIdeaCard {
  id: string
  content: string
  createdAt: string
  isEditing?: boolean
}

interface PersonalIdeaFlowProps {
  ideaId: string
}

export function PersonalIdeaFlow({ ideaId }: PersonalIdeaFlowProps) {
  const [ideas, setIdeas] = useState<PersonalIdeaCard[]>([
    {
      id: "personal-1",
      content: "从投资角度看，H200的发布可能会推动整个AI基础设施的升级周期。不仅仅是云服务商，传统企业也会加速AI转型。",
      createdAt: "2小时前"
    },
    {
      id: "personal-2", 
      content: "需要关注竞争对手的反应。AMD的MI300X和Intel的Gaudi3都在虎视眈眈，价格战可能即将开始。",
      createdAt: "1小时前"
    }
  ])

  const [newIdeaContent, setNewIdeaContent] = useState("")
  const [isAdding, setIsAdding] = useState(false)
  const [editingContent, setEditingContent] = useState("")

  const addNewIdea = () => {
    if (newIdeaContent.trim()) {
      const newIdea: PersonalIdeaCard = {
        id: `personal-${Date.now()}`,
        content: newIdeaContent.trim(),
        createdAt: "刚刚"
      }
      setIdeas(prev => [...prev, newIdea])
      setNewIdeaContent("")
      setIsAdding(false)
    }
  }

  const startEdit = (id: string) => {
    const idea = ideas.find(i => i.id === id)
    if (idea) {
      setEditingContent(idea.content)
      setIdeas(prev => prev.map(idea => 
        idea.id === id ? { ...idea, isEditing: true } : idea
      ))
    }
  }

  const saveEdit = (id: string) => {
    setIdeas(prev => prev.map(idea => 
      idea.id === id 
        ? { ...idea, content: editingContent, isEditing: false }
        : idea
    ))
    setEditingContent("")
  }

  const cancelEdit = (id: string) => {
    setIdeas(prev => prev.map(idea => 
      idea.id === id ? { ...idea, isEditing: false } : idea
    ))
    setEditingContent("")
  }

  const deleteIdea = (id: string) => {
    setIdeas(prev => prev.filter(idea => idea.id !== id))
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <User className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">个人想法流</h3>
        </div>
        <Button 
          size="sm" 
          onClick={() => setIsAdding(true)}
          disabled={isAdding}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          新建想法
        </Button>
      </div>

      <div className="space-y-3">
        {/* 新建想法卡片 */}
        {isAdding && (
          <Card className="border-primary/50 shadow-sm">
            <CardContent className="p-4">
              <div className="space-y-3">
                <Textarea
                  placeholder="记录你的想法..."
                  value={newIdeaContent}
                  onChange={(e) => setNewIdeaContent(e.target.value)}
                  className="min-h-[80px] resize-none"
                  autoFocus
                />
                <div className="flex gap-2 justify-end">
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => {
                      setIsAdding(false)
                      setNewIdeaContent("")
                    }}
                  >
                    <X className="h-4 w-4 mr-2" />
                    取消
                  </Button>
                  <Button 
                    size="sm"
                    onClick={addNewIdea}
                    disabled={!newIdeaContent.trim()}
                  >
                    <Check className="h-4 w-4 mr-2" />
                    保存
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 现有想法卡片 */}
        {ideas.map((idea) => (
          <Card key={idea.id} className="transition-all duration-200 hover:shadow-md">
            <CardContent className="p-4">
              {idea.isEditing ? (
                <div className="space-y-3">
                  <Textarea
                    value={editingContent}
                    onChange={(e) => setEditingContent(e.target.value)}
                    className="min-h-[80px] resize-none"
                    autoFocus
                  />
                  <div className="flex gap-2 justify-end">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => cancelEdit(idea.id)}
                    >
                      <X className="h-4 w-4 mr-2" />
                      取消
                    </Button>
                    <Button 
                      size="sm"
                      onClick={() => saveEdit(idea.id)}
                      disabled={!editingContent.trim()}
                    >
                      <Check className="h-4 w-4 mr-2" />
                      保存
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <p className="text-sm text-gray-700 leading-relaxed flex-1">
                      {idea.content}
                    </p>
                    <div className="flex items-center gap-1 ml-3">
                      <Button 
                        size="sm" 
                        variant="ghost" 
                        className="h-6 w-6 p-0"
                        onClick={() => startEdit(idea.id)}
                      >
                        <Edit3 className="h-3 w-3" />
                      </Button>
                      <Button 
                        size="sm" 
                        variant="ghost" 
                        className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                        onClick={() => deleteIdea(idea.id)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Badge variant="secondary" className="text-xs">
                      个人观点
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {idea.createdAt}
                    </span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}

        {ideas.length === 0 && !isAdding && (
          <Card className="border-dashed">
            <CardContent className="p-8 text-center">
              <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground mb-4">
                还没有记录任何个人想法
              </p>
              <Button onClick={() => setIsAdding(true)}>
                <Plus className="h-4 w-4 mr-2" />
                添加第一个想法
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}