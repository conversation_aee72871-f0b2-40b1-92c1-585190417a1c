'use client'

interface TagFilterProps {
  allTags: string[];
  selectedTags: string[];
  onTagChange: (tags: string[]) => void;
}

export function TagFilter({ allTags, selectedTags, onTagChange }: TagFilterProps) {
  const handleTagClick = (tag: string) => {
    if (selectedTags.includes(tag)) {
      // 如果标签已选中，则移除
      onTagChange(selectedTags.filter(t => t !== tag));
    } else {
      // 单选模式：替换当前选中的标签
      onTagChange([tag]);
    }
  };

  return (
    <div className="flex gap-2">
      {allTags.map(tag => (
        <button
          key={tag}
          onClick={() => handleTagClick(tag)}
          className={`px-3 py-1 text-sm rounded-full border transition-colors ${
            selectedTags.includes(tag)
              ? 'bg-blue-100 text-blue-800 border-blue-300'
              : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
          }`}
        >
          {tag}
        </button>
      ))}
    </div>
  );
}