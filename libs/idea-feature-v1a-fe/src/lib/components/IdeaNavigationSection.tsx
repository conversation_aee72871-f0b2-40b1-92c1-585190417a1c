'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { 
  ArrowPathIcon, 
  CheckIcon, 
  DocumentIcon 
} from '@heroicons/react/24/outline'
import { getIdeaList, type IdeaItem } from '../actions'

// 定义导航项类型
interface IdeaNavItem {
  id: string
  title: string
  status?: 'executing' | 'completed' | 'draft'
  href: string
}

interface IdeaNavigationProps {
  currentPath: string
  onNewIdea?: () => void
  className?: string
  ideas?: IdeaItem[]  // 外部传入的 Ideas 列表
  isLoading?: boolean // 外部的加载状态
}

function IdeaNavItemComponent({ 
  item, 
  isActive 
}: { 
  item: IdeaNavItem
  isActive: boolean 
}) {
  const getStatusIcon = (status?: 'executing' | 'completed' | 'draft') => {
    switch (status) {
      case 'executing':
        return <ArrowPathIcon className="h-4 w-4 text-blue-600 animate-spin" title="执行中" />
      case 'completed':
        return <CheckIcon className="h-4 w-4 text-green-600" title="已完成" />
      case 'draft':
        return <DocumentIcon className="h-4 w-4 text-gray-500" title="草稿" />
      default:
        return null
    }
  }

  return (
    <Link href={item.href}>
      <div className={`group flex items-center px-3 py-2 rounded-lg text-sm transition-colors cursor-pointer ${
        isActive 
          ? 'bg-blue-50 text-blue-700 border border-blue-200'
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
      }`}>
        <div className="flex items-center gap-2 flex-1 min-w-0">
          {getStatusIcon(item.status)}
          <span className="truncate">{item.title}</span>
        </div>
      </div>
    </Link>
  )
}

function NavigationActionButton({ 
  label, 
  onClick, 
  className = '' 
}: { 
  label: string
  onClick: () => void
  className?: string 
}) {
  return (
    <button
      onClick={onClick}
      className={`w-full px-3 py-2 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors border border-blue-200 hover:border-blue-300 ${className}`}
    >
      {label}
    </button>
  )
}

export function IdeaNavigationSection({ 
  currentPath, 
  onNewIdea,
  className = '',
  ideas: externalIdeas,
  isLoading: externalIsLoading 
}: IdeaNavigationProps) {
  const [ideas, setIdeas] = useState<IdeaNavItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // 如果有外部传入的 ideas，使用外部数据
    if (externalIdeas) {
      const navItems: IdeaNavItem[] = externalIdeas.map(idea => ({
        id: String(idea.id),
        title: idea.title,
        status: getNavStatus(idea),
        href: `/workbench-v1a/ideas/${idea.id}`
      }))
      setIdeas(navItems)
      setIsLoading(false) // 外部数据已加载完成
      return
    }
    
    // 否则使用内部加载逻辑（向后兼容）
    const loadIdeas = async () => {
      try {
        setIsLoading(true)
        setError(null)
        
        const ideaData = await getIdeaList()
        
        // 转换为导航所需的格式
        const navItems: IdeaNavItem[] = ideaData.myIdeas.map(idea => ({
          id: String(idea.id),
          title: idea.title,
          status: getNavStatus(idea),
          href: `/workbench-v1a/ideas/${idea.id}`
        }))
        
        setIdeas(navItems)
      } catch (err) {
        setError('加载 Ideas 失败')
        console.error('Failed to load ideas for navigation:', err)
      } finally {
        setIsLoading(false)
      }
    }

    loadIdeas()
  }, [externalIdeas, externalIsLoading])

  // 根据 idea 数据获取导航状态
  const getNavStatus = (idea: IdeaItem): 'executing' | 'completed' | 'draft' | undefined => {
    // 直接使用 idea 的状态字段，确保类型一致
    const status = idea.status
    if (status === 'executing' || status === 'completed' || status === 'draft') {
      return status
    }
    return undefined
  }

  const handleNewIdea = () => {
    if (onNewIdea) {
      onNewIdea()
    }
  }

  if (error) {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="px-3">
          <h2 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
            我的Ideas
          </h2>
        </div>
        <div className="px-3 py-2 text-xs text-red-600">
          {error}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 新建 Idea 按钮 */}
      <NavigationActionButton 
        label="新 Idea" 
        onClick={handleNewIdea}
      />
      
      {/* 分组标题 */}
      <div className="px-3 pt-4">
        <h2 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
          我的Ideas
        </h2>
      </div>
      
      {/* Ideas 列表 */}
      <div className="space-y-1">
        {(externalIsLoading !== undefined ? externalIsLoading : isLoading) ? (
          <div className="px-3 py-2 text-xs text-gray-500">
            加载中...
          </div>
        ) : ideas.length === 0 ? (
          <div className="px-3 py-2 text-xs text-muted-foreground">
            暂无 Ideas
          </div>
        ) : (
          ideas.map((item) => (
            <IdeaNavItemComponent 
              key={item.id} 
              item={item} 
              isActive={currentPath === item.href}
            />
          ))
        )}
      </div>
    </div>
  )
}