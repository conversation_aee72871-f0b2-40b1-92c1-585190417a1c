'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  DollarSign, 
  BarChart3,
  AlertCircle,
  CheckCircle,
  Clock,
  Send,
  RefreshCw
} from 'lucide-react';

interface InvestmentIdea {
  id: string;
  title: string;
  symbol: string;
  category: string;
  sentiment: 'bullish' | 'bearish' | 'neutral';
  change: string;
  confidence: number;
  keyPoints: string[];
  riskFactors: string[];
  following: boolean;
}

interface AnalysisStage {
  name: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  progress: number;
  content?: string;
}

export function InvestmentInsightDashboard() {
  const [query, setQuery] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const [analysisStages, setAnalysisStages] = useState<AnalysisStage[]>([
    { name: '事实验证', status: 'pending', progress: 0 },
    { name: '影响模拟', status: 'pending', progress: 0 },
    { name: '投资建议', status: 'pending', progress: 0 },
    { name: '最终报告', status: 'pending', progress: 0 }
  ]);
  const [finalReport, setFinalReport] = useState<string>('');
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  // 模拟投资想法数据（基于页面截图）
  const [investmentIdeas] = useState<InvestmentIdea[]>([
    {
      id: '1',
      title: 'NVIDIA H200超级芯片量产，AI训练效率革命性提升',
      symbol: 'NVDA',
      category: 'AI芯片',
      sentiment: 'bullish',
      change: '+2.3%',
      confidence: 90,
      keyPoints: ['芯片性能大幅提升', 'AI算力需求持续增长', '下游应用需求旺盛'],
      riskFactors: ['地缘政治影响', '需关注监管政策', '竞争对手追赶'],
      following: true
    },
    {
      id: '2', 
      title: '微软Copilot全面集成Office365，企业AI办公时代到来',
      symbol: 'MSFT',
      category: '企业软件',
      sentiment: 'bullish',
      change: '+2.3%',
      confidence: 85,
      keyPoints: ['企业数字化转型加速', 'AI应用渗透率提升', 'B端市场爆发'],
      riskFactors: ['监管政策趋严', '需关注AI安全和数据'],
      following: true
    },
    {
      id: '3',
      title: '特斯拉FSD V13实现L4级自动驾驶，商业化应用在即',
      symbol: 'TSLA',
      category: '智能汽车',
      sentiment: 'bullish', 
      change: '+2.3%',
      confidence: 75,
      keyPoints: ['技术突破来临', 'L4级自动驾驶实现', '出行服务商业化'],
      riskFactors: ['安全监管要求提高', '需关注政策审批', '技术成熟度验证'],
      following: true
    },
    {
      id: '4',
      title: '苹果M4 Ultra芯片发布，端侧AI计算能力超越云端',
      symbol: 'AAPL',
      category: '芯片技术',
      sentiment: 'bullish',
      change: '+2.3%', 
      confidence: 80,
      keyPoints: ['芯片性能大幅提升', 'AI算力需求持续增长', '地缘政治影响'],
      riskFactors: ['地缘政治影响', '需关注监管政策'],
      following: true
    }
  ]);

  const startAnalysis = async () => {
    if (!query.trim() || isAnalyzing) return;

    setIsAnalyzing(true);
    setFinalReport('');
    setAnalysisResults(null);
    
    // 重置阶段状态
    setAnalysisStages(stages => 
      stages.map(stage => ({ ...stage, status: 'pending', progress: 0, content: undefined }))
    );

    try {
      // 提交分析任务（通过前端API路由转发）
      const response = await fetch('/api/investment/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      });

      if (!response.ok) {
        throw new Error('提交分析任务失败');
      }

      const { task_id } = await response.json();
      setCurrentTaskId(task_id);

      // 建立SSE连接（通过前端API路由转发）
      const eventSource = new EventSource(`/api/investment/stream/${task_id}`);
      eventSourceRef.current = eventSource;

      eventSource.onmessage = (event) => {
        console.log('收到SSE消息:', event);
      };

      // 处理不同类型的事件
      eventSource.addEventListener('connected', (event) => {
        console.log('SSE连接已建立');
      });

      eventSource.addEventListener('research_started', (event) => {
        const data = JSON.parse(event.data);
        console.log('分析开始:', data);
      });

      eventSource.addEventListener('stage_started', (event) => {
        const data = JSON.parse(event.data);
        updateStageStatus(data.stage, 'running', 0);
      });

      eventSource.addEventListener('stage_progress', (event) => {
        const data = JSON.parse(event.data);
        updateStageStatus(data.stage, 'running', data.progress, data.content);
      });

      eventSource.addEventListener('stage_completed', (event) => {
        const data = JSON.parse(event.data);
        updateStageStatus(data.stage, 'completed', 100);
      });

      eventSource.addEventListener('research_completed', (event) => {
        const data = JSON.parse(event.data);
        setFinalReport(data.final_report);
        setAnalysisResults(data.analysis_results);
        setIsAnalyzing(false);
        eventSource.close();
      });

      eventSource.addEventListener('error', (event: any) => {
        const data = JSON.parse(event.data);
        console.error('分析错误:', data);
        setIsAnalyzing(false);
        eventSource.close();
      });

      eventSource.onerror = (error) => {
        console.error('SSE连接错误:', error);
        setIsAnalyzing(false);
        eventSource.close();
      };

    } catch (error) {
      console.error('启动分析失败:', error);
      setIsAnalyzing(false);
    }
  };

  const updateStageStatus = (stageName: string, status: AnalysisStage['status'], progress: number, content?: string) => {
    setAnalysisStages(stages => 
      stages.map(stage => {
        if (getStageKey(stage.name) === stageName) {
          return { ...stage, status, progress, content };
        }
        return stage;
      })
    );
  };

  const getStageKey = (stageName: string) => {
    const stageMap: Record<string, string> = {
      '事实验证': 'fact_verification',
      '影响模拟': 'impact_simulation', 
      '投资建议': 'thesis_recommendation',
      '最终报告': 'final_report'
    };
    return stageMap[stageName] || stageName;
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'bullish':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'bearish':
        return <TrendingDown className="w-4 h-4 text-red-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStageIcon = (status: AnalysisStage['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'running':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  // 清理EventSource
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* 标题栏 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">我的 Ideas</h1>
            <p className="text-gray-600 mt-1">共 6 个被法追踪中</p>
          </div>
          <div className="flex items-center space-x-4">
            <span className="inline-flex items-center rounded-full font-medium px-2.5 py-1 text-sm bg-orange-100 text-orange-600">
              1,850 积分
            </span>
            <span className="inline-flex items-center rounded-full font-medium px-2.5 py-1 text-sm border border-gray-300 text-gray-700">+200/日</span>
            <button className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-indigo-600 text-white hover:bg-indigo-700 h-10 px-4 py-2">
              + 新 Idea
            </button>
          </div>
        </div>

        {/* 投资分析输入区 */}
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="flex flex-col space-y-1.5 p-6">
            <h3 className="text-2xl font-semibold leading-none tracking-tight flex items-center space-x-2">
              <BarChart3 className="w-5 h-5" />
              <span>AI 投资分析</span>
            </h3>
          </div>
          <div className="p-6 pt-0">
            <div className="flex space-x-4">
              <input
                type="text"
                placeholder="输入您想分析的投资标的或问题..."
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="flex-1 h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                onKeyPress={(e) => e.key === 'Enter' && startAnalysis()}
              />
              <button 
                onClick={startAnalysis}
                disabled={isAnalyzing || !query.trim()}
                className="px-6 inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700 h-10 space-x-2"
              >
                {isAnalyzing ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <Send className="w-4 h-4" />
                )}
                <span>{isAnalyzing ? '分析中...' : '开始分析'}</span>
              </button>
            </div>

            {/* 分析进度 */}
            {isAnalyzing && (
              <div className="mt-6 space-y-4">
                <h3 className="font-semibold text-gray-900">分析进度</h3>
                {analysisStages.map((stage, index) => (
                  <div key={index} className="flex items-center space-x-4">
                    {getStageIcon(stage.status)}
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">{stage.name}</span>
                        <span className="text-xs text-gray-500">{stage.progress}%</span>
                      </div>
                      <div className="relative h-2 w-full overflow-hidden rounded-full bg-gray-200">
                        <div
                          className="h-full w-full flex-1 bg-blue-600 transition-all duration-300 ease-out"
                          style={{ transform: `translateX(-${100 - stage.progress}%)` }}
                        />
                      </div>
                      {stage.content && (
                        <p className="text-xs text-gray-600 mt-1">{stage.content}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* 分析结果 */}
            {finalReport && (
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-3">分析报告</h3>
                <div className="prose prose-sm max-w-none">
                  <pre className="whitespace-pre-wrap text-sm">{finalReport}</pre>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 投资想法网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {investmentIdeas.map((idea) => (
            <div key={idea.id} className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-lg transition-shadow">
              <div className="flex flex-col space-y-1.5 p-6 pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-2">
                    {getSentimentIcon(idea.sentiment)}
                    <div>
                      <h3 className="text-lg text-2xl font-semibold leading-none tracking-tight">{idea.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{idea.category}</p>
                    </div>
                  </div>
                  <span className={`inline-flex items-center rounded-full font-medium px-2.5 py-1 text-sm ${idea.following ? 'bg-gray-900 text-white' : 'border border-gray-300 text-gray-700'}`}>
                    Following
                  </span>
                </div>
              </div>
              <div className="p-6 pt-0 space-y-4">
                {/* 股票信息 */}
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-red-500 rounded flex items-center justify-center text-white text-xs font-bold">
                      {idea.symbol.slice(0, 2)}
                    </div>
                    <div>
                      <p className="font-semibold">{idea.symbol}</p>
                      <p className="text-xs text-gray-600">股票代码</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">{idea.change}</p>
                    <p className="text-xs text-gray-600">涨跌幅</p>
                  </div>
                </div>

                {/* 关键要点 */}
                <div>
                  <h4 className="font-semibold text-sm mb-2 flex items-center">
                    <TrendingUp className="w-4 h-4 mr-1 text-green-500" />
                    利好
                  </h4>
                  <ul className="space-y-1">
                    {idea.keyPoints.map((point, index) => (
                      <li key={index} className="text-sm text-gray-600 flex items-start">
                        <span className="mr-2">•</span>
                        <span>{point}</span>
                        <span className="ml-auto text-xs text-gray-400">
                          {index < 2 ? '2小时前' : '30分钟前'}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* 风险因素 */}
                <div>
                  <h4 className="font-semibold text-sm mb-2 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-1 text-orange-500" />
                    风险
                  </h4>
                  <ul className="space-y-1">
                    {idea.riskFactors.map((risk, index) => (
                      <li key={index} className="text-sm text-gray-600 flex items-start">
                        <span className="mr-2">•</span>
                        <span>{risk}</span>
                        <span className="ml-auto text-xs text-gray-400">
                          30分钟前
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* 置信度 */}
                <div className="pt-2 border-t">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">置信度</span>
                    <span className="font-semibold">{idea.confidence}%</span>
                  </div>
                  <div className="relative h-2 w-full overflow-hidden rounded-full bg-gray-200 mt-1">
                    <div
                      className="h-full w-full flex-1 bg-blue-600 transition-all duration-300 ease-out"
                      style={{ transform: `translateX(-${100 - idea.confidence}%)` }}
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 