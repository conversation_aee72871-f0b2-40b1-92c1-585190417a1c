{"name": "@yai-investor-insight/research-v2b-fe", "version": "0.1.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "@yai-investor-insight/research-v2b-fe", "version": "0.1.0", "dependencies": {"@ag-ui/client": "^0.0.35", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "lucide-react": "^0.525.0", "react": "^19.0.0", "react-dom": "^19.0.0", "zustand": "^5.0.6"}, "devDependencies": {"typescript": "^5.0.0", "zustand": "^5.0.6"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "zustand": "^5.0.0"}}, "../../node_modules/.pnpm/@ag-ui+client@0.0.35/node_modules/@ag-ui/client": {"version": "0.0.35", "dependencies": {"@ag-ui/core": "0.0.35", "@ag-ui/encoder": "0.0.35", "@ag-ui/proto": "0.0.35", "@types/uuid": "^10.0.0", "fast-json-patch": "^3.1.1", "rxjs": "7.8.1", "untruncate-json": "^0.0.1", "uuid": "^11.1.0", "zod": "^3.22.4"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^20.11.19", "jest": "^29.7.0", "ts-jest": "^29.1.2", "tsup": "^8.0.2", "typescript": "^5.3.3"}}, "../../node_modules/.pnpm/@types+react-dom@19.0.0/node_modules/@types/react-dom": {"version": "19.0.0", "license": "MIT", "dependencies": {"@types/react": "*"}}, "../../node_modules/.pnpm/@types+react@19.0.0/node_modules/@types/react": {"version": "19.0.0", "license": "MIT", "dependencies": {"csstype": "^3.0.2"}}, "../../node_modules/.pnpm/react-dom@19.0.0_react@19.0.0/node_modules/react-dom": {"version": "19.0.0", "license": "MIT", "dependencies": {"scheduler": "^0.25.0"}, "peerDependencies": {"react": "^19.0.0"}}, "../../node_modules/.pnpm/react@19.0.0/node_modules/react": {"version": "19.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript": {"version": "5.8.3", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "devDependencies": {"@dprint/formatter": "^0.4.1", "@dprint/typescript": "0.93.3", "@esfx/canceltoken": "^1.0.0", "@eslint/js": "^9.17.0", "@octokit/rest": "^21.0.2", "@types/chai": "^4.3.20", "@types/diff": "^5.2.3", "@types/minimist": "^1.2.5", "@types/mocha": "^10.0.10", "@types/ms": "^0.7.34", "@types/node": "latest", "@types/source-map-support": "^0.5.10", "@types/which": "^3.0.4", "@typescript-eslint/rule-tester": "^8.18.1", "@typescript-eslint/type-utils": "^8.18.1", "@typescript-eslint/utils": "^8.18.1", "azure-devops-node-api": "^14.1.0", "c8": "^10.1.3", "chai": "^4.5.0", "chalk": "^4.1.2", "chokidar": "^3.6.0", "diff": "^5.2.0", "dprint": "^0.47.6", "esbuild": "^0.24.0", "eslint": "^9.17.0", "eslint-formatter-autolinkable-stylish": "^1.4.0", "eslint-plugin-regexp": "^2.7.0", "fast-xml-parser": "^4.5.1", "glob": "^10.4.5", "globals": "^15.13.0", "hereby": "^1.10.0", "jsonc-parser": "^3.3.1", "knip": "^5.41.0", "minimist": "^1.2.8", "mocha": "^10.8.2", "mocha-fivemat-progress-reporter": "^0.1.0", "monocart-coverage-reports": "^2.11.4", "ms": "^2.1.3", "playwright": "^1.49.1", "source-map-support": "^0.5.21", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1", "which": "^3.0.1"}, "engines": {"node": ">=14.17"}}, "../../node_modules/.pnpm/zustand@5.0.7_@types+react@19.0.0_immer@10.1.1_react@19.0.0_use-sync-external-store@1.5.0_react@19.0.0_/node_modules/zustand": {"version": "5.0.7", "dev": true, "license": "MIT", "engines": {"node": ">=12.20.0"}, "peerDependencies": {"@types/react": ">=18.0.0", "immer": ">=9.0.6", "react": ">=18.0.0", "use-sync-external-store": ">=1.2.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "immer": {"optional": true}, "react": {"optional": true}, "use-sync-external-store": {"optional": true}}}, "node_modules/@ag-ui/client": {"resolved": "../../node_modules/.pnpm/@ag-ui+client@0.0.35/node_modules/@ag-ui/client", "link": true}, "node_modules/@types/react": {"resolved": "../../node_modules/.pnpm/@types+react@19.0.0/node_modules/@types/react", "link": true}, "node_modules/@types/react-dom": {"resolved": "../../node_modules/.pnpm/@types+react-dom@19.0.0/node_modules/@types/react-dom", "link": true}, "node_modules/lucide-react": {"version": "0.525.0", "resolved": "https://registry.npmjs.org/lucide-react/-/lucide-react-0.525.0.tgz", "integrity": "sha512-Tm1txJ2OkymCGkvwoHt33Y2JpN5xucVq1slHcgE6Lk0WjDfjgKWor5CdVER8U6DvcfMwh4M8XxmpTiyzfmfDYQ==", "license": "ISC", "peerDependencies": {"react": "^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/react": {"resolved": "../../node_modules/.pnpm/react@19.0.0/node_modules/react", "link": true}, "node_modules/react-dom": {"resolved": "../../node_modules/.pnpm/react-dom@19.0.0_react@19.0.0/node_modules/react-dom", "link": true}, "node_modules/typescript": {"resolved": "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript", "link": true}, "node_modules/zustand": {"resolved": "../../node_modules/.pnpm/zustand@5.0.7_@types+react@19.0.0_immer@10.1.1_react@19.0.0_use-sync-external-store@1.5.0_react@19.0.0_/node_modules/zustand", "link": true}}}