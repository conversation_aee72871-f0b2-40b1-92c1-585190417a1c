'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { getStockList, type StockNavItem } from '../actions'

interface StockNavigationProps {
  currentPath: string
  onAddStock?: () => void
  className?: string
}

function StockNavItemComponent({ 
  item, 
  isActive 
}: { 
  item: StockNavItem
  isActive: boolean 
}) {
  const isPositive = item.change !== undefined && item.change >= 0
  
  return (
    <Link href={item.href}>
      <div className={`group flex items-center justify-between px-3 py-2 rounded-lg text-sm transition-colors cursor-pointer ${
        isActive 
          ? 'bg-blue-50 text-blue-700 border border-blue-200'
          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
      }`}>
        <span className="font-medium">{item.symbol}</span>
        {item.change !== undefined && (
          <span className={`text-xs ${
            isPositive ? 'text-green-600' : 'text-red-600'
          }`}>
            {isPositive ? '+' : ''}{item.change.toFixed(2)}%
          </span>
        )}
      </div>
    </Link>
  )
}

export function StockNavigationSection({ 
  currentPath, 
  onAddStock,
  className = '' 
}: StockNavigationProps) {
  const [stocks, setStocks] = useState<StockNavItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadStocks = async () => {
      try {
        setIsLoading(true)
        setError(null)
        
        const stockData = await getStockList()
        setStocks(stockData.watchedStocks)
        
      } catch (err) {
        setError('加载股票失败')
        console.error('Failed to load stocks for navigation:', err)
      } finally {
        setIsLoading(false)
      }
    }

    loadStocks()
  }, [])

  const handleAddStock = () => {
    if (onAddStock) {
      onAddStock()
    }
  }

  if (error) {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="px-3">
          <h2 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
            我的股票
          </h2>
        </div>
        <div className="px-3 py-2 text-xs text-red-600">
          {error}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 分组标题 */}
      <div className="px-3">
        <h2 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
          我的股票
        </h2>
      </div>
      
      {/* 分组内容 */}
      <div className="space-y-1">
        {/* 股票列表 */}
        {isLoading ? (
          <div className="px-3 py-2 text-xs text-gray-500">
            加载中...
          </div>
        ) : stocks.length === 0 ? (
          <div className="px-3 py-2 text-xs text-muted-foreground">
            暂无关注股票
          </div>
        ) : (
          stocks.map((item) => (
            <StockNavItemComponent 
              key={item.symbol} 
              item={item} 
              isActive={currentPath === item.href}
            />
          ))
        )}
      </div>
    </div>
  )
}