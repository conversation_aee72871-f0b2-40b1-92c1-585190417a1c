'use client'

import { useState, useEffect } from 'react'
import { ArrowLeft } from 'lucide-react'
import { Button } from '@yai-investor-insight/shared-fe-kit'
import { StockChart } from './StockChart'
import { EnhancedCompanyInfo } from './EnhancedCompanyInfo'
import { FinancialTable } from './FinancialTable'
import { getStockInfo, getStockAnalysis, type StockAnalysis } from '../actions'
import type { StockDetailProps, StockInfo } from '../types'

export function StockDetailPage({ symbol, onClose, className = '' }: StockDetailProps) {
  const [stockInfo, setStockInfo] = useState<StockInfo | null>(null)
  const [stockAnalysis, setStockAnalysis] = useState<StockAnalysis | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadStockData = async () => {
      try {
        setIsLoading(true)
        setError(null)
        
        const [info, analysis] = await Promise.all([
          getStockInfo(symbol),
          getStockAnalysis(symbol)
        ])
        
        setStockInfo(info)
        setStockAnalysis(analysis)
      } catch (err) {
        console.error('加载股票数据失败:', err)
        setError(err instanceof Error ? err.message : '加载失败')
      } finally {
        setIsLoading(false)
      }
    }

    loadStockData()
  }, [symbol])

  if (isLoading) {
    return (
      <div className={`w-full ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">正在加载股票信息...</div>
        </div>
      </div>
    )
  }

  if (error || !stockInfo) {
    return (
      <div className={`w-full ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-red-500">{error || '未找到股票信息'}</div>
        </div>
      </div>
    )
  }

  return (
    <div className={`w-full ${className}`}>
      {/* 顶部返回按钮 */}
      <div className="flex items-center justify-between mb-6">
        <Button 
          variant="ghost" 
          onClick={onClose}
          className="text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          返回工作台
        </Button>
      </div>

      <div className="max-w-7xl mx-auto space-y-6">
        <div className="space-y-4">
          {/* 股价图表 */}
          <StockChart
            symbol={symbol}
            timeframe="1D"
            height={250}
            showVolume={true}
          />

          {/* 两栏布局：公司信息 + 财务数据 */}
          <div className="grid lg:grid-cols-2 gap-4">
            <EnhancedCompanyInfo stockInfo={stockInfo} />
            <FinancialTable symbol={symbol} />
          </div>

          {/* AI分析和相关信息 */}
          <div className="grid lg:grid-cols-1 gap-4">
            {/* AI分析卡片 */}
            <div className="bg-white rounded-lg border p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                🤖 AI投资分析
              </h3>
              <div className="space-y-4">
                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">投资建议</h4>
                  <p className="text-blue-800 text-sm">
                    {stockAnalysis?.recommendation || '暂无投资建议'}
                  </p>
                </div>
                
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-green-50 rounded-lg p-4">
                    <h4 className="font-medium text-green-900 mb-2">机会要点</h4>
                    <ul className="text-green-800 text-sm space-y-1">
                      {stockAnalysis?.opportunities.map((item, index) => (
                        <li key={index}>• {item}</li>
                      )) || <li>暂无机会要点</li>}
                    </ul>
                  </div>
                  
                  <div className="bg-red-50 rounded-lg p-4">
                    <h4 className="font-medium text-red-900 mb-2">风险提示</h4>
                    <ul className="text-red-800 text-sm space-y-1">
                      {stockAnalysis?.risks.map((item, index) => (
                        <li key={index}>• {item}</li>
                      )) || <li>暂无风险提示</li>}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}