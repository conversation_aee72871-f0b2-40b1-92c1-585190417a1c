'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@yai-investor-insight/shared-fe-kit'
import { Building, Package, DollarSign, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@yai-investor-insight/shared-fe-kit'
import type { StockInfo } from '../types'

interface EnhancedCompanyInfoProps {
  stockInfo: StockInfo
  className?: string
}

export function EnhancedCompanyInfo({ stockInfo, className = '' }: EnhancedCompanyInfoProps) {
  const [showGrowthRates, setShowGrowthRates] = useState(false)
  const [showPercentages, setShowPercentages] = useState(false)

  // 根据股票代码获取业务收入数据
  const getBusinessRevenue = () => {
    const businessData: Record<string, any> = {
      'NVDA': {
        description: "英伟达主要专注于GPU和AI计算加速器领域，其H100、A100等数据中心GPU在深度学习训练和推理市场占据主导地位。公司通过CUDA生态系统构建了强大的软件平台，为AI开发者提供完整工具链。此外，DRIVE自动驾驶平台和专业可视化产品线也为公司带来稳定增长，形成了从芯片硬件到软件生态的完整产业布局，在AI革命中占据核心地位。",
        revenue3Years: [
          { year: '2023', segments: [
            { segment: '数据中心', revenue: '$47.5B', change: '+427%', percentage: '78%' },
            { segment: '游戏', revenue: '$10.4B', change: '+15%', percentage: '17%' },
            { segment: '专业可视化', revenue: '$1.5B', change: '+108%', percentage: '2%' },
            { segment: '汽车', revenue: '$1.1B', change: '+72%', percentage: '2%' }
          ]},
          { year: '2022', segments: [
            { segment: '数据中心', revenue: '$15.0B', change: '+61%', percentage: '55%' },
            { segment: '游戏', revenue: '$9.1B', change: '-33%', percentage: '34%' },
            { segment: '专业可视化', revenue: '$1.5B', change: '-27%', percentage: '6%' },
            { segment: '汽车', revenue: '$0.6B', change: '+60%', percentage: '2%' }
          ]},
          { year: '2021', segments: [
            { segment: '数据中心', revenue: '$10.6B', change: '+124%', percentage: '40%' },
            { segment: '游戏', revenue: '$12.5B', change: '+61%', percentage: '47%' },
            { segment: '专业可视化', revenue: '$2.1B', change: '+12%', percentage: '8%' },
            { segment: '汽车', revenue: '$0.6B', change: '+6%', percentage: '2%' }
          ]}
        ]
      },
      'MSFT': {
        description: "微软是全球领先的软件和云服务提供商，在企业软件、云计算和AI领域具有强大竞争力。Copilot的推出标志着企业AI办公时代的到来。",
        revenue3Years: [
          { year: '2023', segments: [
            { segment: '生产力业务', revenue: '$69.3B', change: '+15%', percentage: '33%' },
            { segment: 'Azure云服务', revenue: '$87.9B', change: '+27%', percentage: '41%' },
            { segment: 'Windows&设备', revenue: '$54.7B', change: '+8%', percentage: '26%' }
          ]},
          { year: '2022', segments: [
            { segment: '生产力业务', revenue: '$60.3B', change: '+17%', percentage: '34%' },
            { segment: 'Azure云服务', revenue: '$69.2B', change: '+32%', percentage: '39%' },
            { segment: 'Windows&设备', revenue: '$50.7B', change: '+11%', percentage: '28%' }
          ]},
          { year: '2021', segments: [
            { segment: '生产力业务', revenue: '$51.5B', change: '+22%', percentage: '35%' },
            { segment: 'Azure云服务', revenue: '$52.4B', change: '+45%', percentage: '36%' },
            { segment: 'Windows&设备', revenue: '$45.7B', change: '+13%', percentage: '29%' }
          ]}
        ]
      }
    }

    return businessData[stockInfo.symbol] || {
      description: "公司主要业务涵盖核心产品服务、云计算解决方案和企业级应用三大板块。通过持续的技术创新和市场拓展，公司在各个细分领域都建立了竞争优势。",
      revenue3Years: [
        { year: '2023', segments: [
          { segment: '主营业务', revenue: '$150.2B', change: '+12%', percentage: '75%' },
          { segment: '云服务', revenue: '$45.8B', change: '+28%', percentage: '23%' },
          { segment: '其他业务', revenue: '$4.2B', change: '+5%', percentage: '2%' }
        ]}
      ]
    }
  }

  const businessData = getBusinessRevenue()

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-base">
          <Building className="h-4 w-4" />
          公司信息
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 基本信息 */}
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="space-y-1">
            <span className="text-gray-600">员工数</span>
            <p className="font-semibold">{stockInfo.employees || 'N/A'}</p>
          </div>
          <div className="space-y-1">
            <span className="text-gray-600">行业</span>
            <p className="font-semibold">{stockInfo.industry || stockInfo.sector || 'N/A'}</p>
          </div>
          <div className="space-y-1">
            <span className="text-gray-600">交易所</span>
            <p className="font-semibold">{stockInfo.exchange}</p>
          </div>
        </div>
        
        {/* 公司简介 */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm flex items-center gap-2">
            <Building className="h-3 w-3" />
            公司简介
          </h4>
          <p className="text-sm text-gray-600 leading-relaxed">
            {stockInfo.description || "暂无公司描述信息"}
          </p>
        </div>

        {/* 主要业务和产品线 */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm flex items-center gap-2">
            <Package className="h-3 w-3" />
            主要业务和产品线
          </h4>
          <div className="text-sm text-gray-600 leading-relaxed">
            {businessData.description}
          </div>
        </div>

        {/* 分业务收入变化 */}
        {businessData.revenue3Years && businessData.revenue3Years.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-sm flex items-center gap-2">
                <DollarSign className="h-3 w-3" />
                分业务收入变化（最近3年）
              </h4>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowGrowthRates(!showGrowthRates)}
                  className="text-xs h-6 px-2 text-gray-600 hover:text-gray-900"
                >
                  {showGrowthRates ? (
                    <>
                      <ChevronUp className="h-3 w-3 mr-1" />
                      隐藏增长率
                    </>
                  ) : (
                    <>
                      <ChevronDown className="h-3 w-3 mr-1" />
                      显示增长率
                    </>
                  )}
                </Button>
                <Button
                  variant={showPercentages ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setShowPercentages(!showPercentages)}
                  className="text-xs h-6"
                >
                  {showPercentages ? '营收金额' : '占比显示'}
                </Button>
              </div>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 text-xs">业务板块</th>
                    {businessData.revenue3Years.map((yearData: any) => (
                      <th key={yearData.year} className="text-center py-2 text-xs">
                        {yearData.year}年
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {/* 营收数据行 */}
                  {businessData.revenue3Years[0]?.segments.map((segment: any, segmentIndex: number) => (
                    <tr key={segmentIndex} className="border-b border-gray-100">
                      <td className="py-2 font-medium text-xs">
                        {businessData.revenue3Years[0].segments[segmentIndex].segment}
                      </td>
                      {businessData.revenue3Years.map((yearData: any) => (
                        <td key={yearData.year} className="text-center py-2 text-xs">
                          <div className="font-medium">
                            {showPercentages 
                              ? yearData.segments[segmentIndex]?.percentage
                              : yearData.segments[segmentIndex]?.revenue
                            }
                          </div>
                        </td>
                      ))}
                    </tr>
                  ))}
                  
                  {/* YoY增长率 */}
                  {showGrowthRates && (
                    <>
                      <tr className="bg-gray-50">
                        <td className="py-2 font-semibold text-xs text-blue-600">
                          YoY增长率
                        </td>
                        {businessData.revenue3Years.map((yearData: any) => (
                          <td key={yearData.year} className="text-center py-2 text-xs font-medium text-blue-600">
                            {yearData.year}年
                          </td>
                        ))}
                      </tr>
                      
                      {businessData.revenue3Years[0]?.segments.map((segment: any, segmentIndex: number) => (
                        <tr key={`yoy-${segmentIndex}`} className="border-b border-gray-100">
                          <td className="py-2 font-medium text-xs text-gray-600">
                            {businessData.revenue3Years[0].segments[segmentIndex].segment}
                          </td>
                          {businessData.revenue3Years.map((yearData: any) => (
                            <td key={yearData.year} className="text-center py-2 text-xs">
                              <span className={`font-medium ${
                                yearData.segments[segmentIndex]?.change.startsWith('+') 
                                  ? 'text-green-600' 
                                  : 'text-red-600'
                              }`}>
                                {yearData.segments[segmentIndex]?.change}
                              </span>
                            </td>
                          ))}
                        </tr>
                      ))}
                    </>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}