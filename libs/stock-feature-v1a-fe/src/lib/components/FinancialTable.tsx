'use client'

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@yai-investor-insight/shared-fe-kit'
import { TrendingUp, DollarSign, Calendar } from 'lucide-react'
import type { FinancialMetrics } from '../types'

interface FinancialTableProps {
  symbol: string
  financialData?: FinancialMetrics
  className?: string
}

export function FinancialTable({ symbol, financialData, className = '' }: FinancialTableProps) {
  // 根据股票代码获取财务数据
  const getFinancialData = (): FinancialMetrics => {
    const defaultData: Record<string, FinancialMetrics> = {
      'NVDA': {
        marketCap: 1780000000000, // $1.78T
        peRatio: 65.4,
        pbRatio: 12.8,
        dividendYield: 0.03,
        eps: 11.05,
        revenue: 60900000000, // $60.9B
        netIncome: 29800000000, // $29.8B
        totalDebt: 8950000000, // $8.95B
        totalAssets: 85000000000, // $85B
        freeCashFlow: 26000000000 // $26B
      },
      'MSFT': {
        marketCap: 2890000000000, // $2.89T
        peRatio: 32.1,
        pbRatio: 5.2,
        dividendYield: 0.72,
        eps: 11.78,
        revenue: 211900000000, // $211.9B
        netIncome: 72400000000, // $72.4B
        totalDebt: 47000000000, // $47B
        totalAssets: 484000000000, // $484B
        freeCashFlow: 56800000000 // $56.8B
      },
      'CRM': {
        marketCap: 249000000000, // $249B
        peRatio: 58.2,
        pbRatio: 2.8,
        dividendYield: 0,
        eps: 4.29,
        revenue: 31300000000, // $31.3B
        netIncome: 4130000000, // $4.13B
        totalDebt: 8500000000, // $8.5B
        totalAssets: 98000000000, // $98B
        freeCashFlow: 6200000000 // $6.2B
      }
    }

    return financialData || defaultData[symbol] || {
      marketCap: 0,
      peRatio: 0,
      pbRatio: 0,
      dividendYield: 0,
      eps: 0,
      revenue: 0,
      netIncome: 0,
      totalDebt: 0,
      totalAssets: 0,
      freeCashFlow: 0
    }
  }

  const financial = getFinancialData()

  // 格式化数字
  const formatNumber = (num: number, type: 'currency' | 'percentage' | 'ratio' = 'currency') => {
    if (type === 'percentage') {
      return `${(num * 100).toFixed(2)}%`
    }
    if (type === 'ratio') {
      return num.toFixed(2)
    }
    
    // 货币格式化
    if (num >= 1000000000000) {
      return `$${(num / 1000000000000).toFixed(1)}T`
    }
    if (num >= 1000000000) {
      return `$${(num / 1000000000).toFixed(1)}B`
    }
    if (num >= 1000000) {
      return `$${(num / 1000000).toFixed(1)}M`
    }
    return `$${num.toLocaleString()}`
  }

  // 获取近期季度数据
  const getRecentQuarters = () => {
    const quarterData: Record<string, any[]> = {
      'NVDA': [
        { quarter: 'Q4 2024', revenue: '$22.1B', growth: '+22%' },
        { quarter: 'Q3 2024', revenue: '$18.1B', growth: '+34%' },
        { quarter: 'Q2 2024', revenue: '$13.5B', growth: '+88%' },
        { quarter: 'Q1 2024', revenue: '$7.2B', growth: '+19%' }
      ],
      'MSFT': [
        { quarter: 'Q4 2024', revenue: '$56.2B', growth: '+15%' },
        { quarter: 'Q3 2024', revenue: '$52.9B', growth: '+17%' },
        { quarter: 'Q2 2024', revenue: '$50.4B', growth: '+18%' },
        { quarter: 'Q1 2024', revenue: '$48.1B', growth: '+13%' }
      ],
      'CRM': [
        { quarter: 'Q4 2024', revenue: '$9.3B', growth: '+11%' },
        { quarter: 'Q3 2024', revenue: '$8.7B', growth: '+14%' },
        { quarter: 'Q2 2024', revenue: '$8.6B', growth: '+12%' },
        { quarter: 'Q1 2024', revenue: '$8.3B', growth: '+10%' }
      ]
    }

    return quarterData[symbol] || [
      { quarter: 'Q4 2024', revenue: '$10.0B', growth: '+8%' },
      { quarter: 'Q3 2024', revenue: '$9.2B', growth: '+6%' },
      { quarter: 'Q2 2024', revenue: '$8.8B', growth: '+4%' },
      { quarter: 'Q1 2024', revenue: '$8.5B', growth: '+2%' }
    ]
  }

  const quarters = getRecentQuarters()

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-base">
          <DollarSign className="h-4 w-4" />
          财务数据
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 核心财务指标 */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">核心指标</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">市值</span>
                <span className="font-medium">{formatNumber(financial.marketCap)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">P/E 比率</span>
                <span className="font-medium">{formatNumber(financial.peRatio, 'ratio')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">P/B 比率</span>
                <span className="font-medium">{formatNumber(financial.pbRatio, 'ratio')}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">股息率</span>
                <span className="font-medium">{formatNumber(financial.dividendYield, 'percentage')}</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">每股收益</span>
                <span className="font-medium">${financial.eps.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">年收入</span>
                <span className="font-medium">{formatNumber(financial.revenue)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">净利润</span>
                <span className="font-medium">{formatNumber(financial.netIncome)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">自由现金流</span>
                <span className="font-medium">{formatNumber(financial.freeCashFlow)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* 资产负债 */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">资产负债</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">总资产</span>
              <span className="font-medium">{formatNumber(financial.totalAssets)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">总负债</span>
              <span className="font-medium">{formatNumber(financial.totalDebt)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">资产负债率</span>
              <span className="font-medium">
                {((financial.totalDebt / financial.totalAssets) * 100).toFixed(1)}%
              </span>
            </div>
          </div>
        </div>

        {/* 近期季度业绩 */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm flex items-center gap-2">
            <Calendar className="h-3 w-3" />
            近期季度业绩
          </h4>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 text-xs">季度</th>
                  <th className="text-right py-2 text-xs">营收</th>
                  <th className="text-right py-2 text-xs">增长率</th>
                </tr>
              </thead>
              <tbody>
                {quarters.map((quarter, index) => (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-2 text-xs">{quarter.quarter}</td>
                    <td className="text-right py-2 text-xs font-medium">{quarter.revenue}</td>
                    <td className="text-right py-2 text-xs">
                      <span className={`flex items-center justify-end gap-1 ${
                        quarter.growth.startsWith('+') ? 'text-green-600' : 'text-red-600'
                      }`}>
                        <TrendingUp className="h-3 w-3" />
                        {quarter.growth}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}