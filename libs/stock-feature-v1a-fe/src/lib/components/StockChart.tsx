'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle, Button } from '@yai-investor-insight/shared-fe-kit'
import { TrendingUp, TrendingDown, BarChart3 } from 'lucide-react'
import type { StockChartProps } from '../types'

export function StockChart({ 
  symbol, 
  timeframe = '1D', 
  height = 200, 
  showVolume = true, 
  className = '' 
}: StockChartProps) {
  const [currentTimeframe, setCurrentTimeframe] = useState(timeframe)

  // 模拟股票数据
  const getStockData = () => {
    const stockData: Record<string, any> = {
      'NVDA': {
        name: '英伟达公司',
        price: '$722.48',
        change: '+2.3%',
        type: 'bullish' as const
      },
      'CRM': {
        name: 'Salesforce公司',
        price: '$249.63',
        change: '-0.8%',
        type: 'bearish' as const
      },
      'MSFT': {
        name: '微软公司',
        price: '$378.24',
        change: '+0.9%',
        type: 'bullish' as const
      },
      'GOOGL': {
        name: '谷歌公司',
        price: '$138.21',
        change: '+0.7%',
        type: 'bullish' as const
      },
      'TSLA': {
        name: '特斯拉公司',
        price: '$243.84',
        change: '+3.2%',
        type: 'bullish' as const
      },
      'AMZN': {
        name: '亚马逊公司',
        price: '$151.94',
        change: '+1.2%',
        type: 'bullish' as const
      }
    }
    
    return stockData[symbol] || {
      name: symbol,
      price: '$100.00',
      change: '+0.0%',
      type: 'bullish' as const
    }
  }

  const stockData = getStockData()

  // 模拟不同时间框架的价格数据
  const getPriceData = () => {
    switch (currentTimeframe) {
      case '1D':
        return [
          { time: "09:30", price: 715, volume: 2.1 },
          { time: "10:00", price: 718, volume: 1.8 },
          { time: "10:30", price: 720, volume: 2.3 },
          { time: "11:00", price: 722, volume: 1.9 },
          { time: "11:30", price: 719, volume: 2.5 },
          { time: "12:00", price: 721, volume: 1.7 },
          { time: "12:30", price: 723, volume: 2.0 },
          { time: "13:00", price: 725, volume: 2.2 },
          { time: "13:30", price: 722, volume: 1.8 },
          { time: "14:00", price: 724, volume: 2.1 },
          { time: "14:30", price: 726, volume: 1.9 },
          { time: "15:00", price: 722, volume: 2.3 },
          { time: "15:30", price: 720, volume: 2.0 }
        ]
      case '5D':
        return [
          { time: "周一", price: 715, volume: 45.2 },
          { time: "周二", price: 720, volume: 52.1 },
          { time: "周三", price: 718, volume: 48.7 },
          { time: "周四", price: 725, volume: 55.3 },
          { time: "周五", price: 722, volume: 51.8 }
        ]
      case '1M':
        return [
          { time: "W1", price: 680, volume: 250 },
          { time: "W2", price: 695, volume: 280 },
          { time: "W3", price: 710, volume: 310 },
          { time: "W4", price: 722, volume: 290 }
        ]
      default:
        return [
          { time: "09:30", price: 715, volume: 2.1 },
          { time: "15:30", price: 720, volume: 2.0 }
        ]
    }
  }

  const priceData = getPriceData()
  const prices = priceData.map(d => d.price)
  const minPrice = Math.min(...prices)
  const maxPrice = Math.max(...prices)
  const priceRange = maxPrice - minPrice

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-primary/10">
              {stockData.type === "bullish" ? (
                <TrendingUp className="h-5 w-5 text-green-600" />
              ) : (
                <TrendingDown className="h-5 w-5 text-red-600" />
              )}
            </div>
            <div>
              <CardTitle className="text-xl font-bold">{symbol}</CardTitle>
              <p className="text-sm text-gray-600">{stockData.name}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">{stockData.price}</div>
            <div className={`text-base font-medium flex items-center gap-1 ${
              stockData.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
            }`}>
              {stockData.change.startsWith('+') ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
              {stockData.change}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-gray-600" />
              <span className="text-sm font-medium">股价走势</span>
              <span className={`px-2 py-1 rounded text-xs ${stockData.type === "bullish" ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"}`}>
                {stockData.type === "bullish" ? "看涨" : "看跌"}
              </span>
            </div>
            
            {/* 时间框架切换 */}
            <div className="flex gap-1">
              {['1D', '5D', '1M', '3M', '6M', '1Y'].map((tf) => (
                <Button
                  key={tf}
                  variant={currentTimeframe === tf ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setCurrentTimeframe(tf as any)}
                  className="text-xs h-7"
                >
                  {tf}
                </Button>
              ))}
            </div>
          </div>
          
          {/* 图表区域 */}
          <div className="relative bg-gray-50 rounded-lg p-4" style={{ height: `${height}px` }}>
            <div className="flex items-end justify-between h-full">
              {priceData.map((point, index) => {
                const heightPercent = priceRange > 0 ? ((point.price - minPrice) / priceRange) * 100 : 50
                return (
                  <div key={index} className="flex flex-col items-center gap-1">
                    <div 
                      className={`w-2 rounded-t transition-all duration-300 ${
                        stockData.type === "bullish" ? "bg-green-500" : "bg-red-500"
                      }`}
                      style={{ height: `${Math.max(heightPercent, 10)}%` }}
                      title={`${point.time}: $${point.price}`}
                    />
                    {(currentTimeframe === '1D' ? index % 3 === 0 : true) && (
                      <span className="text-xs text-gray-600 mt-1">
                        {point.time}
                      </span>
                    )}
                  </div>
                )
              })}
            </div>
          </div>

          {/* 价格区间和成交量 */}
          {showVolume && (
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-1">
                <div>
                  <span className="text-gray-600">最低: </span>
                  <span className="font-medium">${minPrice.toFixed(2)}</span>
                </div>
                <div>
                  <span className="text-gray-600">最高: </span>
                  <span className="font-medium">${maxPrice.toFixed(2)}</span>
                </div>
              </div>
              <div className="space-y-1">
                <div>
                  <span className="text-gray-600">成交量: </span>
                  <span className="font-medium">
                    {currentTimeframe === '5D' ? '251.1M' : 
                     currentTimeframe === '1D' ? '25.1M' : '45.2M'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">换手率: </span>
                  <span className="font-medium">2.34%</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}