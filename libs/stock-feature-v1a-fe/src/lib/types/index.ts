// 股票基本信息
export interface StockInfo {
  symbol: string
  name: string
  exchange: string
  sector?: string
  industry?: string
  description?: string
  website?: string
  employees?: number
  marketCap?: number
  peRatio?: number
  dividendYield?: number
}

// 股票价格数据
export interface StockPrice {
  symbol: string
  price: number
  change: number
  changePercent: number
  volume: number
  high: number
  low: number
  open: number
  previousClose: number
  timestamp: Date
}

// 历史价格数据点
export interface PriceDataPoint {
  date: string
  open: number
  high: number
  low: number
  close: number
  volume: number
}

// 股票图表数据
export interface StockChartData {
  symbol: string
  timeframe: '1D' | '5D' | '1M' | '3M' | '6M' | '1Y' | '2Y' | '5Y'
  data: PriceDataPoint[]
}

// 财务指标
export interface FinancialMetrics {
  marketCap: number
  peRatio: number
  pbRatio: number
  dividendYield: number
  eps: number
  revenue: number
  netIncome: number
  totalDebt: number
  totalAssets: number
  freeCashFlow: number
}

// 新闻/事件
export interface NewsItem {
  id: string
  title: string
  summary: string
  source: string
  publishedAt: Date
  url: string
  sentiment?: 'positive' | 'negative' | 'neutral'
  relevanceScore?: number
}

// 分析师评级
export interface AnalystRating {
  firm: string
  analyst: string
  rating: 'Strong Buy' | 'Buy' | 'Hold' | 'Sell' | 'Strong Sell'
  targetPrice: number
  currentPrice: number
  updatedAt: Date
  reasoning?: string
}

// 股票详情页面的 Props
export interface StockDetailProps {
  symbol: string
  onClose?: () => void
  className?: string
}

// 股票图表组件的 Props
export interface StockChartProps {
  symbol: string
  timeframe?: '1D' | '5D' | '1M' | '3M' | '6M' | '1Y' | '2Y' | '5Y'
  height?: number
  showVolume?: boolean
  className?: string
}

// 股票搜索结果
export interface StockSearchResult {
  symbol: string
  name: string
  exchange: string
  price?: number
  change?: number
  changePercent?: number
}

// 股票分析数据
export interface StockAnalysis {
  symbol: string
  recommendation: 'Strong Buy' | 'Buy' | 'Hold' | 'Sell' | 'Strong Sell'
  confidence: number
  keyPoints: string[]
  risks: string[]
  opportunities: string[]
  targetPrice: number
  timeHorizon: '1M' | '3M' | '6M' | '1Y'
  lastUpdated: Date
}