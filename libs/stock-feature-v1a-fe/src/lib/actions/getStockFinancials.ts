'use server'

export interface StockFinancials {
  symbol: string
  financialData: Array<{
    metric: string
    value: string
    unit?: string
    change?: string
  }>
  lastUpdated: string
}

/**
 * 获取股票财务数据
 * 包括主要财务指标和比率
 */
export async function getStockFinancials(symbol: string): Promise<StockFinancials> {
  try {
    // TODO: 集成实际的后端 API
    // 这里需要调用后端服务来获取真实的财务数据
    
    const financialDataMap: Record<string, Array<{ metric: string; value: string; unit?: string; change?: string }>> = {
      'NVDA': [
        { metric: '市值', value: '1.78', unit: '万亿美元', change: '+12.5%' },
        { metric: '市盈率(TTM)', value: '65.4', change: '+8.2%' },
        { metric: '市销率(TTM)', value: '18.7', change: '+15.3%' },
        { metric: '每股收益(TTM)', value: '13.38', unit: '美元', change: '+168%' },
        { metric: '营收(TTM)', value: '958.7', unit: '亿美元', change: '+126%' },
        { metric: '毛利率', value: '75.1%', change: '+2.8pp' },
        { metric: '净利润率', value: '48.9%', change: '+12.1pp' },
        { metric: '资产负债率', value: '18.2%', change: '-2.1pp' },
        { metric: '股息收益率', value: '0.03%', change: '持平' },
        { metric: 'ROE', value: '125.3%', change: '+85.2pp' },
        { metric: 'ROA', value: '68.9%', change: '+42.1pp' },
        { metric: '自由现金流', value: '282.1', unit: '亿美元', change: '+215%' }
      ],
      'MSFT': [
        { metric: '市值', value: '2.89', unit: '万亿美元', change: '+8.7%' },
        { metric: '市盈率(TTM)', value: '32.1', change: '+4.2%' },
        { metric: '市销率(TTM)', value: '12.8', change: '+6.1%' },
        { metric: '每股收益(TTM)', value: '11.84', unit: '美元', change: '+18.5%' },
        { metric: '营收(TTM)', value: '2277.3', unit: '亿美元', change: '+13.2%' },
        { metric: '毛利率', value: '68.4%', change: '+1.2pp' },
        { metric: '净利润率', value: '36.7%', change: '+2.8pp' },
        { metric: '资产负债率', value: '22.1%', change: '-1.5pp' },
        { metric: '股息收益率', value: '0.72%', change: '+0.08pp' },
        { metric: 'ROE', value: '41.2%', change: '+3.8pp' },
        { metric: 'ROA', value: '18.9%', change: '+2.1pp' },
        { metric: '自由现金流', value: '658.4', unit: '亿美元', change: '+15.8%' }
      ],
      'CRM': [
        { metric: '市值', value: '2490', unit: '亿美元', change: '+5.2%' },
        { metric: '市盈率(TTM)', value: '58.2', change: '+12.8%' },
        { metric: '市销率(TTM)', value: '8.9', change: '+2.1%' },
        { metric: '每股收益(TTM)', value: '4.59', unit: '美元', change: '+22.3%' },
        { metric: '营收(TTM)', value: '315.2', unit: '亿美元', change: '+11.2%' },
        { metric: '毛利率', value: '77.8%', change: '+1.5pp' },
        { metric: '净利润率', value: '1.1%', change: '+8.2pp' },
        { metric: '资产负债率', value: '15.8%', change: '-2.3pp' },
        { metric: '股息收益率', value: '0%', change: '无股息' },
        { metric: 'ROE', value: '2.8%', change: '+18.9pp' },
        { metric: 'ROA', value: '1.2%', change: '+6.8pp' },
        { metric: '自由现金流', value: '58.7', unit: '亿美元', change: '+8.9%' }
      ]
    }

    // 模拟 API 调用延迟
    await new Promise(resolve => setTimeout(resolve, 100))

    const upperSymbol = symbol.toUpperCase()
    const defaultFinancials = [
      { metric: '市值', value: 'N/A', unit: '美元' },
      { metric: '市盈率(TTM)', value: 'N/A' },
      { metric: '市销率(TTM)', value: 'N/A' },
      { metric: '每股收益(TTM)', value: 'N/A', unit: '美元' },
      { metric: '营收(TTM)', value: 'N/A', unit: '美元' },
      { metric: '毛利率', value: 'N/A' }
    ]

    const financials: StockFinancials = {
      symbol: upperSymbol,
      financialData: financialDataMap[upperSymbol] || defaultFinancials,
      lastUpdated: new Date().toISOString()
    }

    console.log('获取股票财务数据:', { symbol: upperSymbol, metrics: financials.financialData.length })
    
    return financials
    
  } catch (error) {
    console.error('获取股票财务数据失败:', error)
    throw new Error(`获取股票 ${symbol} 财务数据失败，请稍后重试`)
  }
}