'use server'

export interface StockChartData {
  symbol: string
  timeframe: string
  prices: Array<{
    timestamp: string
    open: number
    high: number
    low: number
    close: number
    volume: number
  }>
  currentPrice: number
  change: number
  changePercent: number
}

/**
 * 获取股票图表数据
 * 包括历史价格、成交量等信息
 */
export async function getStockChart(symbol: string, timeframe: string = '1D'): Promise<StockChartData> {
  try {
    // TODO: 集成实际的后端 API
    // 这里需要调用后端服务来获取真实的股票价格数据
    
    // 生成模拟的股票价格数据
    const generateMockPrices = (basePrice: number, days: number = 30): Array<{
      timestamp: string
      open: number
      high: number
      low: number
      close: number
      volume: number
    }> => {
      const prices: Array<{
        timestamp: string
        open: number
        high: number
        low: number
        close: number
        volume: number
      }> = []
      let currentPrice = basePrice
      
      for (let i = days; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        
        // 模拟价格波动 (-5% 到 +5%)
        const changePercent = (Math.random() - 0.5) * 0.1
        const open = currentPrice
        const change = open * changePercent
        const close = open + change
        const high = Math.max(open, close) * (1 + Math.random() * 0.03)
        const low = Math.min(open, close) * (1 - Math.random() * 0.03)
        const volume = Math.floor(Math.random() * 10000000) + 1000000
        
        prices.push({
          timestamp: date.toISOString(),
          open: Number(open.toFixed(2)),
          high: Number(high.toFixed(2)),
          low: Number(low.toFixed(2)),
          close: Number(close.toFixed(2)),
          volume
        })
        
        currentPrice = close
      }
      
      return prices
    }

    // 不同股票的基准价格
    const basePrices: Record<string, number> = {
      'NVDA': 875.28,
      'MSFT': 378.24,
      'CRM': 267.45,
      'GOOGL': 142.56,
      'TSLA': 248.42,
      'AMZN': 151.94
    }

    // 模拟 API 调用延迟
    await new Promise(resolve => setTimeout(resolve, 100))

    const upperSymbol = symbol.toUpperCase()
    const basePrice = basePrices[upperSymbol] || 100
    const prices = generateMockPrices(basePrice)
    
    const currentPrice = prices[prices.length - 1].close
    const previousPrice = prices[prices.length - 2].close
    const change = currentPrice - previousPrice
    const changePercent = (change / previousPrice) * 100

    const chartData: StockChartData = {
      symbol: upperSymbol,
      timeframe,
      prices,
      currentPrice: Number(currentPrice.toFixed(2)),
      change: Number(change.toFixed(2)),
      changePercent: Number(changePercent.toFixed(2))
    }

    console.log('获取股票图表数据:', { symbol: upperSymbol, currentPrice, change, changePercent })
    
    return chartData
    
  } catch (error) {
    console.error('获取股票图表数据失败:', error)
    throw new Error(`获取股票 ${symbol} 图表数据失败，请稍后重试`)
  }
}