'use server'

export interface StockNavItem {
  symbol: string
  name: string
  change?: number
  href: string
}

export interface StockListResponse {
  watchedStocks: StockNavItem[]
}

/**
 * 获取用户关注的股票列表
 * 用于导航栏显示
 */
export async function getStockList(): Promise<StockListResponse> {
  try {
    // TODO: 集成实际的后端 API
    // 这里需要调用后端服务来获取用户关注的股票列表

    // 模拟 API 调用延迟
    await new Promise(resolve => setTimeout(resolve, 100))

    // 模拟用户关注的股票数据
    const watchedStocks: StockNavItem[] = [
      { symbol: 'CRM', name: 'Salesforce', change: 2.3, href: '/workbench-v1a/stocks/CRM' },
      { symbol: 'MSFT', name: 'Microsoft', change: -1.2, href: '/workbench-v1a/stocks/MSFT' },
      { symbol: 'NVDA', name: 'NVIDIA', change: 4.1, href: '/workbench-v1a/stocks/NVDA' },
      { symbol: 'TSLA', name: 'Te<PERSON>', change: -0.5, href: '/workbench-v1a/stocks/TSLA' },
      { symbol: 'GOOGL', name: 'Alphabet', change: 1.8, href: '/workbench-v1a/stocks/GOOGL' },
      { symbol: 'AMZN', name: 'Amazon', change: -2.1, href: '/workbench-v1a/stocks/AMZN' }
    ]

    const response: StockListResponse = {
      watchedStocks
    }

    console.log('获取关注股票列表:', { count: watchedStocks.length })
    
    return response
    
  } catch (error) {
    console.error('获取股票列表失败:', error)
    throw new Error('获取股票列表失败，请稍后重试')
  }
}