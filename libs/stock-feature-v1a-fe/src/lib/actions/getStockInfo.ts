'use server'

import type { StockInfo } from '../types'

/**
 * 获取股票基本信息和投资分析
 * 这是股票模块内部的 server action，负责获取股票的完整信息
 */
export async function getStockInfo(symbol: string): Promise<StockInfo | null> {
  try {
    // TODO: 集成实际的后端 API
    // 这里需要调用后端服务来获取真实的股票数据
    
    const stockData: Record<string, StockInfo> = {
      'NVDA': {
        symbol: 'NVDA',
        name: '英伟达公司',
        exchange: 'NASDAQ',
        sector: '半导体',
        industry: 'GPU芯片',
        description: '英伟达是一家专注于图形处理器和AI芯片设计的科技公司，在数据中心、游戏和自动驾驶领域占据领导地位。H200芯片的发布进一步巩固了其在AI训练和推理领域的技术优势。',
        website: 'https://www.nvidia.com',
        employees: 26196,
        marketCap: 1780000000000,
        peRatio: 65.4,
        dividendYield: 0.03
      },
      'CRM': {
        symbol: 'CRM',
        name: 'Salesforce公司',
        exchange: 'NYSE',
        sector: '软件',
        industry: 'CRM软件',
        description: 'Salesforce是全球领先的客户关系管理(CRM)软件提供商，通过云平台帮助企业管理销售、服务、营销等业务流程。',
        website: 'https://www.salesforce.com',
        employees: 73000,
        marketCap: 249000000000,
        peRatio: 58.2,
        dividendYield: 0
      },
      'MSFT': {
        symbol: 'MSFT',
        name: '微软公司',
        exchange: 'NASDAQ',
        sector: '软件',
        industry: '操作系统&云服务',
        description: '微软是全球领先的软件和云服务提供商，在企业软件、云计算和AI领域具有强大竞争力。Copilot的推出标志着企业AI办公时代的到来。',
        website: 'https://www.microsoft.com',
        employees: 221000,
        marketCap: 2890000000000,
        peRatio: 32.1,
        dividendYield: 0.72
      },
      'GOOGL': {
        symbol: 'GOOGL',
        name: '谷歌公司',
        exchange: 'NASDAQ',
        sector: '互联网',
        industry: '搜索引擎&云服务',
        description: '谷歌是全球最大的搜索引擎和在线广告公司，在云计算、人工智能、自动驾驶等前沿技术领域投资巨大。YouTube和Android也是其重要业务。',
        website: 'https://www.google.com',
        employees: 182502,
        marketCap: 1730000000000,
        peRatio: 23.4,
        dividendYield: 0
      },
      'TSLA': {
        symbol: 'TSLA',
        name: '特斯拉公司',
        exchange: 'NASDAQ',
        sector: '汽车',
        industry: '电动汽车',
        description: '特斯拉是全球电动汽车和清洁能源公司的领导者，致力于推动世界向可持续能源转型。在自动驾驶、储能和太阳能领域也有重要布局。',
        website: 'https://www.tesla.com',
        employees: 140473,
        marketCap: 774800000000,
        peRatio: 63.2,
        dividendYield: 0
      },
      'AMZN': {
        symbol: 'AMZN',
        name: '亚马逊公司',
        exchange: 'NASDAQ',
        sector: '电商',
        industry: '电子商务&云服务',
        description: '亚马逊是全球最大的电子商务和云计算公司，AWS云服务是其主要利润来源。在物流、人工智能和数字广告领域也有重要地位。',
        website: 'https://www.amazon.com',
        employees: 1541000,
        marketCap: 1540000000000,
        peRatio: 43.8,
        dividendYield: 0
      }
    }

    // 模拟 API 调用延迟
    await new Promise(resolve => setTimeout(resolve, 100))
    
    const stockInfo = stockData[symbol.toUpperCase()]
    
    if (!stockInfo) {
      return {
        symbol: symbol.toUpperCase(),
        name: symbol.toUpperCase(),
        exchange: 'UNKNOWN',
        description: '暂无详细信息'
      }
    }
    
    console.log('获取股票信息:', stockInfo)
    
    return stockInfo
    
  } catch (error) {
    console.error('获取股票信息失败:', error)
    throw new Error(`获取股票 ${symbol} 信息失败，请稍后重试`)
  }
}