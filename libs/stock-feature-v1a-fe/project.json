{"name": "stock-feature-v1a-fe", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/stock-feature-v1a-fe/src", "projectType": "library", "tags": ["scope:stock", "type:feature", "platform:web"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "web", "outputPath": "dist/libs/stock-feature-v1a-fe", "main": "libs/stock-feature-v1a-fe/src/index.ts", "tsConfig": "libs/stock-feature-v1a-fe/tsconfig.lib.json", "webpackConfig": "libs/stock-feature-v1a-fe/webpack.config.cjs"}}, "lint": {"executor": "@nx/eslint:lint"}, "type-check": {"executor": "nx:run-commands", "options": {"command": "tsc --noEmit -p libs/stock-feature-v1a-fe/tsconfig.lib.json"}}}}