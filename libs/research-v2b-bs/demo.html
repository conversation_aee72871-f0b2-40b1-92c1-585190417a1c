<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投资洞察 Demo - 重定向</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background-color: #f5f5f5;
            text-align: center;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .redirect-info {
            margin-bottom: 30px;
        }
        .redirect-info h1 {
            color: #1f2937;
            margin-bottom: 20px;
        }
        .redirect-info p {
            color: #6b7280;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .input-section {
            margin-bottom: 30px;
        }
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        input[type="text"] {
            flex: 1;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 16px;
        }
        button {
            padding: 12px 24px;
            background: #4f46e5;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
        }
        button:hover {
            background: #4338ca;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .progress-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8fafc;
            border-radius: 6px;
            display: none;
        }
        .progress-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }
        .progress-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        .progress-icon.pending {
            background: #e5e7eb;
            color: #6b7280;
        }
        .progress-icon.running {
            background: #3b82f6;
            color: white;
            animation: spin 1s linear infinite;
        }
        .progress-icon.completed {
            background: #10b981;
            color: white;
        }
        .progress-icon.error {
            background: #ef4444;
            color: white;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .progress-content {
            flex: 1;
        }
        .progress-title {
            font-weight: 500;
            margin-bottom: 5px;
        }
        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: #3b82f6;
            transition: width 0.3s ease;
        }
        .results-section {
            margin-top: 30px;
            display: none;
        }
        .result-content {
            background: #f8fafc;
            padding: 20px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 20px;
        }
        .status.connected {
            background: #d1fae5;
            color: #065f46;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
        }
        .ideas-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .idea-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-left: 4px solid #10b981;
        }
        .idea-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #1f2937;
        }
        .idea-symbol {
            display: inline-block;
            background: #3b82f6;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .idea-change {
            color: #10b981;
            font-weight: 600;
            margin-left: 10px;
        }
        .idea-points {
            margin: 15px 0;
        }
        .idea-points h4 {
            font-size: 14px;
            margin-bottom: 8px;
            color: #374151;
        }
        .idea-points ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .idea-points li {
            font-size: 13px;
            color: #6b7280;
            margin-bottom: 4px;
            padding-left: 12px;
            position: relative;
        }
        .idea-points li:before {
            content: "•";
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="redirect-info">
            <h1>🚀 投资洞察 AI 分析平台</h1>
            <p>Demo 已升级为基于 React + TypeScript 的现代化页面实现</p>
            <p>正在自动跳转到新的演示页面...</p>
        </div>
        
        <div style="margin: 30px 0;">
            <div style="display: inline-block; padding: 20px; background: #f3f4f6; border-radius: 8px; margin-bottom: 20px;">
                <div style="font-size: 48px; margin-bottom: 10px;">⏳</div>
                <div style="color: #6b7280;">页面跳转中...</div>
            </div>
        </div>

        <div style="margin-top: 30px;">
            <p style="color: #6b7280; font-size: 14px;">
                如果页面没有自动跳转，请点击下方链接：
            </p>
            <button onclick="window.location.href='/investment-insight'" style="padding: 12px 24px; background: #4f46e5; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 16px; margin-top: 10px;">
                前往投资洞察页面
            </button>
        </div>
    </div>

    <script>
        // 自动跳转到新的React页面
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟3秒后跳转，给用户时间看到提示信息
            setTimeout(function() {
                // 检查当前是否在前端应用环境中
                if (window.location.hostname === 'localhost' && window.location.port === '3000') {
                    // 前端开发环境
                    window.location.href = '/investment-insight';
                } else {
                    // 后端演示环境，提供说明
                    document.querySelector('.redirect-info').innerHTML = `
                        <h1>🚀 投资洞察 AI 分析平台</h1>
                        <p>Demo 已升级为基于 React + TypeScript 的现代化实现</p>
                        <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: left;">
                            <h3 style="color: #92400e; margin-top: 0;">🔧 如何访问新版Demo：</h3>
                            <ol style="color: #92400e; line-height: 1.8;">
                                <li>启动前端应用：<code>cd apps/web-app && npm run dev</code></li>
                                <li>访问：<a href="http://localhost:3000/investment-insight" target="_blank" style="color: #1d4ed8;">http://localhost:3000/investment-insight</a></li>
                            </ol>
                        </div>
                        <div style="background: #e0f2fe; border: 1px solid #0284c7; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: left;">
                            <h3 style="color: #0c4a6e; margin-top: 0;">✨ 新版特性：</h3>
                            <ul style="color: #0c4a6e; line-height: 1.8;">
                                <li>基于 React + TypeScript 的现代化UI</li>
                                <li>通过 Next.js API 路由转发后端请求</li>
                                <li>完整的类型安全和错误处理</li>
                                <li>响应式设计和更好的用户体验</li>
                            </ul>
                        </div>
                    `;
                }
            }, 3000);
        });
    </script>
</body>
</html> 