#!/usr/bin/env python3
"""
API 测试脚本
用于测试投资分析API端点
"""

import asyncio
import aiohttp
import json
import time

async def test_investment_api():
    """测试投资分析API"""
    
    # 测试数据
    test_query = "分析NVIDIA的投资价值"
    base_url = "http://localhost:8000"
    
    print("🧪 开始测试投资分析API")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        # 1. 测试提交任务
        print("1️⃣ 测试任务提交...")
        
        submit_url = f"{base_url}/api/v1/investment/submit"
        payload = {"query": test_query}
        
        try:
            async with session.post(submit_url, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    task_id = result.get('task_id')
                    print(f"✅ 任务提交成功: {task_id}")
                    
                    # 2. 测试任务状态查询
                    print("2️⃣ 测试任务状态查询...")
                    status_url = f"{base_url}/api/v1/investment/status/{task_id}"
                    
                    async with session.get(status_url) as status_response:
                        if status_response.status == 200:
                            status_result = await status_response.json()
                            print(f"✅ 任务状态: {status_result.get('status')}")
                        else:
                            print(f"❌ 状态查询失败: {status_response.status}")
                    
                    # 3. 测试SSE流（简单测试，不完整监听）
                    print("3️⃣ 测试SSE连接...")
                    stream_url = f"{base_url}/api/v1/investment/stream/{task_id}"
                    
                    try:
                        async with session.get(stream_url) as stream_response:
                            if stream_response.status == 200:
                                print("✅ SSE连接建立成功")
                                
                                # 读取前几个事件
                                event_count = 0
                                async for line in stream_response.content:
                                    if line:
                                        line_str = line.decode('utf-8').strip()
                                        if line_str.startswith('data:'):
                                            event_count += 1
                                            print(f"📡 收到事件 {event_count}: {line_str[:100]}...")
                                            
                                            if event_count >= 3:  # 只读取前3个事件
                                                break
                            else:
                                print(f"❌ SSE连接失败: {stream_response.status}")
                    except Exception as e:
                        print(f"⚠️  SSE测试异常: {e}")
                        
                else:
                    print(f"❌ 任务提交失败: {response.status}")
                    error_text = await response.text()
                    print(f"错误信息: {error_text}")
                    
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 API测试完成")

async def test_health_check():
    """测试健康检查端点"""
    
    print("🔍 测试健康检查...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get("http://localhost:8000/api/v1/investment/health") as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 健康检查通过: {result.get('message')}")
                else:
                    print(f"❌ 健康检查失败: {response.status}")
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")

async def main():
    """主测试函数"""
    print("🚀 投资洞察API测试工具")
    print("确保后端服务已启动在 http://localhost:8000")
    print()
    
    # 等待用户确认
    input("按回车键开始测试...")
    
    await test_health_check()
    print()
    await test_investment_api()

if __name__ == "__main__":
    asyncio.run(main()) 