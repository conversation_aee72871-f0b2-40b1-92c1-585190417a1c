"""
事件驱动投资分析图的节点函数
"""

import os
import importlib
from datetime import datetime
from typing import List, Dict, Any,Optional
from langchain_core.messages import AIMessage, HumanMessage
from langgraph.types import Send,Command, interrupt
from langchain_core.runnables import RunnableConfig
from langchain_core.output_parsers import PydanticOutputParser
from shared_bs_llm.llm import LLMClientUtils

from ..tools_and_schemas import QuestionList
from ..state import (
    InvestmentAnalysisState
)
from ..configuration import VestConfiguration
from ..promotes.node_promots import NODE_PROMPT_MAP
from ..constants import NodeNames  # Import from constants instead
from ..search import VestSearch
from .node_config import get_node_config, NodeConfig, should_continue_loop
from research_v2b_bs.api.adapter.research_events import ResearchEventEmitter, ResearchStage, ResearchEventType


def get_event_emitter() -> ResearchEventEmitter:
    """获取事件发射器实例（单例模式，永远不会返回 None）"""
    return ResearchEventEmitter.get_instance()
# 在 nodes_function.py 中添加阶段事件管理函数

def get_stage_for_node(node_name: str) -> ResearchStage:
    """根据节点名称获取对应的研究阶段"""
    try:
        node_config = get_node_config(node_name)
        return node_config.stage
    except ValueError:
        return ResearchStage.IDLE

def should_emit_stage_started(state: InvestmentAnalysisState, current_node: str) -> bool:
    """判断是否应该发送阶段开始事件"""
    current_stage = get_stage_for_node(current_node)
    last_stage = state.get("current_research_stage")
    if last_stage is None:
        return True
    # 如果阶段发生变化，需要发送阶段开始事件
    return last_stage != current_stage.value

def should_emit_stage_completed(state: InvestmentAnalysisState, next_node: str) -> bool:
    """判断是否应该发送阶段完成事件"""
    current_stage_value = state.get("current_research_stage")
    if not current_stage_value:
        return False
        
    next_stage = get_stage_for_node(next_node)
    return current_stage_value != next_stage.value

async def emit_stage_events_if_needed(state: InvestmentAnalysisState, current_node: str, next_node: str = None):
    """根据节点切换情况发送阶段事件"""
    emitter = get_event_emitter()
    
    # 检查是否需要发送阶段开始事件
    if should_emit_stage_started(state, current_node):
        current_stage = get_stage_for_node(current_node)
        stage_info = get_stage_info(current_stage)
        
        yield emitter.emit_stage_started(
            stage=current_stage.value,
            stage_name=stage_info["name"],
            description=stage_info["description"],
            steps=stage_info["steps"]
        )
        # 更新状态中的当前阶段
    # 检查是否需要发送阶段完成事件
    if (state["stage_events_sent"].get("need_send_stage_complete", False) and 
        state.get("pending_stage_complete_event")):
        
        event_data = state["pending_stage_complete_event"]
        yield emitter.emit_stage_completed(
            stage=event_data["stage"],
            result=event_data["result"]
        )
        
        # 发送完成后清空
        state["pending_stage_complete_event"] = None
        state["stage_events_sent"]["need_send_stage_complete"] = False

def get_stage_info(stage: ResearchStage) -> Dict[str, Any]:
    """获取阶段信息"""
    stage_info_map = {
        ResearchStage.FACT_VERIFICATION: {
            "name": "事实核查验证",
            "description": "通过真理之眼系统进行事实核查和验证",
            "steps": ["问题生成", "初步分析", "深度验证", "事实合成"]
        },
        ResearchStage.IMPACT_SIMULATION: {
            "name": "影响模拟分析", 
            "description": "通过卡桑德拉系统进行影响预测和模拟",
            "steps": ["预测需求构建", "影响假设生成", "深度模拟", "影响合成"]
        },
        ResearchStage.THESIS_GENERATION: {
            "name": "投资论文生成",
            "description": "通过阿尔法系统生成投资建议和论文", 
            "steps": ["筛选因子构建", "候选名单生成", "深度分析", "最终验证"]
        }
    }
    return stage_info_map.get(stage, {"name": "未知阶段", "description": "", "steps": []})

def get_stage_result(stage: str,report: str) -> Dict[str, Any]:
    """获取阶段结果"""
    if stage == ResearchStage.FACT_VERIFICATION.value:
        return {
            "summary": report,
            "details": "事实核查完成",
            "confidence_score": 0.85
        }
    elif stage == ResearchStage.IMPACT_SIMULATION.value:
        return {
            "summary": report, 
            "details": "影响模拟完成",
            "confidence_score": 0.80
        }
    elif stage == ResearchStage.THESIS_GENERATION.value:
        return {
            "summary": report,
            "details": "投资论文生成完成",
            "confidence_score": 0.90
        }
    return {"summary": "阶段完成", "details": {}, "confidence_score": 0.0}    
def build_prompt_params(state, node_name, extra_data=None):
    """根据节点配置动态构建提示词参数"""
    params = {}
    
    try:
        # 使用新的 get_node_config 函数获取节点配置
        node_config = get_node_config(node_name)
    except ValueError:
        # 如果没有配置，返回基础参数
        return {"profile": state.get("profile", "")}
    
    # 从 param_mapping 中动态构建参数
    param_mapping = node_config.param_mapping or {}
    
    for param_name, state_key in param_mapping.items():
        if state_key == "search_results_summary" and extra_data:
            # 特殊处理：如果是搜索结果摘要且有额外数据，使用额外数据
            params[param_name] = extra_data
        else:
            # 从状态中获取对应的值
            params[param_name] = state.get(state_key, "")
    
    return params
# 修改 generate_fact_questions 函数中的事件发送
async def generate_fact_questions(
    state: InvestmentAnalysisState, config: RunnableConfig, nodeName: str
):
    """生成事实核查问题"""
    configurable = VestConfiguration.from_runnable_config(config)
    emitter = get_event_emitter()
    
    try:
        node_config = get_node_config(nodeName)
        current_stage = get_stage_for_node(nodeName)
        yield {"current_research_stage": current_stage.value}
    except ValueError as e:
        raise ValueError(f"Unknown node name: {nodeName}") from e

    
    # 发送阶段事件（如果需要）
    async for stage_event in emit_stage_events_if_needed(state, nodeName):
        yield stage_event
    
    # 发射步骤开始事件 - 使用正确的阶段值
    if emitter:
        yield emitter.emit_step_started(
            step_name=node_config.step_name,
            description=f"开始{node_config.step_name}...",
            stage=node_config.stage.value  # 使用配置中的阶段值
        )
    # 创建PydanticOutputParser
    parser = PydanticOutputParser(pydantic_object=QuestionList)
    
    llm = LLMClientUtils.create_llm(
        model_type=configurable.question_generation_model,
        temperature=0.7,
        streaming=False
    )

    # 动态导入提示词模块
    module_name = f"research_v2b_bs.api.demo.promotes.{node_config.prompt_module}"
    prompt_module = importlib.import_module(module_name)
    
    # 使用动态参数映射
    params = build_prompt_params(state, nodeName)
    
    # 在prompt中添加格式说明
    formatted_prompt = prompt_module.PROMPT.format(**params) + "\n\n" + parser.get_format_instructions()
    
    try:
        # 调用LLM获取响应
        response = await llm.ainvoke(formatted_prompt)
        result = parser.parse(response.content)
        
        # 构建问题卡片数据
        questions_data = [
            {
                "id": f"{node_config.category}_{i+1}",
                "title": f"{node_config.category} {i+1}",
                "content": question,
                "type": "fact_check",
                "status": "generated",
                "category": node_config.category or "事实核查",
                "icon": node_config.icon or "🔍"
            }
            for i, question in enumerate(result.questions)
        ]
        
        
    # 发射步骤完成事件 - 使用正确的阶段值
        if emitter:
            yield emitter.emit_step_finished(
                step_name=node_config.step_name,
                description=f"成功生成 {len(result.questions)} 个{node_config.category}问题",
                result_summary=f"生成了 {len(result.questions)} 个问题",
                stage=node_config.stage.value  # 使用配置中的阶段值
            )
        
        yield {
            "fact_check_questions":result.questions,
        }
        
    except Exception as e:
        # 发射步骤失败事件 - 改为 yield
        if emitter:
            yield emitter.emit_step_finished(
                step_name=node_config.step_name,
                description="问题生成失败",
                error=str(e),
                stage=node_config.stage.value  # 使用配置中的阶段值
            )
        raise

# 修改 initial_analysis 函数中的事件发送
async def initial_analysis(
    state: InvestmentAnalysisState, config: RunnableConfig, nodeName: str  
):
    """初步事实分析 - 先汇总搜索结果再生成分析报告"""
    configurable = VestConfiguration.from_runnable_config(config)
    emitter = get_event_emitter()
    
    try:
        # 使用新的 get_node_config 函数
        node_config = get_node_config(nodeName)
        current_stage = get_stage_for_node(nodeName)
        yield {"current_research_stage": current_stage.value}
    except ValueError as e:
        raise ValueError(f"Unknown node name: {nodeName}") from e
    
    # 动态导入提示词模块
    module_name = f"research_v2b_bs.api.demo.promotes.{node_config.prompt_module}"
    prompt_module = importlib.import_module(module_name)
    
    current_count = state.get("initial_analysis_results_count", 0)
    if current_count >= configurable.max_quantitative_loops:
        yield {"initial_analysis_results_count": current_count}
    
    # 检查节点配置中是否有 loop_count_key
    if node_config.loop_count_key:
        loop_count_key = node_config.loop_count_key
        current_count = state.get(loop_count_key, 0)
        # 使用 yield 更新状态
        yield {loop_count_key: current_count + 1}
 
    # 发送步骤开始事件 - 使用正确的阶段值
    if emitter:
        yield emitter.emit_step_started(
            step_name=node_config.step_name,
            description="开始分析 - 收集和汇总信息...",
            stage=node_config.stage.value  # 使用配置中的阶段值
        )
    
    search = VestSearch(configurable)
    questions = state.get("fact_check_questions", [])
    
    # 第一阶段：收集所有搜索结果
    all_search_results = []
    search_summary = []
    
    for i, question in enumerate(questions[:5]):
        try:     
            search_result = await search.search(question)
            all_search_results.append({
                "question": question,
                "search_content": search_result.get('search_content', ''),
                "sources": search_result.get('sources_gathered', [])
            })
            
            # 为每个问题创建简要总结
            search_summary.append(f"**问题**: {question}\n**搜索结果**: {search_result.get('search_content', '')[:96000]}...\n")
            
        except Exception as e:
            if configurable.enable_debug:
                print(f"搜索异常 - 问题: '{question}', 错误: {str(e)}")
            all_search_results.append({
                "question": question,
                "search_content": f"搜索失败: {str(e)}",
                "sources": []
            })
    
    # 第二阶段：汇总所有信息并生成分析报告
    try:
        llm = LLMClientUtils.create_llm(
            model_type=configurable.analysis_model,
            temperature=0.3,
            streaming=True
        )
        
        # 构建汇总的信息收集结果
        consolidated_search_results = "\n\n".join([
            f"**问题 {i+1}**: {result['question']}\n**收集信息**: {result['search_content']}"
            for i, result in enumerate(all_search_results)
        ])
        
        # 使用动态参数映射
        params = build_prompt_params(state, nodeName, consolidated_search_results)
        
        # 格式化提示词
        formatted_prompt = prompt_module.PROMPT.format(**params)
        
        # 生成初步事实分析报告
        analysis_response = await llm.ainvoke(formatted_prompt)
    
        # 发射步骤完成事件 - 改为 yield
        if emitter:
            yield emitter.emit_step_finished(
                step_name=node_config.step_name,
                description=f"完成初步事实分析，生成了 {len(analysis_response.content)} 字符的分析报告",
                result_summary=f"基于 {len(all_search_results)} 个问题的搜索结果生成了初步事实分析报告"
            )
            # 发送阶段事件（如果需要）
    
        # 在分析完成后，准备阶段完成事件数据（但不发送）
        current_stage = node_config.stage
        stage_result = get_stage_result(current_stage.value, analysis_response.content)
        # 根据节点类型返回不同的状态键
        result_key = node_config.output_key
        
        # 返回结果
        yield {
            result_key: [analysis_response.content],
            "search_results_summary": consolidated_search_results,
            "initial_analysis_results": [analysis_response.content],
            "all_search_results": all_search_results,
            "initial_analysis_results_count": current_count + 1,
            "pending_stage_complete_event":{"stage": current_stage.value,"result": stage_result}
        }
        
    except Exception as e:
        # 发射步骤失败事件 - 改为 yield
        if emitter:
            yield emitter.emit_step_finished(
                step_name=node_config.step_name,
                description="初步事实分析失败",
                error=str(e)
            )
        if configurable.enable_debug:
            print(f"初步事实分析异常: {str(e)}")
        raise
# 路由函数
def should_continue_macro_loop(
    state: InvestmentAnalysisState, config: RunnableConfig
) -> str:
    """判断是否继续宏观分析循环"""
    configurable = VestConfiguration.from_runnable_config(config)
    current_count = state.get("macro_loop_count", 0)
    
    if current_count >= configurable.max_macro_loops:
        return NodeNames.CASSANDRA_GENERATE_FACT_QUESTIONS
    else:
        return NodeNames.TRUTH_MACRO_ANALYSIS_LOOP


def should_continue_truth_loop(state: InvestmentAnalysisState) -> str:
    """判断是否继续真理之眼循环"""
    current_count = state.get("initial_analysis_results_count", 0)
    max_iterations = 1
    
    if current_count < max_iterations:
        return "continue"
    else:
        return "exit"

def should_continue_industry_loop(state: InvestmentAnalysisState) -> str:
    """判断是否继续真理之眼循环"""
    current_count = state.get("cassandra_loop_count", 0)
    max_iterations = 1
    
    if current_count < max_iterations:
        return "continue"
    else:
        return "exit"





def should_continue_alpha_loop(state: InvestmentAnalysisState) -> str:
    """判断是否继续真理之眼循环"""
    current_count = state.get("alpha_loop_count", 0)
    max_iterations = 1
    
    if current_count < max_iterations:
        return "continue"
    else:
        # 标记需要发送阶段完成事件
        if "stage_events_sent" not in state:
            state["stage_events_sent"] = {}
        state["stage_events_sent"]["need_send_stage_complete"] = True
        return "exit"

async def human_feedback(state: InvestmentAnalysisState):
    if (state.get("pending_stage_complete_event")):
        event_data = state["pending_stage_complete_event"]
        emitter = get_event_emitter()
        yield emitter.emit_stage_completed(
            stage=event_data["stage"],
            result=event_data["result"]
        )
        # 发送完成后清空
        state["pending_stage_complete_event"] = None
    
    feedback = interrupt("请补充信息:")
    yield {"user_feedback": feedback}

    
def should_use_cassandra_analysis(user_feedback: str) -> bool:
    """根据用户反馈判断是否使用卡桑德拉分析路径"""
    if not user_feedback:
        return True  # 默认使用卡桑德拉路径
    
    # 检查反馈中是否包含影响预测相关的关键词
    impact_keywords = ['影响', '预测', '模拟', '趋势', '未来', '变化', '后果', '效应']
    feedback_lower = user_feedback.lower()
    
    # 如果包含影响预测相关关键词，使用卡桑德拉路径
    for keyword in impact_keywords:
        if keyword in feedback_lower:
            return True
    
    # 否则使用阿尔法路径
    return False

def decide_next_after_human_feedback(state):
    """根据用户反馈决定下一个节点"""
    user_feedback = state.get('user_feedback', '')
    current_stage = state.get('current_stage', '')
    
    if current_stage == 'fact-verification':
        # 根据反馈内容或其他条件决定走哪个分析路径
        if should_use_cassandra_analysis(user_feedback):
            return "CASSANDRA_GENERATE_FACT_QUESTIONS"
        else:
            return "ALPHA_GENERATE_FACT_QUESTIONS"
    
    return "CASSANDRA_GENERATE_FACT_QUESTIONS"  # 默认路径