"""API v1路由模块"""
from fastapi import APIRouter
from .endpoints.chat import router as chat_router
from .endpoints.research import router as research_router
from .endpoints.health import router as health_router
from .endpoints.fact_check import router as fact_check_router
from .endpoints.agui import router as agui_router
from .endpoints.investment_insight import router as investment_router

# 创建v1路由器，设置前缀
v1_router = APIRouter(prefix="/v1")

# 注册各个端点路由
v1_router.include_router(chat_router)
v1_router.include_router(research_router)
v1_router.include_router(health_router)
v1_router.include_router(fact_check_router)
v1_router.include_router(agui_router)
v1_router.include_router(investment_router)

__all__ = ["v1_router"]