"""
投资洞察API端点 - 支持实时流式分析
"""
import json
import asyncio
from typing import Dict, Any, AsyncGenerator
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import StreamingResponse
from sse_starlette.sse import EventSourceResponse
from celery.result import AsyncResult
from pydantic import BaseModel, Field

from ....infrastructure.tasks.investment_tasks import run_investment_analysis
from ....infrastructure.services.logging import get_logger

router = APIRouter(prefix="/investment", tags=["investment-insight"])
logger = get_logger(__name__)


class InvestmentAnalysisRequest(BaseModel):
    """投资分析请求模型"""
    query: str = Field(..., description="投资查询内容", min_length=1, max_length=1000)
    task_id: str = Field(None, description="任务ID（可选）")
    thread_id: str = Field(None, description="线程ID（可选）")


class TaskSubmissionResponse(BaseModel):
    """任务提交响应模型"""
    task_id: str = Field(..., description="任务ID")
    thread_id: str = Field(..., description="线程ID")
    status: str = Field(..., description="任务状态")
    message: str = Field(..., description="响应消息")


@router.post("/submit", response_model=TaskSubmissionResponse)
async def submit_investment_analysis(request: InvestmentAnalysisRequest):
    """
    提交投资分析任务
    
    Args:
        request: 投资分析请求
        
    Returns:
        TaskSubmissionResponse: 任务提交响应
    """
    try:
        logger.info(f"Submitting investment analysis task: {request.query[:100]}...")
        
        # 提交Celery任务
        task = run_investment_analysis.delay(
            prompt=request.query,
            task_id=request.task_id,
            thread_id=request.thread_id
        )
        
        logger.info(f"Investment analysis task submitted: {task.id}")
        
        return TaskSubmissionResponse(
            task_id=task.id,
            thread_id=request.thread_id or f"thread_{task.id}",
            status="submitted",
            message="投资分析任务已提交，请通过SSE端点获取实时结果"
        )
        
    except Exception as e:
        logger.error(f"Failed to submit investment analysis task: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"提交投资分析任务失败: {str(e)}"
        )


@router.get("/stream/{task_id}")
async def investment_analysis_stream(task_id: str):
    """
    投资分析实时流式端点
    
    Args:
        task_id: 任务ID
        
    Returns:
        EventSourceResponse: SSE流式响应
    """
    
    async def event_generator() -> AsyncGenerator[Dict[str, Any], None]:
        """事件生成器"""
        try:
            logger.info(f"Starting SSE stream for task: {task_id}")
            
            # 获取任务结果
            task_result = AsyncResult(task_id)
            
            # 发送连接建立事件
            yield {
                "event": "connected",
                "data": json.dumps({
                    "task_id": task_id,
                    "status": "connected",
                    "message": "SSE连接已建立"
                })
            }
            
            # 轮询任务状态
            while not task_result.ready():
                # 检查任务状态
                if task_result.state == 'PENDING':
                    yield {
                        "event": "status",
                        "data": json.dumps({
                            "task_id": task_id,
                            "status": "pending",
                            "message": "任务等待执行中..."
                        })
                    }
                elif task_result.state == 'PROGRESS':
                    # 获取进度信息
                    progress_info = task_result.info or {}
                    yield {
                        "event": "progress",
                        "data": json.dumps({
                            "task_id": task_id,
                            "status": "running",
                            "progress": progress_info
                        })
                    }
                
                # 等待一段时间再检查
                await asyncio.sleep(0.5)
            
            # 任务完成，获取结果
            if task_result.successful():
                result = task_result.result
                
                # 发送所有存储的事件
                if 'events' in result:
                    for event in result['events']:
                        yield {
                            "event": event['type'],
                            "data": json.dumps(event['data'])
                        }
                
                # 发送最终完成事件
                yield {
                    "event": "completed",
                    "data": json.dumps({
                        "task_id": task_id,
                        "status": "completed",
                        "final_report": result.get('final_report', ''),
                        "analysis_results": result.get('analysis_results', {}),
                        "message": "投资分析完成"
                    })
                }
                
            else:
                # 任务失败
                error_info = str(task_result.info) if task_result.info else "未知错误"
                yield {
                    "event": "error",
                    "data": json.dumps({
                        "task_id": task_id,
                        "status": "failed",
                        "error": error_info,
                        "message": "投资分析失败"
                    })
                }
                
        except Exception as e:
            logger.error(f"SSE stream error for task {task_id}: {str(e)}")
            yield {
                "event": "error",
                "data": json.dumps({
                    "task_id": task_id,
                    "status": "error",
                    "error": str(e),
                    "message": "流式传输错误"
                })
            }
    
    return EventSourceResponse(event_generator())


@router.get("/status/{task_id}")
async def get_task_status(task_id: str):
    """
    获取任务状态
    
    Args:
        task_id: 任务ID
        
    Returns:
        Dict: 任务状态信息
    """
    try:
        task_result = AsyncResult(task_id)
        
        response = {
            "task_id": task_id,
            "status": task_result.state,
            "ready": task_result.ready()
        }
        
        if task_result.ready():
            if task_result.successful():
                response["result"] = task_result.result
            else:
                response["error"] = str(task_result.info)
        else:
            response["info"] = task_result.info
            
        return response
        
    except Exception as e:
        logger.error(f"Failed to get task status for {task_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取任务状态失败: {str(e)}"
        )


@router.delete("/cancel/{task_id}")
async def cancel_task(task_id: str):
    """
    取消任务
    
    Args:
        task_id: 任务ID
        
    Returns:
        Dict: 取消结果
    """
    try:
        task_result = AsyncResult(task_id)
        task_result.revoke(terminate=True)
        
        logger.info(f"Task {task_id} cancelled")
        
        return {
            "task_id": task_id,
            "status": "cancelled",
            "message": "任务已取消"
        }
        
    except Exception as e:
        logger.error(f"Failed to cancel task {task_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"取消任务失败: {str(e)}"
        )


@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "investment-insight",
        "message": "投资洞察服务运行正常"
    } 