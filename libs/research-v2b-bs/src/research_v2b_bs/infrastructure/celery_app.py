"""
Celery应用配置
"""
import os
from celery import Celery

# 创建Celery应用实例
app = Celery(
    "research_agent_tasks",
    broker=os.getenv("REDIS_URL", "redis://localhost:6379/0"),
    backend=os.getenv("REDIS_URL", "redis://localhost:6379/0"),
)

# Celery配置
app.conf.update(
    # 任务结果保留时间（秒）
    result_expires=3600,
    # 任务序列化
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    # 工作进程配置
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    # 事件配置
    worker_send_task_events=True,
    task_send_sent_event=True,
)

# 自动发现任务
app.autodiscover_tasks(['research_v2b_bs.infrastructure.tasks']) 