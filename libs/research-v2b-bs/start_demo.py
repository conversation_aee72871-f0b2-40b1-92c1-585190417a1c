#!/usr/bin/env python3
"""
投资洞察 Demo 启动脚本
自动启动所有必需的服务
"""

import os
import sys
import time
import signal
import subprocess
import threading
from pathlib import Path

class DemoLauncher:
    def __init__(self):
        self.processes = []
        self.base_dir = Path(__file__).parent
        
    def check_redis(self):
        """检查Redis是否运行"""
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.ping()
            print("✅ Redis 连接正常")
            return True
        except Exception as e:
            print(f"❌ Redis 连接失败: {e}")
            print("请确保 Redis 服务器正在运行:")
            print("  macOS: brew services start redis")
            print("  Ubuntu: sudo systemctl start redis-server")
            print("  Docker: docker run -d -p 6379:6379 redis:alpine")
            return False
    
    def check_dependencies(self):
        """检查Python依赖"""
        required_packages = [
            'fastapi', 'uvicorn', 'celery', 'redis', 
            'sse_starlette', 'pydantic'
        ]
        
        missing = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing.append(package)
        
        if missing:
            print(f"❌ 缺少依赖包: {', '.join(missing)}")
            print("请运行: pip install " + " ".join(missing))
            return False
        
        print("✅ Python 依赖检查通过")
        return True
    
    def start_celery_worker(self):
        """启动Celery Worker"""
        print("🚀 启动 Celery Worker...")
        
        cmd = [
            sys.executable, '-m', 'celery',
            '-A', 'src.research_v2b_bs.infrastructure.celery_app',
            'worker',
            '--loglevel=info',
            '--pool=threads',
            '--concurrency=2'
        ]
        
        process = subprocess.Popen(
            cmd,
            cwd=self.base_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        self.processes.append(('Celery Worker', process))
        
        # 启动日志读取线程
        def log_reader():
            for line in process.stdout:
                print(f"[Celery] {line.strip()}")
        
        threading.Thread(target=log_reader, daemon=True).start()
        
        # 等待worker启动
        time.sleep(3)
        return process
    
    def start_fastapi_server(self):
        """启动FastAPI服务器"""
        print("🚀 启动 FastAPI 服务器...")
        
        # 创建临时的main.py文件
        main_py_content = '''
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from src.research_v2b_bs.api.v1 import v1_router

app = FastAPI(
    title="投资洞察 AI 分析平台",
    description="基于 LangGraph + Celery + SSE 的实时投资分析系统",
    version="1.0.0"
)

# 注册API路由
app.include_router(v1_router, prefix="/api")

# 静态文件服务
@app.get("/demo.html")
async def serve_demo():
    """提供演示页面"""
    return FileResponse("demo.html")

@app.get("/")
async def root():
    """根路径重定向到演示页面"""
    return FileResponse("demo.html")

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "message": "投资洞察服务运行正常"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)
'''
        
        main_py_path = self.base_dir / "main_demo.py"
        with open(main_py_path, 'w', encoding='utf-8') as f:
            f.write(main_py_content)
        
        cmd = [
            sys.executable, '-m', 'uvicorn',
            'main_demo:app',
            '--host', '0.0.0.0',
            '--port', '8000',
            '--reload'
        ]
        
        process = subprocess.Popen(
            cmd,
            cwd=self.base_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        self.processes.append(('FastAPI Server', process))
        
        # 启动日志读取线程
        def log_reader():
            for line in process.stdout:
                print(f"[FastAPI] {line.strip()}")
        
        threading.Thread(target=log_reader, daemon=True).start()
        
        # 等待服务器启动
        time.sleep(2)
        return process
    
    def cleanup(self):
        """清理所有进程"""
        print("\n🛑 正在停止所有服务...")
        
        for name, process in self.processes:
            if process.poll() is None:  # 进程仍在运行
                print(f"停止 {name}...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
        
        # 清理临时文件
        temp_file = self.base_dir / "main_demo.py"
        if temp_file.exists():
            temp_file.unlink()
        
        print("✅ 所有服务已停止")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.cleanup()
        sys.exit(0)
    
    def run(self):
        """运行demo"""
        print("🚀 投资洞察 AI 分析平台 Demo 启动器")
        print("=" * 50)
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            # 检查依赖
            if not self.check_dependencies():
                return
            
            if not self.check_redis():
                return
            
            # 启动服务
            celery_process = self.start_celery_worker()
            fastapi_process = self.start_fastapi_server()
            
            print("\n" + "=" * 50)
            print("🎉 后端服务启动成功！")
            print("\n📱 访问地址:")
            print("  - API 文档: http://localhost:8000/docs")
            print("  - 健康检查: http://localhost:8000/health")
            print("  - 旧版重定向页面: http://localhost:8000/demo.html")
            print("\n🚀 推荐访问方式（现代化React页面）:")
            print("  1. 新开终端，启动前端应用：")
            print("     cd ../../apps/web-app && npm run dev")
            print("  2. 访问：http://localhost:3000/investment-insight")
            print("\n💡 使用说明:")
            print("  1. 在React页面中输入投资查询，如 '分析NVIDIA的投资价值'")
            print("  2. 点击'开始分析'观看实时分析过程")
            print("  3. 按 Ctrl+C 停止所有服务")
            print("\n" + "=" * 50)
            
            # 等待进程
            try:
                while True:
                    # 检查进程状态
                    all_running = True
                    for name, process in self.processes:
                        if process.poll() is not None:
                            print(f"⚠️  {name} 意外停止")
                            all_running = False
                    
                    if not all_running:
                        break
                    
                    time.sleep(1)
            
            except KeyboardInterrupt:
                pass
        
        except Exception as e:
            print(f"❌ 启动失败: {e}")
        
        finally:
            self.cleanup()

def main():
    """主函数"""
    launcher = DemoLauncher()
    launcher.run()

if __name__ == "__main__":
    main() 