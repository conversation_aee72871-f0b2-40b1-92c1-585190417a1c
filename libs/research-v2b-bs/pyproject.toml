[build-system]
requires = ["setuptools>=64", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "research-v2b-bs"
version = "0.1.0"
description = "Research V2B Backend Service Plugin (Barry Version)"
authors = [
    {name = "Y-AI Team", email = "<EMAIL>"}
]
dependencies = [
    "shared-bs-llm",
    "typing-extensions>=4.0.0",
    # 核心 Web 框架
    "fastapi>=0.116.1",
    "uvicorn[standard]>=0.35.0",
    "pydantic>=2.11.7",
    "langchain==0.3.26",
    "langchain-community>=0.3.27",
    "langchain-openai>=0.3.28",
    "langchain-anthropic>=0.3.17",
    "langgraph>=0.5.3",
    "langfuse~=3.2.1",
    "ag-ui-protocol>=0.1.0",
    "pydantic-settings>=2.3.0",

    # 日志和监控
    "yai-loguru-sinks>=0.6.3",
    "structlog>=25.4.0",
    "opentelemetry-api>=1.35.0",
    "opentelemetry-sdk>=1.35.0",
    "python-dotenv>=1.0.0",
    "dependency-injector>=4.42.1",
    "pydantic-settings>=2.3.0",
    "passlib>=1.7.4",
    "bcrypt>=4.1.0",
    "PyJWT>=2.10.1",
    "yfinance>=0.2.0",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "httpx>=0.25.0",
    "openai>=1.0.0",
    "anthropic>=0.30.0",
    "celery>=5.5.3",
    "redis>=6.2.0"
]

[project.optional-dependencies]
dev = [
   "shared-bs-llm",
   "typing-extensions>=4.0.0",
    # 核心 Web 框架
    "fastapi>=0.116.1",
    "uvicorn[standard]>=0.35.0",
    "pydantic>=2.11.7",
    "langchain==0.3.26",
    "langchain-community>=0.3.27",
    "langchain-openai>=0.3.28",
    "langchain-anthropic>=0.3.17",
    "langgraph>=0.5.3",
    "langfuse~=3.2.1",
    "ag-ui-protocol>=0.1.0",
    "pydantic-settings>=2.3.0",

    # 日志和监控
    "yai-loguru-sinks>=0.6.3",
    "structlog>=25.4.0",
    "opentelemetry-api>=1.35.0",
    "opentelemetry-sdk>=1.35.0",
    "python-dotenv>=1.0.0",
    "dependency-injector>=4.42.1",
    "pydantic-settings>=2.3.0",
    "passlib>=1.7.4",
    "bcrypt>=4.1.0",
    "PyJWT>=2.10.1",
    "yfinance>=0.2.0",
    "requests>=2.31.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "httpx>=0.25.0",
    "openai>=1.0.0",
    "anthropic>=0.30.0",
    "celery>=5.3.4",
    "redis>=5.0.1"
]

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"


[tool.uv.sources]
shared-bs-llm = { workspace = true }
