# 投资洞察 AI 分析平台 Demo

基于 LangGraph + Celery + SSE 的实时投资分析系统，提供类似页面截图的投资洞察功能。

## 🚀 功能特性

- **实时投资分析**: 基于 LangGraph 的多阶段投资分析流程
- **异步任务处理**: 使用 Celery + Redis 进行后台任务处理
- **实时流式响应**: 通过 SSE (Server-Sent Events) 实时推送分析进度
- **投资洞察界面**: 仿照页面截图的投资想法展示界面
- **多阶段分析**: 事实验证 → 影响模拟 → 投资建议 → 最终报告

## 📋 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   前端界面       │    │   FastAPI 后端    │    │   Celery Worker │
│                 │    │                  │    │                 │
│ - 输入投资查询   │───▶│ - 提交分析任务    │───▶│ - 执行 LangGraph │
│ - 实时进度显示   │◀───│ - SSE 流式响应   │◀───│ - 多阶段分析     │
│ - 结果展示       │    │ - 任务状态管理    │    │ - 事件推送       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Redis 消息队列  │    │  LangGraph 图   │
                       │                  │    │                 │
                       │ - 任务队列        │    │ - 投资分析节点   │
                       │ - 结果存储        │    │ - 事实验证       │
                       │ - 事件缓存        │    │ - 影响模拟       │
                       └──────────────────┘    │ - 投资建议       │
                                              └─────────────────┘
```

## 🛠️ 技术栈

### 后端
- **FastAPI**: Web 框架
- **Celery**: 异步任务队列
- **Redis**: 消息代理和结果存储
- **LangGraph**: AI 工作流编排
- **LangChain**: LLM 集成
- **SSE-Starlette**: 服务器推送事件

### 前端
- **React + TypeScript**: 现代前端框架
- **Tailwind CSS**: 样式框架
- **Lucide React**: 图标库
- **EventSource API**: SSE 客户端

## 📦 安装依赖

### 后端依赖

```bash
cd libs/research-v2b-bs

# 安装 Python 依赖
pip install fastapi uvicorn[standard] celery redis langgraph langchain_openai sse-starlette pydantic dependency-injector python-multipart httpx

# 或使用 poetry
poetry install
```

### 前端依赖

```bash
cd libs/research-v2b-fe

# 安装 Node.js 依赖
npm install
# 或
pnpm install
```

## 🔧 环境配置

### 1. Redis 配置

确保 Redis 服务器在本地运行：

```bash
# macOS (使用 Homebrew)
brew install redis
brew services start redis

# Ubuntu/Debian
sudo apt-get install redis-server
sudo systemctl start redis-server

# Docker
docker run -d -p 6379:6379 redis:alpine
```

### 2. 环境变量

创建 `.env` 文件：

```bash
# Redis 配置
REDIS_URL=redis://localhost:6379/0

# OpenAI 配置 (如果使用真实的 LLM)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# 日志级别
LOG_LEVEL=INFO
```

## 🚀 运行系统

### 1. 启动 Redis

```bash
redis-server
```

### 2. 启动 Celery Worker

```bash
cd libs/research-v2b-bs

# 启动 Celery worker
celery -A src.research_v2b_bs.infrastructure.celery_app worker --loglevel=info --pool=threads
```

### 3. 启动 FastAPI 后端

```bash
cd libs/research-v2b-bs

# 启动 FastAPI 服务器
uvicorn src.research_v2b_bs.adapter.api_router:router --reload --host 0.0.0.0 --port 8000
```

### 4. 启动前端 (可选)

```bash
cd libs/research-v2b-fe

# 启动开发服务器
npm run dev
```

### 5. 访问演示页面

#### 推荐方式（现代化React页面）：
1. 启动前端应用：
   ```bash
   cd apps/web-app
   npm run dev
   ```
2. 访问：`http://localhost:3000/investment-insight`

#### 其他访问方式：
- **API 文档**: `http://localhost:8000/docs`
- **旧版HTML重定向页面**: `http://localhost:8000/demo.html`

## 📱 使用方法

### 1. 基本使用

1. 在输入框中输入投资查询，例如：
   - "分析 NVIDIA 的投资价值"
   - "特斯拉股票的风险评估"
   - "苹果公司的长期投资前景"

2. 点击"开始分析"按钮

3. 系统会实时显示分析进度：
   - 🔍 事实验证阶段
   - 📊 影响模拟阶段  
   - 💡 投资建议阶段
   - 📋 最终报告阶段

4. 分析完成后查看详细的投资分析报告

### 2. API 使用

#### 提交分析任务

```bash
curl -X POST "http://localhost:8000/api/v1/investment/submit" \
     -H "Content-Type: application/json" \
     -d '{"query": "分析NVIDIA的投资价值"}'
```

#### 获取实时分析结果

```bash
curl -N "http://localhost:8000/api/v1/investment/stream/{task_id}"
```

#### 查询任务状态

```bash
curl "http://localhost:8000/api/v1/investment/status/{task_id}"
```

## 🎯 分析流程

系统执行以下四个阶段的投资分析：

### 1. 事实验证阶段 (Fact Verification)
- 验证市场数据的准确性
- 检查财务指标的可靠性
- 确认信息来源的权威性

### 2. 影响模拟阶段 (Impact Simulation)  
- 构建多种投资情景
- 计算预期收益率
- 识别主要风险因素

### 3. 投资建议阶段 (Thesis Recommendation)
- 生成具体投资建议
- 确定目标配置比例
- 制定投资时间框架

### 4. 最终报告阶段 (Final Report)
- 综合所有分析结果
- 生成执行摘要
- 提供行动建议

## 🔍 监控和调试

### 查看 Celery 任务状态

```bash
# 查看活跃任务
celery -A src.research_v2b_bs.infrastructure.celery_app inspect active

# 查看已注册任务
celery -A src.research_v2b_bs.infrastructure.celery_app inspect registered

# 查看任务统计
celery -A src.research_v2b_bs.infrastructure.celery_app inspect stats
```

### 查看 Redis 数据

```bash
# 连接到 Redis
redis-cli

# 查看所有键
KEYS *

# 查看任务结果
GET celery-task-meta-{task_id}
```

## 🐛 故障排除

### 常见问题

1. **Redis 连接失败**
   ```
   解决方案：确保 Redis 服务器正在运行，检查连接配置
   ```

2. **Celery Worker 启动失败**
   ```
   解决方案：检查 Python 路径和依赖安装，确保 Redis 可访问
   ```

3. **SSE 连接中断**
   ```
   解决方案：检查网络连接，确保防火墙允许长连接
   ```

4. **分析任务超时**
   ```
   解决方案：增加 Celery 任务超时时间，检查 LLM API 配置
   ```

### 日志查看

```bash
# Celery 日志
tail -f celery.log

# FastAPI 日志
tail -f uvicorn.log

# Redis 日志
tail -f /var/log/redis/redis-server.log
```

## 🔧 自定义配置

### 修改分析阶段

编辑 `libs/research-v2b-bs/src/research_v2b_bs/infrastructure/tasks/investment_tasks.py`:

```python
# 自定义分析阶段
stages = [
    ("custom_stage_1", "自定义阶段1"),
    ("custom_stage_2", "自定义阶段2"),
    # 添加更多阶段...
]
```

### 集成真实 LLM

更新 `investment_tasks.py` 中的 `_simulate_stage_analysis` 方法，调用真实的 LangGraph：

```python
# 替换模拟分析为真实 LangGraph 调用
result = await graph.astream(initial_state, config=config)
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请提交 Issue 或联系开发团队。